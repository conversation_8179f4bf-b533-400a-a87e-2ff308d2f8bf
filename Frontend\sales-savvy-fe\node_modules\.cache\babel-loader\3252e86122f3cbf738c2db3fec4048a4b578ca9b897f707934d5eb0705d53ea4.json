{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\Navbar\\\\CustomerNavbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './CustomerNavbar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CustomerNavbar() {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    const confirmed = window.confirm(\"Are you sure you want to logout?\");\n    if (confirmed) {\n      localStorage.removeItem('user');\n      sessionStorage.clear();\n      navigate('/');\n    }\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"customer-navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-logo\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\uD83D\\uDED2 \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"Sales Savvy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 26\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"user-badge\",\n        children: \"Customer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `navbar-menu ${isMenuOpen ? 'active' : ''}`,\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"navbar-links\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-item\",\n            children: \"\\uD83C\\uDFE0 Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-item\",\n            children: \"\\uD83D\\uDED2 Cart (0)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-item\",\n            children: \"\\uD83D\\uDCE6 My Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"logout-btn\",\n            onClick: handleLogout,\n            children: \"\\uD83D\\uDEAA Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-toggle\",\n      onClick: toggleMenu,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 9\n  }, this);\n}\n_s(CustomerNavbar, \"5bDz1P/CDiQaTeCGRGLL1agRZDk=\", false, function () {\n  return [useNavigate];\n});\n_c = CustomerNavbar;\nvar _c;\n$RefreshReg$(_c, \"CustomerNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "CustomerNavbar", "_s", "isMenuOpen", "setIsMenuOpen", "navigate", "handleLogout", "confirmed", "window", "confirm", "localStorage", "removeItem", "sessionStorage", "clear", "toggleMenu", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/Navbar/CustomerNavbar.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './CustomerNavbar.css';\n\nexport default function CustomerNavbar() {\n    const [isMenuOpen, setIsMenuOpen] = useState(false);\n    const navigate = useNavigate();\n\n    const handleLogout = () => {\n        const confirmed = window.confirm(\"Are you sure you want to logout?\");\n        if (confirmed) {\n            localStorage.removeItem('user');\n            sessionStorage.clear();\n            navigate('/');\n        }\n    };\n\n    const toggleMenu = () => {\n        setIsMenuOpen(!isMenuOpen);\n    };\n\n    return (\n        <nav className=\"customer-navbar\">\n            <div className=\"navbar-logo\">\n                <span>🛒 <span className=\"logo-text\">Sales Savvy</span></span>\n                <span className=\"user-badge\">Customer</span>\n            </div>\n            \n            <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>\n                <ul className=\"navbar-links\">\n                    <li>\n                        <span className=\"nav-item\">\n                            🏠 Home\n                        </span>\n                    </li>\n                    <li>\n                        <span className=\"nav-item\">\n                            🛒 Cart (0)\n                        </span>\n                    </li>\n                    <li>\n                        <span className=\"nav-item\">\n                            📦 My Orders\n                        </span>\n                    </li>\n                    <li>\n                        <button \n                            className=\"logout-btn\"\n                            onClick={handleLogout}\n                        >\n                            🚪 Logout\n                        </button>\n                    </li>\n                </ul>\n            </div>\n\n            <div className=\"navbar-toggle\" onClick={toggleMenu}>\n                <span></span>\n                <span></span>\n                <span></span>\n            </div>\n        </nav>\n    );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACrC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGP,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMQ,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC;IACpE,IAAIF,SAAS,EAAE;MACXG,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;MAC/BC,cAAc,CAACC,KAAK,CAAC,CAAC;MACtBR,QAAQ,CAAC,GAAG,CAAC;IACjB;EACJ,CAAC;EAED,MAAMS,UAAU,GAAGA,CAAA,KAAM;IACrBV,aAAa,CAAC,CAACD,UAAU,CAAC;EAC9B,CAAC;EAED,oBACIH,OAAA;IAAKe,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC5BhB,OAAA;MAAKe,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBhB,OAAA;QAAAgB,QAAA,GAAM,eAAG,eAAAhB,OAAA;UAAMe,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9DpB,OAAA;QAAMe,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAE,eAAeZ,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAAa,QAAA,eACxDhB,OAAA;QAAIe,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACxBhB,OAAA;UAAAgB,QAAA,eACIhB,OAAA;YAAMe,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAE3B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLpB,OAAA;UAAAgB,QAAA,eACIhB,OAAA;YAAMe,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAE3B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLpB,OAAA;UAAAgB,QAAA,eACIhB,OAAA;YAAMe,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAE3B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLpB,OAAA;UAAAgB,QAAA,eACIhB,OAAA;YACIe,SAAS,EAAC,YAAY;YACtBM,OAAO,EAAEf,YAAa;YAAAU,QAAA,EACzB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENpB,OAAA;MAAKe,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEP,UAAW;MAAAE,QAAA,gBAC/ChB,OAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbpB,OAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbpB,OAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAAClB,EAAA,CA3DuBD,cAAc;EAAA,QAEjBH,WAAW;AAAA;AAAAwB,EAAA,GAFRrB,cAAc;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}