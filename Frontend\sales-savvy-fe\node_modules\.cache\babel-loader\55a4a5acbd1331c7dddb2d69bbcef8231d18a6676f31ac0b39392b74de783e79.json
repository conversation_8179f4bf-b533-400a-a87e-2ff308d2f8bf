{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\AdminPages\\\\AllProducts.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function AllProducts() {\n  _s();\n  const [products, setProducts] = useState([]);\n  const navigate = useNavigate();\n  useEffect(() => {\n    axios.get('http://localhost:8080/getAllProducts').then(response => {\n      setProducts(response.data);\n    }).catch(error => {\n      console.error('Error fetching products:', error);\n    });\n  }, []);\n  // 2. Delete by ID, then refresh\n  const handleDelete = id => {\n    axios.get('http://localhost:8080/deleteProduct', {\n      params: {\n        id\n      }\n    }).then(() => fetchProducts()).catch(err => console.error('Delete failed:', err));\n  };\n\n  // 3. Go to update page with product in state\n  const handleUpdate = product => {\n    navigate('/update_prod_page', {\n      state: {\n        product\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"You are at All Products Page\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          overflowX: 'auto',\n          overflowY: 'auto',\n          height: '500px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          border: 1,\n          cellPadding: 10,\n          cellSpacing: 0,\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Id\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 29\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"b\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 50,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [product.price, \"\\u20B9\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: product.image,\n                  alt: \"product image\",\n                  style: {\n                    width: '100px',\n                    height: '100px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleUpdate(p),\n                  children: \"Update\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 55,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(p.id),\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 34\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 29\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n}\n_s(AllProducts, \"IM33jVfoypN7Irf5pwXOwm+Oe18=\", false, function () {\n  return [useNavigate];\n});\n_c = AllProducts;\nvar _c;\n$RefreshReg$(_c, \"AllProducts\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AllProducts", "_s", "products", "setProducts", "navigate", "get", "then", "response", "data", "catch", "error", "console", "handleDelete", "id", "params", "fetchProducts", "err", "handleUpdate", "product", "state", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "overflowX", "overflowY", "height", "border", "cellPadding", "cellSpacing", "map", "name", "description", "price", "src", "image", "alt", "width", "onClick", "p", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/AdminPages/AllProducts.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useNavigate } from 'react-router-dom';\r\nexport default function AllProducts() {\r\n    const [products, setProducts] = useState([]);\r\n    const navigate = useNavigate();\r\n    useEffect(() => {\r\n        axios.get('http://localhost:8080/getAllProducts')\r\n        .then(response => {\r\n            setProducts(response.data);\r\n        })\r\n        .catch(error => {\r\n            console.error('Error fetching products:', error);\r\n        });\r\n    }, []);\r\n     // 2. Delete by ID, then refresh\r\n  const handleDelete = (id) => {\r\n    axios\r\n      .get('http://localhost:8080/deleteProduct', { params: { id } })\r\n      .then(() => fetchProducts())\r\n      .catch(err => console.error('Delete failed:', err));\r\n  };\r\n\r\n  // 3. Go to update page with product in state\r\n  const handleUpdate = (product) => {\r\n    navigate('/update_prod_page', { state: { product } });\r\n  };\r\n\r\n    return (\r\n        <>\r\n        <div>\r\n            <h3>\r\n                You are at All Products Page\r\n            </h3>\r\n            <div style={{overflowX: 'auto', overflowY: 'auto', height: '500px'}}>\r\n                <table border={1} cellPadding={10} cellSpacing={0}>\r\n                    <thead>\r\n                        <tr>\r\n                            <th>Id</th>\r\n                            <th>Name</th>\r\n                            <th>Description</th>\r\n                            <th>Price</th>\r\n                            <th>Image</th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        {products.map(product => (\r\n                            <tr key={product.id}>\r\n                                <td>{product.id}</td>\r\n                                <td><b>{product.name}</b></td>\r\n                                <td>{product.description}</td>\r\n                                <td>{product.price}₹</td>\r\n                                <td><img src = {product.image} alt=\"product image\" style={{width: '100px', height: '100px'}}/></td>\r\n                                 <td>\r\n                                    <button onClick={() => handleUpdate(p)}>Update</button>\r\n                                    <button onClick={() => handleDelete(p.id)}>Delete</button>\r\n                                </td>\r\n                            </tr>\r\n                        ))}\r\n                    </tbody>\r\n                </table>\r\n                </div>\r\n        </div>\r\n        </>\r\n    )\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC/C,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAMW,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9BH,SAAS,CAAC,MAAM;IACZE,KAAK,CAACW,GAAG,CAAC,sCAAsC,CAAC,CAChDC,IAAI,CAACC,QAAQ,IAAI;MACdJ,WAAW,CAACI,QAAQ,CAACC,IAAI,CAAC;IAC9B,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAI;MACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EACL;EACH,MAAME,YAAY,GAAIC,EAAE,IAAK;IAC3BnB,KAAK,CACFW,GAAG,CAAC,qCAAqC,EAAE;MAAES,MAAM,EAAE;QAAED;MAAG;IAAE,CAAC,CAAC,CAC9DP,IAAI,CAAC,MAAMS,aAAa,CAAC,CAAC,CAAC,CAC3BN,KAAK,CAACO,GAAG,IAAIL,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEM,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAChCd,QAAQ,CAAC,mBAAmB,EAAE;MAAEe,KAAK,EAAE;QAAED;MAAQ;IAAE,CAAC,CAAC;EACvD,CAAC;EAEC,oBACIrB,OAAA,CAAAE,SAAA;IAAAqB,QAAA,eACAvB,OAAA;MAAAuB,QAAA,gBACIvB,OAAA;QAAAuB,QAAA,EAAI;MAEJ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL3B,OAAA;QAAK4B,KAAK,EAAE;UAACC,SAAS,EAAE,MAAM;UAAEC,SAAS,EAAE,MAAM;UAAEC,MAAM,EAAE;QAAO,CAAE;QAAAR,QAAA,eAChEvB,OAAA;UAAOgC,MAAM,EAAE,CAAE;UAACC,WAAW,EAAE,EAAG;UAACC,WAAW,EAAE,CAAE;UAAAX,QAAA,gBAC9CvB,OAAA;YAAAuB,QAAA,eACIvB,OAAA;cAAAuB,QAAA,gBACIvB,OAAA;gBAAAuB,QAAA,EAAI;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACX3B,OAAA;gBAAAuB,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb3B,OAAA;gBAAAuB,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB3B,OAAA;gBAAAuB,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd3B,OAAA;gBAAAuB,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACR3B,OAAA;YAAAuB,QAAA,EACKlB,QAAQ,CAAC8B,GAAG,CAACd,OAAO,iBACjBrB,OAAA;cAAAuB,QAAA,gBACIvB,OAAA;gBAAAuB,QAAA,EAAKF,OAAO,CAACL;cAAE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrB3B,OAAA;gBAAAuB,QAAA,eAAIvB,OAAA;kBAAAuB,QAAA,EAAIF,OAAO,CAACe;gBAAI;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9B3B,OAAA;gBAAAuB,QAAA,EAAKF,OAAO,CAACgB;cAAW;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9B3B,OAAA;gBAAAuB,QAAA,GAAKF,OAAO,CAACiB,KAAK,EAAC,QAAC;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzB3B,OAAA;gBAAAuB,QAAA,eAAIvB,OAAA;kBAAKuC,GAAG,EAAIlB,OAAO,CAACmB,KAAM;kBAACC,GAAG,EAAC,eAAe;kBAACb,KAAK,EAAE;oBAACc,KAAK,EAAE,OAAO;oBAAEX,MAAM,EAAE;kBAAO;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClG3B,OAAA;gBAAAuB,QAAA,gBACGvB,OAAA;kBAAQ2C,OAAO,EAAEA,CAAA,KAAMvB,YAAY,CAACwB,CAAC,CAAE;kBAAArB,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvD3B,OAAA;kBAAQ2C,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAAC6B,CAAC,CAAC5B,EAAE,CAAE;kBAAAO,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA,GATAN,OAAO,CAACL,EAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUf,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC,gBACJ,CAAC;AAEX;AAACvB,EAAA,CA9DuBD,WAAW;EAAA,QAEdL,WAAW;AAAA;AAAA+C,EAAA,GAFR1C,WAAW;AAAA,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}