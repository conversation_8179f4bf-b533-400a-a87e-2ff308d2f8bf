{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\AdminPages\\\\AddProducts.jsx\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport Navbar from '../Navbar/Navbar';\nimport './AddProducts.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function AddProducts() {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    image: ''\n  });\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    axios.post('http://localhost:8080/addProduct', formData).then(response => {\n      alert('Product added successfully!');\n      navigate('/AllProducts');\n    }).catch(error => {\n      console.error('Product addition error:', error);\n      alert('Product addition failed. See console for details.');\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"add-products-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"page-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Add New Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Add a new product to your inventory\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-content-area\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-container\",\n          children: /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"product-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"name\",\n                children: \"Product Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"name\",\n                name: \"name\",\n                placeholder: \"Enter product name\",\n                required: true,\n                onChange: handleChange,\n                value: formData.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"description\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                id: \"description\",\n                name: \"description\",\n                placeholder: \"Enter product description\",\n                required: true,\n                onChange: handleChange,\n                value: formData.description,\n                rows: \"4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"price\",\n                children: \"Price (\\u20B9)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                id: \"price\",\n                name: \"price\",\n                placeholder: \"Enter price\",\n                required: true,\n                onChange: handleChange,\n                value: formData.price,\n                min: \"0\",\n                step: \"0.01\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"image\",\n                children: \"Image URL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                id: \"image\",\n                name: \"image\",\n                placeholder: \"Enter image URL\",\n                required: true,\n                onChange: handleChange,\n                value: formData.image\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                className: \"cancel-btn\",\n                onClick: () => navigate('/AllProducts'),\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"submit-btn\",\n                children: \"Add Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n}\n_s(AddProducts, \"1b6pU0N1CrFxDTMaOooi/mrMl7Q=\", false, function () {\n  return [useNavigate];\n});\n_c = AddProducts;\nvar _c;\n$RefreshReg$(_c, \"AddProducts\");", "map": {"version": 3, "names": ["useState", "axios", "useNavigate", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddProducts", "_s", "navigate", "formData", "setFormData", "name", "description", "price", "image", "handleChange", "e", "value", "target", "prev", "handleSubmit", "preventDefault", "post", "then", "response", "alert", "catch", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onSubmit", "htmlFor", "type", "id", "placeholder", "required", "onChange", "rows", "min", "step", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/AdminPages/AddProducts.jsx"], "sourcesContent": ["import { useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport Navbar from '../Navbar/Navbar';\r\nimport './AddProducts.css';\r\nexport default function AddProducts() {\r\n    const navigate = useNavigate();\r\n    const [formData, setFormData] = useState({\r\n        name: '',\r\n        description: '',\r\n        price: '',\r\n        image: ''\r\n      });\r\n    \r\n      const handleChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setFormData(prev => ({ ...prev, [name]: value }));\r\n      };\r\n    \r\n      const handleSubmit = (e) => {\r\n        e.preventDefault();\r\n    \r\n        axios.post('http://localhost:8080/addProduct', formData)\r\n          .then(response => {\r\n            alert('Product added successfully!');\r\n            navigate('/AllProducts');\r\n          })\r\n          .catch(error => {\r\n            console.error('Product addition error:', error);\r\n            alert('Product addition failed. See console for details.');\r\n          });\r\n      };\r\n      return (\r\n        <>\r\n            <Navbar />\r\n            <div className=\"add-products-container\">\r\n                <div className=\"page-header\">\r\n                    <div className=\"page-header-content\">\r\n                        <h1>Add New Product</h1>\r\n                        <p>Add a new product to your inventory</p>\r\n                    </div>\r\n                </div>\r\n\r\n                <div className=\"main-content-area\">\r\n                    <div className=\"form-container\">\r\n                        <form onSubmit={handleSubmit} className=\"product-form\">\r\n                            <div className=\"form-group\">\r\n                                <label htmlFor=\"name\">Product Name</label>\r\n                                <input\r\n                                    type=\"text\"\r\n                                    id=\"name\"\r\n                                    name=\"name\"\r\n                                    placeholder=\"Enter product name\"\r\n                                    required\r\n                                    onChange={handleChange}\r\n                                    value={formData.name}\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"form-group\">\r\n                                <label htmlFor=\"description\">Description</label>\r\n                                <textarea\r\n                                    id=\"description\"\r\n                                    name=\"description\"\r\n                                    placeholder=\"Enter product description\"\r\n                                    required\r\n                                    onChange={handleChange}\r\n                                    value={formData.description}\r\n                                    rows=\"4\"\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"form-group\">\r\n                                <label htmlFor=\"price\">Price (₹)</label>\r\n                                <input\r\n                                    type=\"number\"\r\n                                    id=\"price\"\r\n                                    name=\"price\"\r\n                                    placeholder=\"Enter price\"\r\n                                    required\r\n                                    onChange={handleChange}\r\n                                    value={formData.price}\r\n                                    min=\"0\"\r\n                                    step=\"0.01\"\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"form-group\">\r\n                                <label htmlFor=\"image\">Image URL</label>\r\n                                <input\r\n                                    type=\"url\"\r\n                                    id=\"image\"\r\n                                    name=\"image\"\r\n                                    placeholder=\"Enter image URL\"\r\n                                    required\r\n                                    onChange={handleChange}\r\n                                    value={formData.image}\r\n                                />\r\n                            </div>\r\n\r\n                            <div className=\"form-actions\">\r\n                                <button type=\"button\" className=\"cancel-btn\" onClick={() => navigate('/AllProducts')}>\r\n                                    Cancel\r\n                                </button>\r\n                                <button type=\"submit\" className=\"submit-btn\">\r\n                                    Add Product\r\n                                </button>\r\n                            </div>\r\n                        </form>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </>\r\n    )\r\n}           "], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC3B,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACrCa,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEL,IAAI;MAAEM;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCR,WAAW,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACR,IAAI,GAAGM;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,YAAY,GAAIJ,CAAC,IAAK;IAC1BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElBtB,KAAK,CAACuB,IAAI,CAAC,kCAAkC,EAAEb,QAAQ,CAAC,CACrDc,IAAI,CAACC,QAAQ,IAAI;MAChBC,KAAK,CAAC,6BAA6B,CAAC;MACpCjB,QAAQ,CAAC,cAAc,CAAC;IAC1B,CAAC,CAAC,CACDkB,KAAK,CAACC,KAAK,IAAI;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CF,KAAK,CAAC,mDAAmD,CAAC;IAC5D,CAAC,CAAC;EACN,CAAC;EACD,oBACEtB,OAAA,CAAAE,SAAA;IAAAwB,QAAA,gBACI1B,OAAA,CAACF,MAAM;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACV9B,OAAA;MAAK+B,SAAS,EAAC,wBAAwB;MAAAL,QAAA,gBACnC1B,OAAA;QAAK+B,SAAS,EAAC,aAAa;QAAAL,QAAA,eACxB1B,OAAA;UAAK+B,SAAS,EAAC,qBAAqB;UAAAL,QAAA,gBAChC1B,OAAA;YAAA0B,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB9B,OAAA;YAAA0B,QAAA,EAAG;UAAmC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN9B,OAAA;QAAK+B,SAAS,EAAC,mBAAmB;QAAAL,QAAA,eAC9B1B,OAAA;UAAK+B,SAAS,EAAC,gBAAgB;UAAAL,QAAA,eAC3B1B,OAAA;YAAMgC,QAAQ,EAAEf,YAAa;YAACc,SAAS,EAAC,cAAc;YAAAL,QAAA,gBAClD1B,OAAA;cAAK+B,SAAS,EAAC,YAAY;cAAAL,QAAA,gBACvB1B,OAAA;gBAAOiC,OAAO,EAAC,MAAM;gBAAAP,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1C9B,OAAA;gBACIkC,IAAI,EAAC,MAAM;gBACXC,EAAE,EAAC,MAAM;gBACT3B,IAAI,EAAC,MAAM;gBACX4B,WAAW,EAAC,oBAAoB;gBAChCC,QAAQ;gBACRC,QAAQ,EAAE1B,YAAa;gBACvBE,KAAK,EAAER,QAAQ,CAACE;cAAK;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN9B,OAAA;cAAK+B,SAAS,EAAC,YAAY;cAAAL,QAAA,gBACvB1B,OAAA;gBAAOiC,OAAO,EAAC,aAAa;gBAAAP,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChD9B,OAAA;gBACImC,EAAE,EAAC,aAAa;gBAChB3B,IAAI,EAAC,aAAa;gBAClB4B,WAAW,EAAC,2BAA2B;gBACvCC,QAAQ;gBACRC,QAAQ,EAAE1B,YAAa;gBACvBE,KAAK,EAAER,QAAQ,CAACG,WAAY;gBAC5B8B,IAAI,EAAC;cAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN9B,OAAA;cAAK+B,SAAS,EAAC,YAAY;cAAAL,QAAA,gBACvB1B,OAAA;gBAAOiC,OAAO,EAAC,OAAO;gBAAAP,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxC9B,OAAA;gBACIkC,IAAI,EAAC,QAAQ;gBACbC,EAAE,EAAC,OAAO;gBACV3B,IAAI,EAAC,OAAO;gBACZ4B,WAAW,EAAC,aAAa;gBACzBC,QAAQ;gBACRC,QAAQ,EAAE1B,YAAa;gBACvBE,KAAK,EAAER,QAAQ,CAACI,KAAM;gBACtB8B,GAAG,EAAC,GAAG;gBACPC,IAAI,EAAC;cAAM;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN9B,OAAA;cAAK+B,SAAS,EAAC,YAAY;cAAAL,QAAA,gBACvB1B,OAAA;gBAAOiC,OAAO,EAAC,OAAO;gBAAAP,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxC9B,OAAA;gBACIkC,IAAI,EAAC,KAAK;gBACVC,EAAE,EAAC,OAAO;gBACV3B,IAAI,EAAC,OAAO;gBACZ4B,WAAW,EAAC,iBAAiB;gBAC7BC,QAAQ;gBACRC,QAAQ,EAAE1B,YAAa;gBACvBE,KAAK,EAAER,QAAQ,CAACK;cAAM;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAEN9B,OAAA;cAAK+B,SAAS,EAAC,cAAc;cAAAL,QAAA,gBACzB1B,OAAA;gBAAQkC,IAAI,EAAC,QAAQ;gBAACH,SAAS,EAAC,YAAY;gBAACW,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,cAAc,CAAE;gBAAAqB,QAAA,EAAC;cAEtF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9B,OAAA;gBAAQkC,IAAI,EAAC,QAAQ;gBAACH,SAAS,EAAC,YAAY;gBAAAL,QAAA,EAAC;cAE7C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACR,CAAC;AAEX;AAAC1B,EAAA,CA7GuBD,WAAW;EAAA,QACdN,WAAW;AAAA;AAAA8C,EAAA,GADRxC,WAAW;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}