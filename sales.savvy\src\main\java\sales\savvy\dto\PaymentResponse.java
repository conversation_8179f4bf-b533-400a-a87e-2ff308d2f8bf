package sales.savvy.dto;

public class PaymentResponse {
    private String orderId;
    private Double amount;
    private String currency;
    private String key;
    private String receipt;
    private String username;
    
    // Constructors
    public PaymentResponse() {}
    
    public PaymentResponse(String orderId, Double amount, String currency, String key, String receipt, String username) {
        this.orderId = orderId;
        this.amount = amount;
        this.currency = currency;
        this.key = key;
        this.receipt = receipt;
        this.username = username;
    }
    
    // Getters and Setters
    public String getOrderId() {
        return orderId;
    }
    
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
    
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public String getKey() {
        return key;
    }
    
    public void setKey(String key) {
        this.key = key;
    }
    
    public String getReceipt() {
        return receipt;
    }
    
    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    @Override
    public String toString() {
        return "PaymentResponse{" +
                "orderId='" + orderId + '\'' +
                ", amount=" + amount +
                ", currency='" + currency + '\'' +
                ", key='" + key + '\'' +
                ", receipt='" + receipt + '\'' +
                ", username='" + username + '\'' +
                '}';
    }
}
