package sales.savvy.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import sales.savvy.entity.Order;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    
    Optional<Order> findByOrderNumber(String orderNumber);
    
    Optional<Order> findByRazorpayOrderId(String razorpayOrderId);
    
    Optional<Order> findByPaymentId(String paymentId);
    
    List<Order> findByUsername(String username);
    
    List<Order> findByUsernameOrderByCreatedAtDesc(String username);
    
    List<Order> findByStatus(String status);
    
    List<Order> findByUsernameAndStatus(String username, String status);
    
    @Query("SELECT o FROM Order o WHERE o.username = :username AND o.createdAt BETWEEN :startDate AND :endDate ORDER BY o.createdAt DESC")
    List<Order> findByUsernameAndDateRange(@Param("username") String username, 
                                          @Param("startDate") LocalDateTime startDate, 
                                          @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT o FROM Order o WHERE o.createdAt BETWEEN :startDate AND :endDate ORDER BY o.createdAt DESC")
    List<Order> findByDateRange(@Param("startDate") LocalDateTime startDate, 
                               @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(o) FROM Order o WHERE o.username = :username")
    Long countByUsername(@Param("username") String username);
    
    @Query("SELECT SUM(o.totalAmount) FROM Order o WHERE o.username = :username AND o.status != 'CANCELLED'")
    Double getTotalSpentByUsername(@Param("username") String username);
    
    @Query("SELECT o FROM Order o WHERE o.trackingNumber = :trackingNumber")
    Optional<Order> findByTrackingNumber(@Param("trackingNumber") String trackingNumber);
    
    @Query("SELECT o FROM Order o WHERE o.estimatedDelivery < :currentDate AND o.status IN ('CONFIRMED', 'PROCESSING', 'SHIPPED')")
    List<Order> findOverdueOrders(@Param("currentDate") LocalDateTime currentDate);
}
