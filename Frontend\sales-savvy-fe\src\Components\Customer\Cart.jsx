import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import CustomerNavbar from '../Navbar/CustomerNavbar';
import Payment from '../Payment/Payment';
import { triggerCartUpdate, getUsername, handleApiError, showSuccessMessage } from '../shared/CartUtils';
import './Cart.css';

export default function Cart() {
    const [cartItems, setCartItems] = useState([]);
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(true);
    const [showPayment, setShowPayment] = useState(false);

    const calculateTotal = useCallback((items) => {
        const totalAmount = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
        setTotal(totalAmount);
    }, []);

    const fetchCartItems = useCallback(async () => {
        const username = getUsername();
        if (!username) {
            setLoading(false);
            return;
        }

        try {
            const response = await axios.get(`http://localhost:8080/getCart/${username}`);
            const items = response.data || [];
            setCartItems(items);
            calculateTotal(items);
            setLoading(false);
        } catch (error) {
            handleApiError(error, 'Failed to load cart items');
            setLoading(false);
        }
    }, [calculateTotal]);

    useEffect(() => {
        fetchCartItems();
    }, [fetchCartItems]);

    const updateQuantity = async (productId, newQuantity) => {
        const username = getUsername();
        if (!username) return;

        if (newQuantity <= 0) {
            removeFromCart(productId);
            return;
        }

        try {
            const cartItem = {
                username: username,
                productId: productId,
                quantity: newQuantity
            };

            const response = await axios.put('http://localhost:8080/updateCartItem', cartItem);

            if (response.data === 'cart updated') {
                fetchCartItems(); // Refresh cart data
                triggerCartUpdate();
            } else {
                showSuccessMessage('Failed to update cart: ' + response.data);
            }
        } catch (error) {
            handleApiError(error, 'Failed to update cart. Please try again.');
        }
    };

    const removeFromCart = async (productId) => {
        const username = getUsername();
        if (!username) return;

        try {
            const response = await axios.delete('http://localhost:8080/removeFromCart', {
                params: { username, productId }
            });

            if (response.data === 'item removed from cart') {
                fetchCartItems(); // Refresh cart data
                triggerCartUpdate();
            } else {
                showSuccessMessage('Failed to remove item: ' + response.data);
            }
        } catch (error) {
            handleApiError(error, 'Failed to remove item. Please try again.');
        }
    };

    const clearCart = async () => {
        const confirmed = window.confirm("Are you sure you want to clear your cart?");
        if (!confirmed) return;

        const username = getUsername();
        if (!username) return;

        try {
            const response = await axios.delete(`http://localhost:8080/clearCart/${username}`);

            if (response.data === 'cart cleared') {
                setCartItems([]);
                setTotal(0);
                triggerCartUpdate();
            } else {
                showSuccessMessage('Failed to clear cart: ' + response.data);
            }
        } catch (error) {
            handleApiError(error, 'Failed to clear cart. Please try again.');
        }
    };

    const proceedToCheckout = () => {
        if (cartItems.length === 0) {
            showSuccessMessage("Your cart is empty!");
            return;
        }
        setShowPayment(true);
    };

    const handlePaymentSuccess = async (paymentResponse) => {
        try {
            // Clear the cart after successful payment
            await clearCart();
            setShowPayment(false);
            showSuccessMessage('Payment successful! Your order has been placed.');
        } catch (error) {
            console.error('Error clearing cart after payment:', error);
            setShowPayment(false);
            showSuccessMessage('Payment successful! Please manually clear your cart.');
        }
    };

    const handlePaymentFailure = (error) => {
        console.error('Payment failed:', error);
        setShowPayment(false);
        handleApiError(error, 'Payment failed. Please try again.');
    };

    const closePaymentModal = () => {
        setShowPayment(false);
    };

    if (loading) {
        return (
            <>
                <CustomerNavbar />
                <div className="cart-container">
                    <div className="loading-container">
                        <div className="loading-spinner"></div>
                        <p>Loading your cart...</p>
                    </div>
                </div>
            </>
        );
    }

    return (
        <>
            <CustomerNavbar />
            <div className="cart-container">
                <div className="cart-header">
                    <div className="cart-header-content">
                        <h1>Shopping Cart</h1>
                        <p>Review your items before checkout</p>
                    </div>
                </div>
                <div className="cart-main-content">

                {cartItems.length === 0 ? (
                    <div className="empty-cart">
                        <div className="empty-cart-icon">🛒</div>
                        <h3>Your cart is empty</h3>
                        <p>Add some products to get started!</p>
                        <button
                            className="continue-shopping-btn"
                            onClick={() => window.history.back()}
                        >
                            Continue Shopping
                        </button>
                    </div>
                ) : (
                    <div className="cart-content">
                        <div className="cart-items">
                            <div className="cart-items-grid">
                            {cartItems.map(item => (
                                <div key={item.id} className="cart-item">
                                    <div className="item-image">
                                        <img
                                            src={item.product.image}
                                            alt={item.product.name}
                                            onError={(e) => {
                                                e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';
                                            }}
                                        />
                                    </div>
                                    <div className="item-details">
                                        <h3>{item.product.name}</h3>
                                        <p>{item.product.description}</p>
                                        <div className="item-price">₹{item.product.price}</div>
                                    </div>
                                    <div className="item-controls">
                                        <div className="quantity-controls">
                                            <button
                                                onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                                                className="quantity-btn minus-btn"
                                                title="Remove one"
                                            >
                                                -
                                            </button>
                                            <span className="quantity">{item.quantity}</span>
                                            <button
                                                onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                                                className="quantity-btn"
                                                title="Add one more"
                                            >
                                                +
                                            </button>
                                        </div>
                                        <div className="item-total">₹{item.product.price * item.quantity}</div>
                                        <button
                                            onClick={() => removeFromCart(item.product.id)}
                                            className="remove-btn"
                                            title="Remove from cart"
                                        >
                                            🗑️
                                        </button>
                                    </div>
                                </div>
                            ))}
                            </div>
                        </div>

                        <div className="cart-summary">
                            <div className="summary-card">
                                <h3>Order Summary</h3>
                                <div className="summary-row">
                                    <span>Items ({cartItems.length})</span>
                                    <span>₹{total}</span>
                                </div>
                                <div className="summary-row">
                                    <span>Shipping</span>
                                    <span>Free</span>
                                </div>
                                <div className="summary-row total-row">
                                    <span>Total</span>
                                    <span>₹{total}</span>
                                </div>
                                <div className="cart-actions">
                                    <button 
                                        className="checkout-btn"
                                        onClick={proceedToCheckout}
                                    >
                                        Proceed to Checkout
                                    </button>
                                    <button 
                                        className="clear-cart-btn"
                                        onClick={clearCart}
                                    >
                                        Clear Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                </div>
            </div>

            {/* Payment Modal */}
            {showPayment && (
                <div className="payment-modal-overlay">
                    <div className="payment-modal">
                        <div className="payment-modal-header">
                            <h2>Complete Your Payment</h2>
                            <button
                                className="close-modal-btn"
                                onClick={closePaymentModal}
                                title="Close"
                            >
                                ✕
                            </button>
                        </div>
                        <Payment
                            amount={total}
                            cartItems={cartItems}
                            onPaymentSuccess={handlePaymentSuccess}
                            onPaymentFailure={handlePaymentFailure}
                        />
                    </div>
                </div>
            )}
        </>
    );
}
