import React, { useState, useEffect } from 'react';
import CustomerNavbar from '../Navbar/CustomerNavbar';
import './Cart.css';

export default function Cart() {
    const [cartItems, setCartItems] = useState([]);
    const [total, setTotal] = useState(0);

    useEffect(() => {
        // Load cart items from localStorage
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
            const items = JSON.parse(savedCart);
            setCartItems(items);
            calculateTotal(items);
        }
    }, []);

    const calculateTotal = (items) => {
        const totalAmount = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        setTotal(totalAmount);
    };

    const updateQuantity = (id, newQuantity) => {
        if (newQuantity <= 0) {
            removeFromCart(id);
            return;
        }

        const updatedItems = cartItems.map(item =>
            item.id === id ? { ...item, quantity: newQuantity } : item
        );
        setCartItems(updatedItems);
        calculateTotal(updatedItems);
        localStorage.setItem('cart', JSON.stringify(updatedItems));
    };

    const removeFromCart = (id) => {
        const updatedItems = cartItems.filter(item => item.id !== id);
        setCartItems(updatedItems);
        calculateTotal(updatedItems);
        localStorage.setItem('cart', JSON.stringify(updatedItems));
    };

    const clearCart = () => {
        const confirmed = window.confirm("Are you sure you want to clear your cart?");
        if (confirmed) {
            setCartItems([]);
            setTotal(0);
            localStorage.removeItem('cart');
        }
    };

    const proceedToCheckout = () => {
        if (cartItems.length === 0) {
            alert("Your cart is empty!");
            return;
        }
        alert(`Proceeding to checkout with total: ₹${total}`);
        // Implement actual checkout logic here
    };

    return (
        <>
            <CustomerNavbar />
            <div className="cart-container">
                <div className="cart-header">
                    <h1>Shopping Cart</h1>
                    <p>Review your items before checkout</p>
                </div>

                {cartItems.length === 0 ? (
                    <div className="empty-cart">
                        <div className="empty-cart-icon">🛒</div>
                        <h3>Your cart is empty</h3>
                        <p>Add some products to get started!</p>
                        <button 
                            className="continue-shopping-btn"
                            onClick={() => window.history.back()}
                        >
                            Continue Shopping
                        </button>
                    </div>
                ) : (
                    <div className="cart-content">
                        <div className="cart-items">
                            {cartItems.map(item => (
                                <div key={item.id} className="cart-item">
                                    <div className="item-image">
                                        <img 
                                            src={item.image} 
                                            alt={item.name}
                                            onError={(e) => {
                                                e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';
                                            }}
                                        />
                                    </div>
                                    <div className="item-details">
                                        <h3>{item.name}</h3>
                                        <p>{item.description}</p>
                                        <div className="item-price">₹{item.price}</div>
                                    </div>
                                    <div className="item-controls">
                                        <div className="quantity-controls">
                                            <button 
                                                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                                className="quantity-btn"
                                            >
                                                -
                                            </button>
                                            <span className="quantity">{item.quantity}</span>
                                            <button 
                                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                                className="quantity-btn"
                                            >
                                                +
                                            </button>
                                        </div>
                                        <div className="item-total">₹{item.price * item.quantity}</div>
                                        <button 
                                            onClick={() => removeFromCart(item.id)}
                                            className="remove-btn"
                                            title="Remove from cart"
                                        >
                                            🗑️
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>

                        <div className="cart-summary">
                            <div className="summary-card">
                                <h3>Order Summary</h3>
                                <div className="summary-row">
                                    <span>Items ({cartItems.length})</span>
                                    <span>₹{total}</span>
                                </div>
                                <div className="summary-row">
                                    <span>Shipping</span>
                                    <span>Free</span>
                                </div>
                                <div className="summary-row total-row">
                                    <span>Total</span>
                                    <span>₹{total}</span>
                                </div>
                                <div className="cart-actions">
                                    <button 
                                        className="checkout-btn"
                                        onClick={proceedToCheckout}
                                    >
                                        Proceed to Checkout
                                    </button>
                                    <button 
                                        className="clear-cart-btn"
                                        onClick={clearCart}
                                    >
                                        Clear Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}
