{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\AdminPages\\\\AllProducts.jsx\";\nimport React from 'react';\nimport Navbar from '../Navbar/Navbar';\nimport ProductsList from '../shared/ProductsList';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function AllProducts() {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ProductsList, {\n      userRole: \"admin\",\n      showActions: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n}\n_c = AllProducts;\nvar _c;\n$RefreshReg$(_c, \"AllProducts\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "ProductsList", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AllProducts", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "userRole", "showActions", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/AdminPages/AllProducts.jsx"], "sourcesContent": ["import React from 'react';\r\nimport Navbar from '../Navbar/Navbar';\r\nimport ProductsList from '../shared/ProductsList';\r\n\r\nexport default function AllProducts() {\r\n    return (\r\n        <>\r\n            <Navbar />\r\n            <ProductsList userRole=\"admin\" showActions={true} />\r\n        </>\r\n    );\r\n}\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,YAAY,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,eAAe,SAASC,WAAWA,CAAA,EAAG;EAClC,oBACIH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACIJ,OAAA,CAACH,MAAM;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVR,OAAA,CAACF,YAAY;MAACW,QAAQ,EAAC,OAAO;MAACC,WAAW,EAAE;IAAK;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACtD,CAAC;AAEX;AAACG,EAAA,GAPuBR,WAAW;AAAA,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}