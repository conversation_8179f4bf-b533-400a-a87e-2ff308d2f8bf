package sales.savvy.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import sales.savvy.dto.PaymentRequest;
import sales.savvy.dto.PaymentResponse;
import sales.savvy.dto.PaymentVerificationRequest;
import sales.savvy.entity.Payment;
import sales.savvy.service.PaymentService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/payment")
@CrossOrigin(origins = "*")
public class PaymentController {
    
    @Autowired
    private PaymentService paymentService;
    
    /**
     * Create a new payment order
     */
    @PostMapping("/create-order")
    public ResponseEntity<?> createOrder(@RequestBody PaymentRequest paymentRequest) {
        try {
            // Validate request
            if (paymentRequest.getAmount() == null || paymentRequest.getAmount() <= 0) {
                return ResponseEntity.badRequest().body("Invalid amount");
            }
            
            if (paymentRequest.getUsername() == null || paymentRequest.getUsername().trim().isEmpty()) {
                return ResponseEntity.badRequest().body("Username is required");
            }
            
            // Generate receipt if not provided
            if (paymentRequest.getReceipt() == null || paymentRequest.getReceipt().trim().isEmpty()) {
                paymentRequest.setReceipt("receipt_" + System.currentTimeMillis());
            }
            
            PaymentResponse response = paymentService.createOrder(paymentRequest);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Verify payment after successful payment
     */
    @PostMapping("/verify")
    public ResponseEntity<?> verifyPayment(@RequestBody PaymentVerificationRequest verificationRequest) {
        try {
            // Validate request
            if (verificationRequest.getRazorpayOrderId() == null || 
                verificationRequest.getRazorpayPaymentId() == null || 
                verificationRequest.getRazorpaySignature() == null) {
                return ResponseEntity.badRequest().body("Missing required payment details");
            }
            
            String result = paymentService.verifyPayment(verificationRequest);
            
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", result);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("status", "failed");
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
        }
    }
    
    /**
     * Get payment details by order ID
     */
    @GetMapping("/order/{orderId}")
    public ResponseEntity<?> getPaymentByOrderId(@PathVariable String orderId) {
        try {
            Payment payment = paymentService.getPaymentByOrderId(orderId);
            if (payment != null) {
                return ResponseEntity.ok(payment);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Get payment details by payment ID
     */
    @GetMapping("/payment/{paymentId}")
    public ResponseEntity<?> getPaymentByPaymentId(@PathVariable String paymentId) {
        try {
            Payment payment = paymentService.getPaymentByPaymentId(paymentId);
            if (payment != null) {
                return ResponseEntity.ok(payment);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Get payment history for a user
     */
    @GetMapping("/history/{username}")
    public ResponseEntity<?> getPaymentHistory(@PathVariable String username) {
        try {
            List<Payment> payments = paymentService.getPaymentHistory(username);
            return ResponseEntity.ok(payments);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Get all payments for a user
     */
    @GetMapping("/user/{username}")
    public ResponseEntity<?> getUserPayments(@PathVariable String username) {
        try {
            List<Payment> payments = paymentService.getPaymentsByUsername(username);
            return ResponseEntity.ok(payments);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Get payments by status
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<?> getPaymentsByStatus(@PathVariable String status) {
        try {
            List<Payment> payments = paymentService.getPaymentsByStatus(status);
            return ResponseEntity.ok(payments);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Update payment status (for admin use)
     */
    @PutMapping("/status")
    public ResponseEntity<?> updatePaymentStatus(@RequestParam String orderId, @RequestParam String status) {
        try {
            Payment updatedPayment = paymentService.updatePaymentStatus(orderId, status);
            if (updatedPayment != null) {
                return ResponseEntity.ok(updatedPayment);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}
