import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useLocation, useNavigate } from 'react-router-dom';

export default function UpdateProduct() {
    const location = useLocation();
    const navigate = useNavigate();
    const { product } = location.state || {}; // fallback if state is undefined

    const [updatedProduct, setUpdatedProduct] = useState({
        id: '',
        name: '',
        description: '',
        price: '',
        image: ''
    });

    useEffect(() => {
        if (product) {
            setUpdatedProduct(product);
        }
    }, [product]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setUpdatedProduct(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        axios.put('http://localhost:8080/updateProduct', updatedProduct)
            .then(() => {
                alert('Product updated successfully!');
                navigate('/AdminDashboard'); // or whatever your list page route is
            })
            .catch(error => {
                console.error('Error updating product:', error);
                alert('Failed to update product');
            });
    };

    if (!product) {
        return <div>No product selected for update.</div>;
    }

    return (
        <div>
            <h2>Update Product</h2>
            <form onSubmit={handleSubmit}>
                <div>
                    <label>ID (read-only):</label>
                    <input type="text" name="id" value={updatedProduct.id} readOnly />
                </div>
                <div>
                    <label>Name:</label>
                    <input type="text" name="name" value={updatedProduct.name} onChange={handleChange} required />
                </div>
                <div>
                    <label>Description:</label>
                    <input type="text" name="description" value={updatedProduct.description} onChange={handleChange} required />
                </div>
                <div>
                    <label>Price (₹):</label>
                    <input type="number" name="price" value={updatedProduct.price} onChange={handleChange} required />
                </div>
                <div>
                    <label>Image URL:</label>
                    <input type="text" name="image" value={updatedProduct.image} onChange={handleChange} required />
                </div>
                <br />
                <button type="submit">Update</button>
                <button type="button" onClick={() => navigate('/all_products')}>Cancel</button>
            </form>
        </div>
    );
}
