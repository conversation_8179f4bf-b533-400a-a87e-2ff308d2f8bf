// THIS FILE IS AUTO GENERATED
import type { IconType } from '../lib/index'
export declare const Fa42Group: IconType;
export declare const Fa500Px: IconType;
export declare const FaAccessibleIcon: IconType;
export declare const FaAccusoft: IconType;
export declare const FaAdn: IconType;
export declare const FaAdversal: IconType;
export declare const FaAffiliatetheme: IconType;
export declare const FaAirbnb: IconType;
export declare const FaAlgolia: IconType;
export declare const FaAlipay: IconType;
export declare const FaAmazonPay: IconType;
export declare const FaAmazon: IconType;
export declare const FaAmilia: IconType;
export declare const FaAndroid: IconType;
export declare const FaAngellist: IconType;
export declare const FaAngrycreative: IconType;
export declare const FaAngular: IconType;
export declare const FaAppStoreIos: IconType;
export declare const FaAppStore: IconType;
export declare const FaApper: IconType;
export declare const FaApplePay: IconType;
export declare const FaApple: IconType;
export declare const FaArtstation: IconType;
export declare const FaAsymmetrik: IconType;
export declare const FaAtlassian: IconType;
export declare const FaAudible: IconType;
export declare const FaAutoprefixer: IconType;
export declare const FaAvianex: IconType;
export declare const FaAviato: IconType;
export declare const FaAws: IconType;
export declare const FaBandcamp: IconType;
export declare const FaBattleNet: IconType;
export declare const FaBehance: IconType;
export declare const FaBilibili: IconType;
export declare const FaBimobject: IconType;
export declare const FaBitbucket: IconType;
export declare const FaBitcoin: IconType;
export declare const FaBity: IconType;
export declare const FaBlackTie: IconType;
export declare const FaBlackberry: IconType;
export declare const FaBloggerB: IconType;
export declare const FaBlogger: IconType;
export declare const FaBluesky: IconType;
export declare const FaBluetoothB: IconType;
export declare const FaBluetooth: IconType;
export declare const FaBootstrap: IconType;
export declare const FaBots: IconType;
export declare const FaBraveReverse: IconType;
export declare const FaBrave: IconType;
export declare const FaBtc: IconType;
export declare const FaBuffer: IconType;
export declare const FaBuromobelexperte: IconType;
export declare const FaBuyNLarge: IconType;
export declare const FaBuysellads: IconType;
export declare const FaCanadianMapleLeaf: IconType;
export declare const FaCcAmazonPay: IconType;
export declare const FaCcAmex: IconType;
export declare const FaCcApplePay: IconType;
export declare const FaCcDinersClub: IconType;
export declare const FaCcDiscover: IconType;
export declare const FaCcJcb: IconType;
export declare const FaCcMastercard: IconType;
export declare const FaCcPaypal: IconType;
export declare const FaCcStripe: IconType;
export declare const FaCcVisa: IconType;
export declare const FaCentercode: IconType;
export declare const FaCentos: IconType;
export declare const FaChrome: IconType;
export declare const FaChromecast: IconType;
export declare const FaCloudflare: IconType;
export declare const FaCloudscale: IconType;
export declare const FaCloudsmith: IconType;
export declare const FaCloudversify: IconType;
export declare const FaCmplid: IconType;
export declare const FaCodepen: IconType;
export declare const FaCodiepie: IconType;
export declare const FaConfluence: IconType;
export declare const FaConnectdevelop: IconType;
export declare const FaContao: IconType;
export declare const FaCottonBureau: IconType;
export declare const FaCpanel: IconType;
export declare const FaCreativeCommonsBy: IconType;
export declare const FaCreativeCommonsNcEu: IconType;
export declare const FaCreativeCommonsNcJp: IconType;
export declare const FaCreativeCommonsNc: IconType;
export declare const FaCreativeCommonsNd: IconType;
export declare const FaCreativeCommonsPdAlt: IconType;
export declare const FaCreativeCommonsPd: IconType;
export declare const FaCreativeCommonsRemix: IconType;
export declare const FaCreativeCommonsSa: IconType;
export declare const FaCreativeCommonsSamplingPlus: IconType;
export declare const FaCreativeCommonsSampling: IconType;
export declare const FaCreativeCommonsShare: IconType;
export declare const FaCreativeCommonsZero: IconType;
export declare const FaCreativeCommons: IconType;
export declare const FaCriticalRole: IconType;
export declare const FaCss3Alt: IconType;
export declare const FaCss3: IconType;
export declare const FaCuttlefish: IconType;
export declare const FaDAndDBeyond: IconType;
export declare const FaDAndD: IconType;
export declare const FaDailymotion: IconType;
export declare const FaDartLang: IconType;
export declare const FaDashcube: IconType;
export declare const FaDebian: IconType;
export declare const FaDeezer: IconType;
export declare const FaDelicious: IconType;
export declare const FaDeploydog: IconType;
export declare const FaDeskpro: IconType;
export declare const FaDev: IconType;
export declare const FaDeviantart: IconType;
export declare const FaDhl: IconType;
export declare const FaDiaspora: IconType;
export declare const FaDigg: IconType;
export declare const FaDigitalOcean: IconType;
export declare const FaDiscord: IconType;
export declare const FaDiscourse: IconType;
export declare const FaDochub: IconType;
export declare const FaDocker: IconType;
export declare const FaDraft2Digital: IconType;
export declare const FaDribbble: IconType;
export declare const FaDropbox: IconType;
export declare const FaDrupal: IconType;
export declare const FaDyalog: IconType;
export declare const FaEarlybirds: IconType;
export declare const FaEbay: IconType;
export declare const FaEdgeLegacy: IconType;
export declare const FaEdge: IconType;
export declare const FaElementor: IconType;
export declare const FaEllo: IconType;
export declare const FaEmber: IconType;
export declare const FaEmpire: IconType;
export declare const FaEnvira: IconType;
export declare const FaErlang: IconType;
export declare const FaEthereum: IconType;
export declare const FaEtsy: IconType;
export declare const FaEvernote: IconType;
export declare const FaExpeditedssl: IconType;
export declare const FaFacebookF: IconType;
export declare const FaFacebookMessenger: IconType;
export declare const FaFacebook: IconType;
export declare const FaFantasyFlightGames: IconType;
export declare const FaFedex: IconType;
export declare const FaFedora: IconType;
export declare const FaFigma: IconType;
export declare const FaFirefoxBrowser: IconType;
export declare const FaFirefox: IconType;
export declare const FaFirstOrderAlt: IconType;
export declare const FaFirstOrder: IconType;
export declare const FaFirstdraft: IconType;
export declare const FaFlickr: IconType;
export declare const FaFlipboard: IconType;
export declare const FaFlutter: IconType;
export declare const FaFly: IconType;
export declare const FaFontAwesome: IconType;
export declare const FaFonticonsFi: IconType;
export declare const FaFonticons: IconType;
export declare const FaFortAwesomeAlt: IconType;
export declare const FaFortAwesome: IconType;
export declare const FaForumbee: IconType;
export declare const FaFoursquare: IconType;
export declare const FaFreeCodeCamp: IconType;
export declare const FaFreebsd: IconType;
export declare const FaFulcrum: IconType;
export declare const FaGalacticRepublic: IconType;
export declare const FaGalacticSenate: IconType;
export declare const FaGetPocket: IconType;
export declare const FaGgCircle: IconType;
export declare const FaGg: IconType;
export declare const FaGitAlt: IconType;
export declare const FaGit: IconType;
export declare const FaGithubAlt: IconType;
export declare const FaGithub: IconType;
export declare const FaGitkraken: IconType;
export declare const FaGitlab: IconType;
export declare const FaGitter: IconType;
export declare const FaGlideG: IconType;
export declare const FaGlide: IconType;
export declare const FaGofore: IconType;
export declare const FaGolang: IconType;
export declare const FaGoodreadsG: IconType;
export declare const FaGoodreads: IconType;
export declare const FaGoogleDrive: IconType;
export declare const FaGooglePay: IconType;
export declare const FaGooglePlay: IconType;
export declare const FaGooglePlusG: IconType;
export declare const FaGooglePlus: IconType;
export declare const FaGoogleScholar: IconType;
export declare const FaGoogleWallet: IconType;
export declare const FaGoogle: IconType;
export declare const FaGratipay: IconType;
export declare const FaGrav: IconType;
export declare const FaGripfire: IconType;
export declare const FaGrunt: IconType;
export declare const FaGuilded: IconType;
export declare const FaGulp: IconType;
export declare const FaHackerNews: IconType;
export declare const FaHackerrank: IconType;
export declare const FaHashnode: IconType;
export declare const FaHips: IconType;
export declare const FaHireAHelper: IconType;
export declare const FaHive: IconType;
export declare const FaHooli: IconType;
export declare const FaHornbill: IconType;
export declare const FaHotjar: IconType;
export declare const FaHouzz: IconType;
export declare const FaHtml5: IconType;
export declare const FaHubspot: IconType;
export declare const FaIdeal: IconType;
export declare const FaImdb: IconType;
export declare const FaInstagram: IconType;
export declare const FaInstalod: IconType;
export declare const FaIntercom: IconType;
export declare const FaInternetExplorer: IconType;
export declare const FaInvision: IconType;
export declare const FaIoxhost: IconType;
export declare const FaItchIo: IconType;
export declare const FaItunesNote: IconType;
export declare const FaItunes: IconType;
export declare const FaJava: IconType;
export declare const FaJediOrder: IconType;
export declare const FaJenkins: IconType;
export declare const FaJira: IconType;
export declare const FaJoget: IconType;
export declare const FaJoomla: IconType;
export declare const FaJs: IconType;
export declare const FaJsfiddle: IconType;
export declare const FaJxl: IconType;
export declare const FaKaggle: IconType;
export declare const FaKeybase: IconType;
export declare const FaKeycdn: IconType;
export declare const FaKickstarterK: IconType;
export declare const FaKickstarter: IconType;
export declare const FaKorvue: IconType;
export declare const FaLaravel: IconType;
export declare const FaLastfm: IconType;
export declare const FaLeanpub: IconType;
export declare const FaLess: IconType;
export declare const FaLetterboxd: IconType;
export declare const FaLine: IconType;
export declare const FaLinkedinIn: IconType;
export declare const FaLinkedin: IconType;
export declare const FaLinode: IconType;
export declare const FaLinux: IconType;
export declare const FaLyft: IconType;
export declare const FaMagento: IconType;
export declare const FaMailchimp: IconType;
export declare const FaMandalorian: IconType;
export declare const FaMarkdown: IconType;
export declare const FaMastodon: IconType;
export declare const FaMaxcdn: IconType;
export declare const FaMdb: IconType;
export declare const FaMedapps: IconType;
export declare const FaMedium: IconType;
export declare const FaMedrt: IconType;
export declare const FaMeetup: IconType;
export declare const FaMegaport: IconType;
export declare const FaMendeley: IconType;
export declare const FaMeta: IconType;
export declare const FaMicroblog: IconType;
export declare const FaMicrosoft: IconType;
export declare const FaMintbit: IconType;
export declare const FaMix: IconType;
export declare const FaMixcloud: IconType;
export declare const FaMixer: IconType;
export declare const FaMizuni: IconType;
export declare const FaModx: IconType;
export declare const FaMonero: IconType;
export declare const FaNapster: IconType;
export declare const FaNeos: IconType;
export declare const FaNfcDirectional: IconType;
export declare const FaNfcSymbol: IconType;
export declare const FaNimblr: IconType;
export declare const FaNodeJs: IconType;
export declare const FaNode: IconType;
export declare const FaNpm: IconType;
export declare const FaNs8: IconType;
export declare const FaNutritionix: IconType;
export declare const FaOctopusDeploy: IconType;
export declare const FaOdnoklassniki: IconType;
export declare const FaOdysee: IconType;
export declare const FaOldRepublic: IconType;
export declare const FaOpencart: IconType;
export declare const FaOpenid: IconType;
export declare const FaOpensuse: IconType;
export declare const FaOpera: IconType;
export declare const FaOptinMonster: IconType;
export declare const FaOrcid: IconType;
export declare const FaOsi: IconType;
export declare const FaPadlet: IconType;
export declare const FaPage4: IconType;
export declare const FaPagelines: IconType;
export declare const FaPalfed: IconType;
export declare const FaPatreon: IconType;
export declare const FaPaypal: IconType;
export declare const FaPerbyte: IconType;
export declare const FaPeriscope: IconType;
export declare const FaPhabricator: IconType;
export declare const FaPhoenixFramework: IconType;
export declare const FaPhoenixSquadron: IconType;
export declare const FaPhp: IconType;
export declare const FaPiedPiperAlt: IconType;
export declare const FaPiedPiperHat: IconType;
export declare const FaPiedPiperPp: IconType;
export declare const FaPiedPiper: IconType;
export declare const FaPinterestP: IconType;
export declare const FaPinterest: IconType;
export declare const FaPix: IconType;
export declare const FaPixiv: IconType;
export declare const FaPlaystation: IconType;
export declare const FaProductHunt: IconType;
export declare const FaPushed: IconType;
export declare const FaPython: IconType;
export declare const FaQq: IconType;
export declare const FaQuinscape: IconType;
export declare const FaQuora: IconType;
export declare const FaRProject: IconType;
export declare const FaRaspberryPi: IconType;
export declare const FaRavelry: IconType;
export declare const FaReact: IconType;
export declare const FaReacteurope: IconType;
export declare const FaReadme: IconType;
export declare const FaRebel: IconType;
export declare const FaRedRiver: IconType;
export declare const FaRedditAlien: IconType;
export declare const FaReddit: IconType;
export declare const FaRedhat: IconType;
export declare const FaRenren: IconType;
export declare const FaReplyd: IconType;
export declare const FaResearchgate: IconType;
export declare const FaResolving: IconType;
export declare const FaRev: IconType;
export declare const FaRocketchat: IconType;
export declare const FaRockrms: IconType;
export declare const FaRust: IconType;
export declare const FaSafari: IconType;
export declare const FaSalesforce: IconType;
export declare const FaSass: IconType;
export declare const FaSchlix: IconType;
export declare const FaScreenpal: IconType;
export declare const FaScribd: IconType;
export declare const FaSearchengin: IconType;
export declare const FaSellcast: IconType;
export declare const FaSellsy: IconType;
export declare const FaServicestack: IconType;
export declare const FaShirtsinbulk: IconType;
export declare const FaShoelace: IconType;
export declare const FaShopify: IconType;
export declare const FaShopware: IconType;
export declare const FaSignalMessenger: IconType;
export declare const FaSimplybuilt: IconType;
export declare const FaSistrix: IconType;
export declare const FaSith: IconType;
export declare const FaSitrox: IconType;
export declare const FaSketch: IconType;
export declare const FaSkyatlas: IconType;
export declare const FaSkype: IconType;
export declare const FaSlack: IconType;
export declare const FaSlideshare: IconType;
export declare const FaSnapchat: IconType;
export declare const FaSoundcloud: IconType;
export declare const FaSourcetree: IconType;
export declare const FaSpaceAwesome: IconType;
export declare const FaSpeakap: IconType;
export declare const FaSpeakerDeck: IconType;
export declare const FaSpotify: IconType;
export declare const FaSquareBehance: IconType;
export declare const FaSquareDribbble: IconType;
export declare const FaSquareFacebook: IconType;
export declare const FaSquareFontAwesomeStroke: IconType;
export declare const FaSquareFontAwesome: IconType;
export declare const FaSquareGit: IconType;
export declare const FaSquareGithub: IconType;
export declare const FaSquareGitlab: IconType;
export declare const FaSquareGooglePlus: IconType;
export declare const FaSquareHackerNews: IconType;
export declare const FaSquareInstagram: IconType;
export declare const FaSquareJs: IconType;
export declare const FaSquareLastfm: IconType;
export declare const FaSquareLetterboxd: IconType;
export declare const FaSquareOdnoklassniki: IconType;
export declare const FaSquarePiedPiper: IconType;
export declare const FaSquarePinterest: IconType;
export declare const FaSquareReddit: IconType;
export declare const FaSquareSnapchat: IconType;
export declare const FaSquareSteam: IconType;
export declare const FaSquareThreads: IconType;
export declare const FaSquareTumblr: IconType;
export declare const FaSquareTwitter: IconType;
export declare const FaSquareUpwork: IconType;
export declare const FaSquareViadeo: IconType;
export declare const FaSquareVimeo: IconType;
export declare const FaSquareWebAwesomeStroke: IconType;
export declare const FaSquareWebAwesome: IconType;
export declare const FaSquareWhatsapp: IconType;
export declare const FaSquareXTwitter: IconType;
export declare const FaSquareXing: IconType;
export declare const FaSquareYoutube: IconType;
export declare const FaSquarespace: IconType;
export declare const FaStackExchange: IconType;
export declare const FaStackOverflow: IconType;
export declare const FaStackpath: IconType;
export declare const FaStaylinked: IconType;
export declare const FaSteamSymbol: IconType;
export declare const FaSteam: IconType;
export declare const FaStickerMule: IconType;
export declare const FaStrava: IconType;
export declare const FaStripeS: IconType;
export declare const FaStripe: IconType;
export declare const FaStubber: IconType;
export declare const FaStudiovinari: IconType;
export declare const FaStumbleuponCircle: IconType;
export declare const FaStumbleupon: IconType;
export declare const FaSuperpowers: IconType;
export declare const FaSupple: IconType;
export declare const FaSuse: IconType;
export declare const FaSwift: IconType;
export declare const FaSymfony: IconType;
export declare const FaTeamspeak: IconType;
export declare const FaTelegram: IconType;
export declare const FaTencentWeibo: IconType;
export declare const FaTheRedYeti: IconType;
export declare const FaThemeco: IconType;
export declare const FaThemeisle: IconType;
export declare const FaThinkPeaks: IconType;
export declare const FaThreads: IconType;
export declare const FaTiktok: IconType;
export declare const FaTradeFederation: IconType;
export declare const FaTrello: IconType;
export declare const FaTumblr: IconType;
export declare const FaTwitch: IconType;
export declare const FaTwitter: IconType;
export declare const FaTypo3: IconType;
export declare const FaUber: IconType;
export declare const FaUbuntu: IconType;
export declare const FaUikit: IconType;
export declare const FaUmbraco: IconType;
export declare const FaUncharted: IconType;
export declare const FaUniregistry: IconType;
export declare const FaUnity: IconType;
export declare const FaUnsplash: IconType;
export declare const FaUntappd: IconType;
export declare const FaUps: IconType;
export declare const FaUpwork: IconType;
export declare const FaUsb: IconType;
export declare const FaUsps: IconType;
export declare const FaUssunnah: IconType;
export declare const FaVaadin: IconType;
export declare const FaViacoin: IconType;
export declare const FaViadeo: IconType;
export declare const FaViber: IconType;
export declare const FaVimeoV: IconType;
export declare const FaVimeo: IconType;
export declare const FaVine: IconType;
export declare const FaVk: IconType;
export declare const FaVnv: IconType;
export declare const FaVuejs: IconType;
export declare const FaWatchmanMonitoring: IconType;
export declare const FaWaze: IconType;
export declare const FaWebAwesome: IconType;
export declare const FaWebflow: IconType;
export declare const FaWeebly: IconType;
export declare const FaWeibo: IconType;
export declare const FaWeixin: IconType;
export declare const FaWhatsapp: IconType;
export declare const FaWhmcs: IconType;
export declare const FaWikipediaW: IconType;
export declare const FaWindows: IconType;
export declare const FaWirsindhandwerk: IconType;
export declare const FaWix: IconType;
export declare const FaWizardsOfTheCoast: IconType;
export declare const FaWodu: IconType;
export declare const FaWolfPackBattalion: IconType;
export declare const FaWordpressSimple: IconType;
export declare const FaWordpress: IconType;
export declare const FaWpbeginner: IconType;
export declare const FaWpexplorer: IconType;
export declare const FaWpforms: IconType;
export declare const FaWpressr: IconType;
export declare const FaXTwitter: IconType;
export declare const FaXbox: IconType;
export declare const FaXing: IconType;
export declare const FaYCombinator: IconType;
export declare const FaYahoo: IconType;
export declare const FaYammer: IconType;
export declare const FaYandexInternational: IconType;
export declare const FaYandex: IconType;
export declare const FaYarn: IconType;
export declare const FaYelp: IconType;
export declare const FaYoast: IconType;
export declare const FaYoutube: IconType;
export declare const FaZhihu: IconType;
export declare const Fa0: IconType;
export declare const Fa1: IconType;
export declare const Fa2: IconType;
export declare const Fa3: IconType;
export declare const Fa4: IconType;
export declare const Fa5: IconType;
export declare const Fa6: IconType;
export declare const Fa7: IconType;
export declare const Fa8: IconType;
export declare const Fa9: IconType;
export declare const FaA: IconType;
export declare const FaAddressBook: IconType;
export declare const FaAddressCard: IconType;
export declare const FaAlignCenter: IconType;
export declare const FaAlignJustify: IconType;
export declare const FaAlignLeft: IconType;
export declare const FaAlignRight: IconType;
export declare const FaAnchorCircleCheck: IconType;
export declare const FaAnchorCircleExclamation: IconType;
export declare const FaAnchorCircleXmark: IconType;
export declare const FaAnchorLock: IconType;
export declare const FaAnchor: IconType;
export declare const FaAngleDown: IconType;
export declare const FaAngleLeft: IconType;
export declare const FaAngleRight: IconType;
export declare const FaAngleUp: IconType;
export declare const FaAnglesDown: IconType;
export declare const FaAnglesLeft: IconType;
export declare const FaAnglesRight: IconType;
export declare const FaAnglesUp: IconType;
export declare const FaAnkh: IconType;
export declare const FaAppleWhole: IconType;
export declare const FaArchway: IconType;
export declare const FaArrowDown19: IconType;
export declare const FaArrowDown91: IconType;
export declare const FaArrowDownAZ: IconType;
export declare const FaArrowDownLong: IconType;
export declare const FaArrowDownShortWide: IconType;
export declare const FaArrowDownUpAcrossLine: IconType;
export declare const FaArrowDownUpLock: IconType;
export declare const FaArrowDownWideShort: IconType;
export declare const FaArrowDownZA: IconType;
export declare const FaArrowDown: IconType;
export declare const FaArrowLeftLong: IconType;
export declare const FaArrowLeft: IconType;
export declare const FaArrowPointer: IconType;
export declare const FaArrowRightArrowLeft: IconType;
export declare const FaArrowRightFromBracket: IconType;
export declare const FaArrowRightLong: IconType;
export declare const FaArrowRightToBracket: IconType;
export declare const FaArrowRightToCity: IconType;
export declare const FaArrowRight: IconType;
export declare const FaArrowRotateLeft: IconType;
export declare const FaArrowRotateRight: IconType;
export declare const FaArrowTrendDown: IconType;
export declare const FaArrowTrendUp: IconType;
export declare const FaArrowTurnDown: IconType;
export declare const FaArrowTurnUp: IconType;
export declare const FaArrowUp19: IconType;
export declare const FaArrowUp91: IconType;
export declare const FaArrowUpAZ: IconType;
export declare const FaArrowUpFromBracket: IconType;
export declare const FaArrowUpFromGroundWater: IconType;
export declare const FaArrowUpFromWaterPump: IconType;
export declare const FaArrowUpLong: IconType;
export declare const FaArrowUpRightDots: IconType;
export declare const FaArrowUpRightFromSquare: IconType;
export declare const FaArrowUpShortWide: IconType;
export declare const FaArrowUpWideShort: IconType;
export declare const FaArrowUpZA: IconType;
export declare const FaArrowUp: IconType;
export declare const FaArrowsDownToLine: IconType;
export declare const FaArrowsDownToPeople: IconType;
export declare const FaArrowsLeftRightToLine: IconType;
export declare const FaArrowsLeftRight: IconType;
export declare const FaArrowsRotate: IconType;
export declare const FaArrowsSpin: IconType;
export declare const FaArrowsSplitUpAndLeft: IconType;
export declare const FaArrowsToCircle: IconType;
export declare const FaArrowsToDot: IconType;
export declare const FaArrowsToEye: IconType;
export declare const FaArrowsTurnRight: IconType;
export declare const FaArrowsTurnToDots: IconType;
export declare const FaArrowsUpDownLeftRight: IconType;
export declare const FaArrowsUpDown: IconType;
export declare const FaArrowsUpToLine: IconType;
export declare const FaAsterisk: IconType;
export declare const FaAt: IconType;
export declare const FaAtom: IconType;
export declare const FaAudioDescription: IconType;
export declare const FaAustralSign: IconType;
export declare const FaAward: IconType;
export declare const FaB: IconType;
export declare const FaBabyCarriage: IconType;
export declare const FaBaby: IconType;
export declare const FaBackwardFast: IconType;
export declare const FaBackwardStep: IconType;
export declare const FaBackward: IconType;
export declare const FaBacon: IconType;
export declare const FaBacteria: IconType;
export declare const FaBacterium: IconType;
export declare const FaBagShopping: IconType;
export declare const FaBahai: IconType;
export declare const FaBahtSign: IconType;
export declare const FaBanSmoking: IconType;
export declare const FaBan: IconType;
export declare const FaBandage: IconType;
export declare const FaBangladeshiTakaSign: IconType;
export declare const FaBarcode: IconType;
export declare const FaBarsProgress: IconType;
export declare const FaBarsStaggered: IconType;
export declare const FaBars: IconType;
export declare const FaBaseballBatBall: IconType;
export declare const FaBaseball: IconType;
export declare const FaBasketShopping: IconType;
export declare const FaBasketball: IconType;
export declare const FaBath: IconType;
export declare const FaBatteryEmpty: IconType;
export declare const FaBatteryFull: IconType;
export declare const FaBatteryHalf: IconType;
export declare const FaBatteryQuarter: IconType;
export declare const FaBatteryThreeQuarters: IconType;
export declare const FaBedPulse: IconType;
export declare const FaBed: IconType;
export declare const FaBeerMugEmpty: IconType;
export declare const FaBellConcierge: IconType;
export declare const FaBellSlash: IconType;
export declare const FaBell: IconType;
export declare const FaBezierCurve: IconType;
export declare const FaBicycle: IconType;
export declare const FaBinoculars: IconType;
export declare const FaBiohazard: IconType;
export declare const FaBitcoinSign: IconType;
export declare const FaBlenderPhone: IconType;
export declare const FaBlender: IconType;
export declare const FaBlog: IconType;
export declare const FaBold: IconType;
export declare const FaBoltLightning: IconType;
export declare const FaBolt: IconType;
export declare const FaBomb: IconType;
export declare const FaBone: IconType;
export declare const FaBong: IconType;
export declare const FaBookAtlas: IconType;
export declare const FaBookBible: IconType;
export declare const FaBookBookmark: IconType;
export declare const FaBookJournalWhills: IconType;
export declare const FaBookMedical: IconType;
export declare const FaBookOpenReader: IconType;
export declare const FaBookOpen: IconType;
export declare const FaBookQuran: IconType;
export declare const FaBookSkull: IconType;
export declare const FaBookTanakh: IconType;
export declare const FaBook: IconType;
export declare const FaBookmark: IconType;
export declare const FaBorderAll: IconType;
export declare const FaBorderNone: IconType;
export declare const FaBorderTopLeft: IconType;
export declare const FaBoreHole: IconType;
export declare const FaBottleDroplet: IconType;
export declare const FaBottleWater: IconType;
export declare const FaBowlFood: IconType;
export declare const FaBowlRice: IconType;
export declare const FaBowlingBall: IconType;
export declare const FaBoxArchive: IconType;
export declare const FaBoxOpen: IconType;
export declare const FaBoxTissue: IconType;
export declare const FaBox: IconType;
export declare const FaBoxesPacking: IconType;
export declare const FaBoxesStacked: IconType;
export declare const FaBraille: IconType;
export declare const FaBrain: IconType;
export declare const FaBrazilianRealSign: IconType;
export declare const FaBreadSlice: IconType;
export declare const FaBridgeCircleCheck: IconType;
export declare const FaBridgeCircleExclamation: IconType;
export declare const FaBridgeCircleXmark: IconType;
export declare const FaBridgeLock: IconType;
export declare const FaBridgeWater: IconType;
export declare const FaBridge: IconType;
export declare const FaBriefcaseMedical: IconType;
export declare const FaBriefcase: IconType;
export declare const FaBroomBall: IconType;
export declare const FaBroom: IconType;
export declare const FaBrush: IconType;
export declare const FaBucket: IconType;
export declare const FaBugSlash: IconType;
export declare const FaBug: IconType;
export declare const FaBugs: IconType;
export declare const FaBuildingCircleArrowRight: IconType;
export declare const FaBuildingCircleCheck: IconType;
export declare const FaBuildingCircleExclamation: IconType;
export declare const FaBuildingCircleXmark: IconType;
export declare const FaBuildingColumns: IconType;
export declare const FaBuildingFlag: IconType;
export declare const FaBuildingLock: IconType;
export declare const FaBuildingNgo: IconType;
export declare const FaBuildingShield: IconType;
export declare const FaBuildingUn: IconType;
export declare const FaBuildingUser: IconType;
export declare const FaBuildingWheat: IconType;
export declare const FaBuilding: IconType;
export declare const FaBullhorn: IconType;
export declare const FaBullseye: IconType;
export declare const FaBurger: IconType;
export declare const FaBurst: IconType;
export declare const FaBusSimple: IconType;
export declare const FaBus: IconType;
export declare const FaBusinessTime: IconType;
export declare const FaC: IconType;
export declare const FaCableCar: IconType;
export declare const FaCakeCandles: IconType;
export declare const FaCalculator: IconType;
export declare const FaCalendarCheck: IconType;
export declare const FaCalendarDay: IconType;
export declare const FaCalendarDays: IconType;
export declare const FaCalendarMinus: IconType;
export declare const FaCalendarPlus: IconType;
export declare const FaCalendarWeek: IconType;
export declare const FaCalendarXmark: IconType;
export declare const FaCalendar: IconType;
export declare const FaCameraRetro: IconType;
export declare const FaCameraRotate: IconType;
export declare const FaCamera: IconType;
export declare const FaCampground: IconType;
export declare const FaCandyCane: IconType;
export declare const FaCannabis: IconType;
export declare const FaCapsules: IconType;
export declare const FaCarBattery: IconType;
export declare const FaCarBurst: IconType;
export declare const FaCarOn: IconType;
export declare const FaCarRear: IconType;
export declare const FaCarSide: IconType;
export declare const FaCarTunnel: IconType;
export declare const FaCar: IconType;
export declare const FaCaravan: IconType;
export declare const FaCaretDown: IconType;
export declare const FaCaretLeft: IconType;
export declare const FaCaretRight: IconType;
export declare const FaCaretUp: IconType;
export declare const FaCarrot: IconType;
export declare const FaCartArrowDown: IconType;
export declare const FaCartFlatbedSuitcase: IconType;
export declare const FaCartFlatbed: IconType;
export declare const FaCartPlus: IconType;
export declare const FaCartShopping: IconType;
export declare const FaCashRegister: IconType;
export declare const FaCat: IconType;
export declare const FaCediSign: IconType;
export declare const FaCentSign: IconType;
export declare const FaCertificate: IconType;
export declare const FaChair: IconType;
export declare const FaChalkboardUser: IconType;
export declare const FaChalkboard: IconType;
export declare const FaChampagneGlasses: IconType;
export declare const FaChargingStation: IconType;
export declare const FaChartArea: IconType;
export declare const FaChartBar: IconType;
export declare const FaChartColumn: IconType;
export declare const FaChartGantt: IconType;
export declare const FaChartLine: IconType;
export declare const FaChartPie: IconType;
export declare const FaChartSimple: IconType;
export declare const FaCheckDouble: IconType;
export declare const FaCheckToSlot: IconType;
export declare const FaCheck: IconType;
export declare const FaCheese: IconType;
export declare const FaChessBishop: IconType;
export declare const FaChessBoard: IconType;
export declare const FaChessKing: IconType;
export declare const FaChessKnight: IconType;
export declare const FaChessPawn: IconType;
export declare const FaChessQueen: IconType;
export declare const FaChessRook: IconType;
export declare const FaChess: IconType;
export declare const FaChevronDown: IconType;
export declare const FaChevronLeft: IconType;
export declare const FaChevronRight: IconType;
export declare const FaChevronUp: IconType;
export declare const FaChildCombatant: IconType;
export declare const FaChildDress: IconType;
export declare const FaChildReaching: IconType;
export declare const FaChild: IconType;
export declare const FaChildren: IconType;
export declare const FaChurch: IconType;
export declare const FaCircleArrowDown: IconType;
export declare const FaCircleArrowLeft: IconType;
export declare const FaCircleArrowRight: IconType;
export declare const FaCircleArrowUp: IconType;
export declare const FaCircleCheck: IconType;
export declare const FaCircleChevronDown: IconType;
export declare const FaCircleChevronLeft: IconType;
export declare const FaCircleChevronRight: IconType;
export declare const FaCircleChevronUp: IconType;
export declare const FaCircleDollarToSlot: IconType;
export declare const FaCircleDot: IconType;
export declare const FaCircleDown: IconType;
export declare const FaCircleExclamation: IconType;
export declare const FaCircleH: IconType;
export declare const FaCircleHalfStroke: IconType;
export declare const FaCircleInfo: IconType;
export declare const FaCircleLeft: IconType;
export declare const FaCircleMinus: IconType;
export declare const FaCircleNodes: IconType;
export declare const FaCircleNotch: IconType;
export declare const FaCirclePause: IconType;
export declare const FaCirclePlay: IconType;
export declare const FaCirclePlus: IconType;
export declare const FaCircleQuestion: IconType;
export declare const FaCircleRadiation: IconType;
export declare const FaCircleRight: IconType;
export declare const FaCircleStop: IconType;
export declare const FaCircleUp: IconType;
export declare const FaCircleUser: IconType;
export declare const FaCircleXmark: IconType;
export declare const FaCircle: IconType;
export declare const FaCity: IconType;
export declare const FaClapperboard: IconType;
export declare const FaClipboardCheck: IconType;
export declare const FaClipboardList: IconType;
export declare const FaClipboardQuestion: IconType;
export declare const FaClipboardUser: IconType;
export declare const FaClipboard: IconType;
export declare const FaClockRotateLeft: IconType;
export declare const FaClock: IconType;
export declare const FaClone: IconType;
export declare const FaClosedCaptioning: IconType;
export declare const FaCloudArrowDown: IconType;
export declare const FaCloudArrowUp: IconType;
export declare const FaCloudBolt: IconType;
export declare const FaCloudMeatball: IconType;
export declare const FaCloudMoonRain: IconType;
export declare const FaCloudMoon: IconType;
export declare const FaCloudRain: IconType;
export declare const FaCloudShowersHeavy: IconType;
export declare const FaCloudShowersWater: IconType;
export declare const FaCloudSunRain: IconType;
export declare const FaCloudSun: IconType;
export declare const FaCloud: IconType;
export declare const FaClover: IconType;
export declare const FaCodeBranch: IconType;
export declare const FaCodeCommit: IconType;
export declare const FaCodeCompare: IconType;
export declare const FaCodeFork: IconType;
export declare const FaCodeMerge: IconType;
export declare const FaCodePullRequest: IconType;
export declare const FaCode: IconType;
export declare const FaCoins: IconType;
export declare const FaColonSign: IconType;
export declare const FaCommentDollar: IconType;
export declare const FaCommentDots: IconType;
export declare const FaCommentMedical: IconType;
export declare const FaCommentSlash: IconType;
export declare const FaCommentSms: IconType;
export declare const FaComment: IconType;
export declare const FaCommentsDollar: IconType;
export declare const FaComments: IconType;
export declare const FaCompactDisc: IconType;
export declare const FaCompassDrafting: IconType;
export declare const FaCompass: IconType;
export declare const FaCompress: IconType;
export declare const FaComputerMouse: IconType;
export declare const FaComputer: IconType;
export declare const FaCookieBite: IconType;
export declare const FaCookie: IconType;
export declare const FaCopy: IconType;
export declare const FaCopyright: IconType;
export declare const FaCouch: IconType;
export declare const FaCow: IconType;
export declare const FaCreditCard: IconType;
export declare const FaCropSimple: IconType;
export declare const FaCrop: IconType;
export declare const FaCross: IconType;
export declare const FaCrosshairs: IconType;
export declare const FaCrow: IconType;
export declare const FaCrown: IconType;
export declare const FaCrutch: IconType;
export declare const FaCruzeiroSign: IconType;
export declare const FaCube: IconType;
export declare const FaCubesStacked: IconType;
export declare const FaCubes: IconType;
export declare const FaD: IconType;
export declare const FaDatabase: IconType;
export declare const FaDeleteLeft: IconType;
export declare const FaDemocrat: IconType;
export declare const FaDesktop: IconType;
export declare const FaDharmachakra: IconType;
export declare const FaDiagramNext: IconType;
export declare const FaDiagramPredecessor: IconType;
export declare const FaDiagramProject: IconType;
export declare const FaDiagramSuccessor: IconType;
export declare const FaDiamondTurnRight: IconType;
export declare const FaDiamond: IconType;
export declare const FaDiceD20: IconType;
export declare const FaDiceD6: IconType;
export declare const FaDiceFive: IconType;
export declare const FaDiceFour: IconType;
export declare const FaDiceOne: IconType;
export declare const FaDiceSix: IconType;
export declare const FaDiceThree: IconType;
export declare const FaDiceTwo: IconType;
export declare const FaDice: IconType;
export declare const FaDisease: IconType;
export declare const FaDisplay: IconType;
export declare const FaDivide: IconType;
export declare const FaDna: IconType;
export declare const FaDog: IconType;
export declare const FaDollarSign: IconType;
export declare const FaDolly: IconType;
export declare const FaDongSign: IconType;
export declare const FaDoorClosed: IconType;
export declare const FaDoorOpen: IconType;
export declare const FaDove: IconType;
export declare const FaDownLeftAndUpRightToCenter: IconType;
export declare const FaDownLong: IconType;
export declare const FaDownload: IconType;
export declare const FaDragon: IconType;
export declare const FaDrawPolygon: IconType;
export declare const FaDropletSlash: IconType;
export declare const FaDroplet: IconType;
export declare const FaDrumSteelpan: IconType;
export declare const FaDrum: IconType;
export declare const FaDrumstickBite: IconType;
export declare const FaDumbbell: IconType;
export declare const FaDumpsterFire: IconType;
export declare const FaDumpster: IconType;
export declare const FaDungeon: IconType;
export declare const FaE: IconType;
export declare const FaEarDeaf: IconType;
export declare const FaEarListen: IconType;
export declare const FaEarthAfrica: IconType;
export declare const FaEarthAmericas: IconType;
export declare const FaEarthAsia: IconType;
export declare const FaEarthEurope: IconType;
export declare const FaEarthOceania: IconType;
export declare const FaEgg: IconType;
export declare const FaEject: IconType;
export declare const FaElevator: IconType;
export declare const FaEllipsisVertical: IconType;
export declare const FaEllipsis: IconType;
export declare const FaEnvelopeCircleCheck: IconType;
export declare const FaEnvelopeOpenText: IconType;
export declare const FaEnvelopeOpen: IconType;
export declare const FaEnvelope: IconType;
export declare const FaEnvelopesBulk: IconType;
export declare const FaEquals: IconType;
export declare const FaEraser: IconType;
export declare const FaEthernet: IconType;
export declare const FaEuroSign: IconType;
export declare const FaExclamation: IconType;
export declare const FaExpand: IconType;
export declare const FaExplosion: IconType;
export declare const FaEyeDropper: IconType;
export declare const FaEyeLowVision: IconType;
export declare const FaEyeSlash: IconType;
export declare const FaEye: IconType;
export declare const FaF: IconType;
export declare const FaFaceAngry: IconType;
export declare const FaFaceDizzy: IconType;
export declare const FaFaceFlushed: IconType;
export declare const FaFaceFrownOpen: IconType;
export declare const FaFaceFrown: IconType;
export declare const FaFaceGrimace: IconType;
export declare const FaFaceGrinBeamSweat: IconType;
export declare const FaFaceGrinBeam: IconType;
export declare const FaFaceGrinHearts: IconType;
export declare const FaFaceGrinSquintTears: IconType;
export declare const FaFaceGrinSquint: IconType;
export declare const FaFaceGrinStars: IconType;
export declare const FaFaceGrinTears: IconType;
export declare const FaFaceGrinTongueSquint: IconType;
export declare const FaFaceGrinTongueWink: IconType;
export declare const FaFaceGrinTongue: IconType;
export declare const FaFaceGrinWide: IconType;
export declare const FaFaceGrinWink: IconType;
export declare const FaFaceGrin: IconType;
export declare const FaFaceKissBeam: IconType;
export declare const FaFaceKissWinkHeart: IconType;
export declare const FaFaceKiss: IconType;
export declare const FaFaceLaughBeam: IconType;
export declare const FaFaceLaughSquint: IconType;
export declare const FaFaceLaughWink: IconType;
export declare const FaFaceLaugh: IconType;
export declare const FaFaceMehBlank: IconType;
export declare const FaFaceMeh: IconType;
export declare const FaFaceRollingEyes: IconType;
export declare const FaFaceSadCry: IconType;
export declare const FaFaceSadTear: IconType;
export declare const FaFaceSmileBeam: IconType;
export declare const FaFaceSmileWink: IconType;
export declare const FaFaceSmile: IconType;
export declare const FaFaceSurprise: IconType;
export declare const FaFaceTired: IconType;
export declare const FaFan: IconType;
export declare const FaFaucetDrip: IconType;
export declare const FaFaucet: IconType;
export declare const FaFax: IconType;
export declare const FaFeatherPointed: IconType;
export declare const FaFeather: IconType;
export declare const FaFerry: IconType;
export declare const FaFileArrowDown: IconType;
export declare const FaFileArrowUp: IconType;
export declare const FaFileAudio: IconType;
export declare const FaFileCircleCheck: IconType;
export declare const FaFileCircleExclamation: IconType;
export declare const FaFileCircleMinus: IconType;
export declare const FaFileCirclePlus: IconType;
export declare const FaFileCircleQuestion: IconType;
export declare const FaFileCircleXmark: IconType;
export declare const FaFileCode: IconType;
export declare const FaFileContract: IconType;
export declare const FaFileCsv: IconType;
export declare const FaFileExcel: IconType;
export declare const FaFileExport: IconType;
export declare const FaFileImage: IconType;
export declare const FaFileImport: IconType;
export declare const FaFileInvoiceDollar: IconType;
export declare const FaFileInvoice: IconType;
export declare const FaFileLines: IconType;
export declare const FaFileMedical: IconType;
export declare const FaFilePdf: IconType;
export declare const FaFilePen: IconType;
export declare const FaFilePowerpoint: IconType;
export declare const FaFilePrescription: IconType;
export declare const FaFileShield: IconType;
export declare const FaFileSignature: IconType;
export declare const FaFileVideo: IconType;
export declare const FaFileWaveform: IconType;
export declare const FaFileWord: IconType;
export declare const FaFileZipper: IconType;
export declare const FaFile: IconType;
export declare const FaFillDrip: IconType;
export declare const FaFill: IconType;
export declare const FaFilm: IconType;
export declare const FaFilterCircleDollar: IconType;
export declare const FaFilterCircleXmark: IconType;
export declare const FaFilter: IconType;
export declare const FaFingerprint: IconType;
export declare const FaFireBurner: IconType;
export declare const FaFireExtinguisher: IconType;
export declare const FaFireFlameCurved: IconType;
export declare const FaFireFlameSimple: IconType;
export declare const FaFire: IconType;
export declare const FaFishFins: IconType;
export declare const FaFish: IconType;
export declare const FaFlagCheckered: IconType;
export declare const FaFlagUsa: IconType;
export declare const FaFlag: IconType;
export declare const FaFlaskVial: IconType;
export declare const FaFlask: IconType;
export declare const FaFloppyDisk: IconType;
export declare const FaFlorinSign: IconType;
export declare const FaFolderClosed: IconType;
export declare const FaFolderMinus: IconType;
export declare const FaFolderOpen: IconType;
export declare const FaFolderPlus: IconType;
export declare const FaFolderTree: IconType;
export declare const FaFolder: IconType;
export declare const FaFont: IconType;
export declare const FaFootball: IconType;
export declare const FaForwardFast: IconType;
export declare const FaForwardStep: IconType;
export declare const FaForward: IconType;
export declare const FaFrancSign: IconType;
export declare const FaFrog: IconType;
export declare const FaFutbol: IconType;
export declare const FaG: IconType;
export declare const FaGamepad: IconType;
export declare const FaGasPump: IconType;
export declare const FaGaugeHigh: IconType;
export declare const FaGaugeSimpleHigh: IconType;
export declare const FaGaugeSimple: IconType;
export declare const FaGauge: IconType;
export declare const FaGavel: IconType;
export declare const FaGear: IconType;
export declare const FaGears: IconType;
export declare const FaGem: IconType;
export declare const FaGenderless: IconType;
export declare const FaGhost: IconType;
export declare const FaGift: IconType;
export declare const FaGifts: IconType;
export declare const FaGlassWaterDroplet: IconType;
export declare const FaGlassWater: IconType;
export declare const FaGlasses: IconType;
export declare const FaGlobe: IconType;
export declare const FaGolfBallTee: IconType;
export declare const FaGopuram: IconType;
export declare const FaGraduationCap: IconType;
export declare const FaGreaterThanEqual: IconType;
export declare const FaGreaterThan: IconType;
export declare const FaGripLinesVertical: IconType;
export declare const FaGripLines: IconType;
export declare const FaGripVertical: IconType;
export declare const FaGrip: IconType;
export declare const FaGroupArrowsRotate: IconType;
export declare const FaGuaraniSign: IconType;
export declare const FaGuitar: IconType;
export declare const FaGun: IconType;
export declare const FaH: IconType;
export declare const FaHammer: IconType;
export declare const FaHamsa: IconType;
export declare const FaHandBackFist: IconType;
export declare const FaHandDots: IconType;
export declare const FaHandFist: IconType;
export declare const FaHandHoldingDollar: IconType;
export declare const FaHandHoldingDroplet: IconType;
export declare const FaHandHoldingHand: IconType;
export declare const FaHandHoldingHeart: IconType;
export declare const FaHandHoldingMedical: IconType;
export declare const FaHandHolding: IconType;
export declare const FaHandLizard: IconType;
export declare const FaHandMiddleFinger: IconType;
export declare const FaHandPeace: IconType;
export declare const FaHandPointDown: IconType;
export declare const FaHandPointLeft: IconType;
export declare const FaHandPointRight: IconType;
export declare const FaHandPointUp: IconType;
export declare const FaHandPointer: IconType;
export declare const FaHandScissors: IconType;
export declare const FaHandSparkles: IconType;
export declare const FaHandSpock: IconType;
export declare const FaHand: IconType;
export declare const FaHandcuffs: IconType;
export declare const FaHandsAslInterpreting: IconType;
export declare const FaHandsBound: IconType;
export declare const FaHandsBubbles: IconType;
export declare const FaHandsClapping: IconType;
export declare const FaHandsHoldingChild: IconType;
export declare const FaHandsHoldingCircle: IconType;
export declare const FaHandsHolding: IconType;
export declare const FaHandsPraying: IconType;
export declare const FaHands: IconType;
export declare const FaHandshakeAngle: IconType;
export declare const FaHandshakeSimpleSlash: IconType;
export declare const FaHandshakeSimple: IconType;
export declare const FaHandshakeSlash: IconType;
export declare const FaHandshake: IconType;
export declare const FaHanukiah: IconType;
export declare const FaHardDrive: IconType;
export declare const FaHashtag: IconType;
export declare const FaHatCowboySide: IconType;
export declare const FaHatCowboy: IconType;
export declare const FaHatWizard: IconType;
export declare const FaHeadSideCoughSlash: IconType;
export declare const FaHeadSideCough: IconType;
export declare const FaHeadSideMask: IconType;
export declare const FaHeadSideVirus: IconType;
export declare const FaHeading: IconType;
export declare const FaHeadphonesSimple: IconType;
export declare const FaHeadphones: IconType;
export declare const FaHeadset: IconType;
export declare const FaHeartCircleBolt: IconType;
export declare const FaHeartCircleCheck: IconType;
export declare const FaHeartCircleExclamation: IconType;
export declare const FaHeartCircleMinus: IconType;
export declare const FaHeartCirclePlus: IconType;
export declare const FaHeartCircleXmark: IconType;
export declare const FaHeartCrack: IconType;
export declare const FaHeartPulse: IconType;
export declare const FaHeart: IconType;
export declare const FaHelicopterSymbol: IconType;
export declare const FaHelicopter: IconType;
export declare const FaHelmetSafety: IconType;
export declare const FaHelmetUn: IconType;
export declare const FaHighlighter: IconType;
export declare const FaHillAvalanche: IconType;
export declare const FaHillRockslide: IconType;
export declare const FaHippo: IconType;
export declare const FaHockeyPuck: IconType;
export declare const FaHollyBerry: IconType;
export declare const FaHorseHead: IconType;
export declare const FaHorse: IconType;
export declare const FaHospitalUser: IconType;
export declare const FaHospital: IconType;
export declare const FaHotTubPerson: IconType;
export declare const FaHotdog: IconType;
export declare const FaHotel: IconType;
export declare const FaHourglassEnd: IconType;
export declare const FaHourglassHalf: IconType;
export declare const FaHourglassStart: IconType;
export declare const FaHourglass: IconType;
export declare const FaHouseChimneyCrack: IconType;
export declare const FaHouseChimneyMedical: IconType;
export declare const FaHouseChimneyUser: IconType;
export declare const FaHouseChimneyWindow: IconType;
export declare const FaHouseChimney: IconType;
export declare const FaHouseCircleCheck: IconType;
export declare const FaHouseCircleExclamation: IconType;
export declare const FaHouseCircleXmark: IconType;
export declare const FaHouseCrack: IconType;
export declare const FaHouseFire: IconType;
export declare const FaHouseFlag: IconType;
export declare const FaHouseFloodWaterCircleArrowRight: IconType;
export declare const FaHouseFloodWater: IconType;
export declare const FaHouseLaptop: IconType;
export declare const FaHouseLock: IconType;
export declare const FaHouseMedicalCircleCheck: IconType;
export declare const FaHouseMedicalCircleExclamation: IconType;
export declare const FaHouseMedicalCircleXmark: IconType;
export declare const FaHouseMedicalFlag: IconType;
export declare const FaHouseMedical: IconType;
export declare const FaHouseSignal: IconType;
export declare const FaHouseTsunami: IconType;
export declare const FaHouseUser: IconType;
export declare const FaHouse: IconType;
export declare const FaHryvniaSign: IconType;
export declare const FaHurricane: IconType;
export declare const FaICursor: IconType;
export declare const FaI: IconType;
export declare const FaIceCream: IconType;
export declare const FaIcicles: IconType;
export declare const FaIcons: IconType;
export declare const FaIdBadge: IconType;
export declare const FaIdCardClip: IconType;
export declare const FaIdCard: IconType;
export declare const FaIgloo: IconType;
export declare const FaImagePortrait: IconType;
export declare const FaImage: IconType;
export declare const FaImages: IconType;
export declare const FaInbox: IconType;
export declare const FaIndent: IconType;
export declare const FaIndianRupeeSign: IconType;
export declare const FaIndustry: IconType;
export declare const FaInfinity: IconType;
export declare const FaInfo: IconType;
export declare const FaItalic: IconType;
export declare const FaJ: IconType;
export declare const FaJarWheat: IconType;
export declare const FaJar: IconType;
export declare const FaJedi: IconType;
export declare const FaJetFighterUp: IconType;
export declare const FaJetFighter: IconType;
export declare const FaJoint: IconType;
export declare const FaJugDetergent: IconType;
export declare const FaK: IconType;
export declare const FaKaaba: IconType;
export declare const FaKey: IconType;
export declare const FaKeyboard: IconType;
export declare const FaKhanda: IconType;
export declare const FaKipSign: IconType;
export declare const FaKitMedical: IconType;
export declare const FaKitchenSet: IconType;
export declare const FaKiwiBird: IconType;
export declare const FaL: IconType;
export declare const FaLandMineOn: IconType;
export declare const FaLandmarkDome: IconType;
export declare const FaLandmarkFlag: IconType;
export declare const FaLandmark: IconType;
export declare const FaLanguage: IconType;
export declare const FaLaptopCode: IconType;
export declare const FaLaptopFile: IconType;
export declare const FaLaptopMedical: IconType;
export declare const FaLaptop: IconType;
export declare const FaLariSign: IconType;
export declare const FaLayerGroup: IconType;
export declare const FaLeaf: IconType;
export declare const FaLeftLong: IconType;
export declare const FaLeftRight: IconType;
export declare const FaLemon: IconType;
export declare const FaLessThanEqual: IconType;
export declare const FaLessThan: IconType;
export declare const FaLifeRing: IconType;
export declare const FaLightbulb: IconType;
export declare const FaLinesLeaning: IconType;
export declare const FaLinkSlash: IconType;
export declare const FaLink: IconType;
export declare const FaLiraSign: IconType;
export declare const FaListCheck: IconType;
export declare const FaListOl: IconType;
export declare const FaListUl: IconType;
export declare const FaList: IconType;
export declare const FaLitecoinSign: IconType;
export declare const FaLocationArrow: IconType;
export declare const FaLocationCrosshairs: IconType;
export declare const FaLocationDot: IconType;
export declare const FaLocationPinLock: IconType;
export declare const FaLocationPin: IconType;
export declare const FaLockOpen: IconType;
export declare const FaLock: IconType;
export declare const FaLocust: IconType;
export declare const FaLungsVirus: IconType;
export declare const FaLungs: IconType;
export declare const FaM: IconType;
export declare const FaMagnet: IconType;
export declare const FaMagnifyingGlassArrowRight: IconType;
export declare const FaMagnifyingGlassChart: IconType;
export declare const FaMagnifyingGlassDollar: IconType;
export declare const FaMagnifyingGlassLocation: IconType;
export declare const FaMagnifyingGlassMinus: IconType;
export declare const FaMagnifyingGlassPlus: IconType;
export declare const FaMagnifyingGlass: IconType;
export declare const FaManatSign: IconType;
export declare const FaMapLocationDot: IconType;
export declare const FaMapLocation: IconType;
export declare const FaMapPin: IconType;
export declare const FaMap: IconType;
export declare const FaMarker: IconType;
export declare const FaMarsAndVenusBurst: IconType;
export declare const FaMarsAndVenus: IconType;
export declare const FaMarsDouble: IconType;
export declare const FaMarsStrokeRight: IconType;
export declare const FaMarsStrokeUp: IconType;
export declare const FaMarsStroke: IconType;
export declare const FaMars: IconType;
export declare const FaMartiniGlassCitrus: IconType;
export declare const FaMartiniGlassEmpty: IconType;
export declare const FaMartiniGlass: IconType;
export declare const FaMaskFace: IconType;
export declare const FaMaskVentilator: IconType;
export declare const FaMask: IconType;
export declare const FaMasksTheater: IconType;
export declare const FaMattressPillow: IconType;
export declare const FaMaximize: IconType;
export declare const FaMedal: IconType;
export declare const FaMemory: IconType;
export declare const FaMenorah: IconType;
export declare const FaMercury: IconType;
export declare const FaMessage: IconType;
export declare const FaMeteor: IconType;
export declare const FaMicrochip: IconType;
export declare const FaMicrophoneLinesSlash: IconType;
export declare const FaMicrophoneLines: IconType;
export declare const FaMicrophoneSlash: IconType;
export declare const FaMicrophone: IconType;
export declare const FaMicroscope: IconType;
export declare const FaMillSign: IconType;
export declare const FaMinimize: IconType;
export declare const FaMinus: IconType;
export declare const FaMitten: IconType;
export declare const FaMobileButton: IconType;
export declare const FaMobileRetro: IconType;
export declare const FaMobileScreenButton: IconType;
export declare const FaMobileScreen: IconType;
export declare const FaMobile: IconType;
export declare const FaMoneyBill1Wave: IconType;
export declare const FaMoneyBill1: IconType;
export declare const FaMoneyBillTransfer: IconType;
export declare const FaMoneyBillTrendUp: IconType;
export declare const FaMoneyBillWave: IconType;
export declare const FaMoneyBillWheat: IconType;
export declare const FaMoneyBill: IconType;
export declare const FaMoneyBills: IconType;
export declare const FaMoneyCheckDollar: IconType;
export declare const FaMoneyCheck: IconType;
export declare const FaMonument: IconType;
export declare const FaMoon: IconType;
export declare const FaMortarPestle: IconType;
export declare const FaMosque: IconType;
export declare const FaMosquitoNet: IconType;
export declare const FaMosquito: IconType;
export declare const FaMotorcycle: IconType;
export declare const FaMound: IconType;
export declare const FaMountainCity: IconType;
export declare const FaMountainSun: IconType;
export declare const FaMountain: IconType;
export declare const FaMugHot: IconType;
export declare const FaMugSaucer: IconType;
export declare const FaMusic: IconType;
export declare const FaN: IconType;
export declare const FaNairaSign: IconType;
export declare const FaNetworkWired: IconType;
export declare const FaNeuter: IconType;
export declare const FaNewspaper: IconType;
export declare const FaNotEqual: IconType;
export declare const FaNotdef: IconType;
export declare const FaNoteSticky: IconType;
export declare const FaNotesMedical: IconType;
export declare const FaO: IconType;
export declare const FaObjectGroup: IconType;
export declare const FaObjectUngroup: IconType;
export declare const FaOilCan: IconType;
export declare const FaOilWell: IconType;
export declare const FaOm: IconType;
export declare const FaOtter: IconType;
export declare const FaOutdent: IconType;
export declare const FaP: IconType;
export declare const FaPager: IconType;
export declare const FaPaintRoller: IconType;
export declare const FaPaintbrush: IconType;
export declare const FaPalette: IconType;
export declare const FaPallet: IconType;
export declare const FaPanorama: IconType;
export declare const FaPaperPlane: IconType;
export declare const FaPaperclip: IconType;
export declare const FaParachuteBox: IconType;
export declare const FaParagraph: IconType;
export declare const FaPassport: IconType;
export declare const FaPaste: IconType;
export declare const FaPause: IconType;
export declare const FaPaw: IconType;
export declare const FaPeace: IconType;
export declare const FaPenClip: IconType;
export declare const FaPenFancy: IconType;
export declare const FaPenNib: IconType;
export declare const FaPenRuler: IconType;
export declare const FaPenToSquare: IconType;
export declare const FaPen: IconType;
export declare const FaPencil: IconType;
export declare const FaPeopleArrows: IconType;
export declare const FaPeopleCarryBox: IconType;
export declare const FaPeopleGroup: IconType;
export declare const FaPeopleLine: IconType;
export declare const FaPeoplePulling: IconType;
export declare const FaPeopleRobbery: IconType;
export declare const FaPeopleRoof: IconType;
export declare const FaPepperHot: IconType;
export declare const FaPercent: IconType;
export declare const FaPersonArrowDownToLine: IconType;
export declare const FaPersonArrowUpFromLine: IconType;
export declare const FaPersonBiking: IconType;
export declare const FaPersonBooth: IconType;
export declare const FaPersonBreastfeeding: IconType;
export declare const FaPersonBurst: IconType;
export declare const FaPersonCane: IconType;
export declare const FaPersonChalkboard: IconType;
export declare const FaPersonCircleCheck: IconType;
export declare const FaPersonCircleExclamation: IconType;
export declare const FaPersonCircleMinus: IconType;
export declare const FaPersonCirclePlus: IconType;
export declare const FaPersonCircleQuestion: IconType;
export declare const FaPersonCircleXmark: IconType;
export declare const FaPersonDigging: IconType;
export declare const FaPersonDotsFromLine: IconType;
export declare const FaPersonDressBurst: IconType;
export declare const FaPersonDress: IconType;
export declare const FaPersonDrowning: IconType;
export declare const FaPersonFallingBurst: IconType;
export declare const FaPersonFalling: IconType;
export declare const FaPersonHalfDress: IconType;
export declare const FaPersonHarassing: IconType;
export declare const FaPersonHiking: IconType;
export declare const FaPersonMilitaryPointing: IconType;
export declare const FaPersonMilitaryRifle: IconType;
export declare const FaPersonMilitaryToPerson: IconType;
export declare const FaPersonPraying: IconType;
export declare const FaPersonPregnant: IconType;
export declare const FaPersonRays: IconType;
export declare const FaPersonRifle: IconType;
export declare const FaPersonRunning: IconType;
export declare const FaPersonShelter: IconType;
export declare const FaPersonSkating: IconType;
export declare const FaPersonSkiingNordic: IconType;
export declare const FaPersonSkiing: IconType;
export declare const FaPersonSnowboarding: IconType;
export declare const FaPersonSwimming: IconType;
export declare const FaPersonThroughWindow: IconType;
export declare const FaPersonWalkingArrowLoopLeft: IconType;
export declare const FaPersonWalkingArrowRight: IconType;
export declare const FaPersonWalkingDashedLineArrowRight: IconType;
export declare const FaPersonWalkingLuggage: IconType;
export declare const FaPersonWalkingWithCane: IconType;
export declare const FaPersonWalking: IconType;
export declare const FaPerson: IconType;
export declare const FaPesetaSign: IconType;
export declare const FaPesoSign: IconType;
export declare const FaPhoneFlip: IconType;
export declare const FaPhoneSlash: IconType;
export declare const FaPhoneVolume: IconType;
export declare const FaPhone: IconType;
export declare const FaPhotoFilm: IconType;
export declare const FaPiggyBank: IconType;
export declare const FaPills: IconType;
export declare const FaPizzaSlice: IconType;
export declare const FaPlaceOfWorship: IconType;
export declare const FaPlaneArrival: IconType;
export declare const FaPlaneCircleCheck: IconType;
export declare const FaPlaneCircleExclamation: IconType;
export declare const FaPlaneCircleXmark: IconType;
export declare const FaPlaneDeparture: IconType;
export declare const FaPlaneLock: IconType;
export declare const FaPlaneSlash: IconType;
export declare const FaPlaneUp: IconType;
export declare const FaPlane: IconType;
export declare const FaPlantWilt: IconType;
export declare const FaPlateWheat: IconType;
export declare const FaPlay: IconType;
export declare const FaPlugCircleBolt: IconType;
export declare const FaPlugCircleCheck: IconType;
export declare const FaPlugCircleExclamation: IconType;
export declare const FaPlugCircleMinus: IconType;
export declare const FaPlugCirclePlus: IconType;
export declare const FaPlugCircleXmark: IconType;
export declare const FaPlug: IconType;
export declare const FaPlusMinus: IconType;
export declare const FaPlus: IconType;
export declare const FaPodcast: IconType;
export declare const FaPooStorm: IconType;
export declare const FaPoo: IconType;
export declare const FaPoop: IconType;
export declare const FaPowerOff: IconType;
export declare const FaPrescriptionBottleMedical: IconType;
export declare const FaPrescriptionBottle: IconType;
export declare const FaPrescription: IconType;
export declare const FaPrint: IconType;
export declare const FaPumpMedical: IconType;
export declare const FaPumpSoap: IconType;
export declare const FaPuzzlePiece: IconType;
export declare const FaQ: IconType;
export declare const FaQrcode: IconType;
export declare const FaQuestion: IconType;
export declare const FaQuoteLeft: IconType;
export declare const FaQuoteRight: IconType;
export declare const FaR: IconType;
export declare const FaRadiation: IconType;
export declare const FaRadio: IconType;
export declare const FaRainbow: IconType;
export declare const FaRankingStar: IconType;
export declare const FaReceipt: IconType;
export declare const FaRecordVinyl: IconType;
export declare const FaRectangleAd: IconType;
export declare const FaRectangleList: IconType;
export declare const FaRectangleXmark: IconType;
export declare const FaRecycle: IconType;
export declare const FaRegistered: IconType;
export declare const FaRepeat: IconType;
export declare const FaReplyAll: IconType;
export declare const FaReply: IconType;
export declare const FaRepublican: IconType;
export declare const FaRestroom: IconType;
export declare const FaRetweet: IconType;
export declare const FaRibbon: IconType;
export declare const FaRightFromBracket: IconType;
export declare const FaRightLeft: IconType;
export declare const FaRightLong: IconType;
export declare const FaRightToBracket: IconType;
export declare const FaRing: IconType;
export declare const FaRoadBarrier: IconType;
export declare const FaRoadBridge: IconType;
export declare const FaRoadCircleCheck: IconType;
export declare const FaRoadCircleExclamation: IconType;
export declare const FaRoadCircleXmark: IconType;
export declare const FaRoadLock: IconType;
export declare const FaRoadSpikes: IconType;
export declare const FaRoad: IconType;
export declare const FaRobot: IconType;
export declare const FaRocket: IconType;
export declare const FaRotateLeft: IconType;
export declare const FaRotateRight: IconType;
export declare const FaRotate: IconType;
export declare const FaRoute: IconType;
export declare const FaRss: IconType;
export declare const FaRubleSign: IconType;
export declare const FaRug: IconType;
export declare const FaRulerCombined: IconType;
export declare const FaRulerHorizontal: IconType;
export declare const FaRulerVertical: IconType;
export declare const FaRuler: IconType;
export declare const FaRupeeSign: IconType;
export declare const FaRupiahSign: IconType;
export declare const FaS: IconType;
export declare const FaSackDollar: IconType;
export declare const FaSackXmark: IconType;
export declare const FaSailboat: IconType;
export declare const FaSatelliteDish: IconType;
export declare const FaSatellite: IconType;
export declare const FaScaleBalanced: IconType;
export declare const FaScaleUnbalancedFlip: IconType;
export declare const FaScaleUnbalanced: IconType;
export declare const FaSchoolCircleCheck: IconType;
export declare const FaSchoolCircleExclamation: IconType;
export declare const FaSchoolCircleXmark: IconType;
export declare const FaSchoolFlag: IconType;
export declare const FaSchoolLock: IconType;
export declare const FaSchool: IconType;
export declare const FaScissors: IconType;
export declare const FaScrewdriverWrench: IconType;
export declare const FaScrewdriver: IconType;
export declare const FaScrollTorah: IconType;
export declare const FaScroll: IconType;
export declare const FaSdCard: IconType;
export declare const FaSection: IconType;
export declare const FaSeedling: IconType;
export declare const FaServer: IconType;
export declare const FaShapes: IconType;
export declare const FaShareFromSquare: IconType;
export declare const FaShareNodes: IconType;
export declare const FaShare: IconType;
export declare const FaSheetPlastic: IconType;
export declare const FaShekelSign: IconType;
export declare const FaShieldCat: IconType;
export declare const FaShieldDog: IconType;
export declare const FaShieldHalved: IconType;
export declare const FaShieldHeart: IconType;
export declare const FaShieldVirus: IconType;
export declare const FaShield: IconType;
export declare const FaShip: IconType;
export declare const FaShirt: IconType;
export declare const FaShoePrints: IconType;
export declare const FaShopLock: IconType;
export declare const FaShopSlash: IconType;
export declare const FaShop: IconType;
export declare const FaShower: IconType;
export declare const FaShrimp: IconType;
export declare const FaShuffle: IconType;
export declare const FaShuttleSpace: IconType;
export declare const FaSignHanging: IconType;
export declare const FaSignal: IconType;
export declare const FaSignature: IconType;
export declare const FaSignsPost: IconType;
export declare const FaSimCard: IconType;
export declare const FaSink: IconType;
export declare const FaSitemap: IconType;
export declare const FaSkullCrossbones: IconType;
export declare const FaSkull: IconType;
export declare const FaSlash: IconType;
export declare const FaSleigh: IconType;
export declare const FaSliders: IconType;
export declare const FaSmog: IconType;
export declare const FaSmoking: IconType;
export declare const FaSnowflake: IconType;
export declare const FaSnowman: IconType;
export declare const FaSnowplow: IconType;
export declare const FaSoap: IconType;
export declare const FaSocks: IconType;
export declare const FaSolarPanel: IconType;
export declare const FaSortDown: IconType;
export declare const FaSortUp: IconType;
export declare const FaSort: IconType;
export declare const FaSpa: IconType;
export declare const FaSpaghettiMonsterFlying: IconType;
export declare const FaSpellCheck: IconType;
export declare const FaSpider: IconType;
export declare const FaSpinner: IconType;
export declare const FaSplotch: IconType;
export declare const FaSpoon: IconType;
export declare const FaSprayCanSparkles: IconType;
export declare const FaSprayCan: IconType;
export declare const FaSquareArrowUpRight: IconType;
export declare const FaSquareCaretDown: IconType;
export declare const FaSquareCaretLeft: IconType;
export declare const FaSquareCaretRight: IconType;
export declare const FaSquareCaretUp: IconType;
export declare const FaSquareCheck: IconType;
export declare const FaSquareEnvelope: IconType;
export declare const FaSquareFull: IconType;
export declare const FaSquareH: IconType;
export declare const FaSquareMinus: IconType;
export declare const FaSquareNfi: IconType;
export declare const FaSquareParking: IconType;
export declare const FaSquarePen: IconType;
export declare const FaSquarePersonConfined: IconType;
export declare const FaSquarePhoneFlip: IconType;
export declare const FaSquarePhone: IconType;
export declare const FaSquarePlus: IconType;
export declare const FaSquarePollHorizontal: IconType;
export declare const FaSquarePollVertical: IconType;
export declare const FaSquareRootVariable: IconType;
export declare const FaSquareRss: IconType;
export declare const FaSquareShareNodes: IconType;
export declare const FaSquareUpRight: IconType;
export declare const FaSquareVirus: IconType;
export declare const FaSquareXmark: IconType;
export declare const FaSquare: IconType;
export declare const FaStaffSnake: IconType;
export declare const FaStairs: IconType;
export declare const FaStamp: IconType;
export declare const FaStapler: IconType;
export declare const FaStarAndCrescent: IconType;
export declare const FaStarHalfStroke: IconType;
export declare const FaStarHalf: IconType;
export declare const FaStarOfDavid: IconType;
export declare const FaStarOfLife: IconType;
export declare const FaStar: IconType;
export declare const FaSterlingSign: IconType;
export declare const FaStethoscope: IconType;
export declare const FaStop: IconType;
export declare const FaStopwatch20: IconType;
export declare const FaStopwatch: IconType;
export declare const FaStoreSlash: IconType;
export declare const FaStore: IconType;
export declare const FaStreetView: IconType;
export declare const FaStrikethrough: IconType;
export declare const FaStroopwafel: IconType;
export declare const FaSubscript: IconType;
export declare const FaSuitcaseMedical: IconType;
export declare const FaSuitcaseRolling: IconType;
export declare const FaSuitcase: IconType;
export declare const FaSunPlantWilt: IconType;
export declare const FaSun: IconType;
export declare const FaSuperscript: IconType;
export declare const FaSwatchbook: IconType;
export declare const FaSynagogue: IconType;
export declare const FaSyringe: IconType;
export declare const FaT: IconType;
export declare const FaTableCellsColumnLock: IconType;
export declare const FaTableCellsLarge: IconType;
export declare const FaTableCellsRowLock: IconType;
export declare const FaTableCellsRowUnlock: IconType;
export declare const FaTableCells: IconType;
export declare const FaTableColumns: IconType;
export declare const FaTableList: IconType;
export declare const FaTableTennisPaddleBall: IconType;
export declare const FaTable: IconType;
export declare const FaTabletButton: IconType;
export declare const FaTabletScreenButton: IconType;
export declare const FaTablet: IconType;
export declare const FaTablets: IconType;
export declare const FaTachographDigital: IconType;
export declare const FaTag: IconType;
export declare const FaTags: IconType;
export declare const FaTape: IconType;
export declare const FaTarpDroplet: IconType;
export declare const FaTarp: IconType;
export declare const FaTaxi: IconType;
export declare const FaTeethOpen: IconType;
export declare const FaTeeth: IconType;
export declare const FaTemperatureArrowDown: IconType;
export declare const FaTemperatureArrowUp: IconType;
export declare const FaTemperatureEmpty: IconType;
export declare const FaTemperatureFull: IconType;
export declare const FaTemperatureHalf: IconType;
export declare const FaTemperatureHigh: IconType;
export declare const FaTemperatureLow: IconType;
export declare const FaTemperatureQuarter: IconType;
export declare const FaTemperatureThreeQuarters: IconType;
export declare const FaTengeSign: IconType;
export declare const FaTentArrowDownToLine: IconType;
export declare const FaTentArrowLeftRight: IconType;
export declare const FaTentArrowTurnLeft: IconType;
export declare const FaTentArrowsDown: IconType;
export declare const FaTent: IconType;
export declare const FaTents: IconType;
export declare const FaTerminal: IconType;
export declare const FaTextHeight: IconType;
export declare const FaTextSlash: IconType;
export declare const FaTextWidth: IconType;
export declare const FaThermometer: IconType;
export declare const FaThumbsDown: IconType;
export declare const FaThumbsUp: IconType;
export declare const FaThumbtackSlash: IconType;
export declare const FaThumbtack: IconType;
export declare const FaTicketSimple: IconType;
export declare const FaTicket: IconType;
export declare const FaTimeline: IconType;
export declare const FaToggleOff: IconType;
export declare const FaToggleOn: IconType;
export declare const FaToiletPaperSlash: IconType;
export declare const FaToiletPaper: IconType;
export declare const FaToiletPortable: IconType;
export declare const FaToilet: IconType;
export declare const FaToiletsPortable: IconType;
export declare const FaToolbox: IconType;
export declare const FaTooth: IconType;
export declare const FaToriiGate: IconType;
export declare const FaTornado: IconType;
export declare const FaTowerBroadcast: IconType;
export declare const FaTowerCell: IconType;
export declare const FaTowerObservation: IconType;
export declare const FaTractor: IconType;
export declare const FaTrademark: IconType;
export declare const FaTrafficLight: IconType;
export declare const FaTrailer: IconType;
export declare const FaTrainSubway: IconType;
export declare const FaTrainTram: IconType;
export declare const FaTrain: IconType;
export declare const FaTransgender: IconType;
export declare const FaTrashArrowUp: IconType;
export declare const FaTrashCanArrowUp: IconType;
export declare const FaTrashCan: IconType;
export declare const FaTrash: IconType;
export declare const FaTreeCity: IconType;
export declare const FaTree: IconType;
export declare const FaTriangleExclamation: IconType;
export declare const FaTrophy: IconType;
export declare const FaTrowelBricks: IconType;
export declare const FaTrowel: IconType;
export declare const FaTruckArrowRight: IconType;
export declare const FaTruckDroplet: IconType;
export declare const FaTruckFast: IconType;
export declare const FaTruckFieldUn: IconType;
export declare const FaTruckField: IconType;
export declare const FaTruckFront: IconType;
export declare const FaTruckMedical: IconType;
export declare const FaTruckMonster: IconType;
export declare const FaTruckMoving: IconType;
export declare const FaTruckPickup: IconType;
export declare const FaTruckPlane: IconType;
export declare const FaTruckRampBox: IconType;
export declare const FaTruck: IconType;
export declare const FaTty: IconType;
export declare const FaTurkishLiraSign: IconType;
export declare const FaTurnDown: IconType;
export declare const FaTurnUp: IconType;
export declare const FaTv: IconType;
export declare const FaU: IconType;
export declare const FaUmbrellaBeach: IconType;
export declare const FaUmbrella: IconType;
export declare const FaUnderline: IconType;
export declare const FaUniversalAccess: IconType;
export declare const FaUnlockKeyhole: IconType;
export declare const FaUnlock: IconType;
export declare const FaUpDownLeftRight: IconType;
export declare const FaUpDown: IconType;
export declare const FaUpLong: IconType;
export declare const FaUpRightAndDownLeftFromCenter: IconType;
export declare const FaUpRightFromSquare: IconType;
export declare const FaUpload: IconType;
export declare const FaUserAstronaut: IconType;
export declare const FaUserCheck: IconType;
export declare const FaUserClock: IconType;
export declare const FaUserDoctor: IconType;
export declare const FaUserGear: IconType;
export declare const FaUserGraduate: IconType;
export declare const FaUserGroup: IconType;
export declare const FaUserInjured: IconType;
export declare const FaUserLargeSlash: IconType;
export declare const FaUserLarge: IconType;
export declare const FaUserLock: IconType;
export declare const FaUserMinus: IconType;
export declare const FaUserNinja: IconType;
export declare const FaUserNurse: IconType;
export declare const FaUserPen: IconType;
export declare const FaUserPlus: IconType;
export declare const FaUserSecret: IconType;
export declare const FaUserShield: IconType;
export declare const FaUserSlash: IconType;
export declare const FaUserTag: IconType;
export declare const FaUserTie: IconType;
export declare const FaUserXmark: IconType;
export declare const FaUser: IconType;
export declare const FaUsersBetweenLines: IconType;
export declare const FaUsersGear: IconType;
export declare const FaUsersLine: IconType;
export declare const FaUsersRays: IconType;
export declare const FaUsersRectangle: IconType;
export declare const FaUsersSlash: IconType;
export declare const FaUsersViewfinder: IconType;
export declare const FaUsers: IconType;
export declare const FaUtensils: IconType;
export declare const FaV: IconType;
export declare const FaVanShuttle: IconType;
export declare const FaVault: IconType;
export declare const FaVectorSquare: IconType;
export declare const FaVenusDouble: IconType;
export declare const FaVenusMars: IconType;
export declare const FaVenus: IconType;
export declare const FaVestPatches: IconType;
export declare const FaVest: IconType;
export declare const FaVialCircleCheck: IconType;
export declare const FaVialVirus: IconType;
export declare const FaVial: IconType;
export declare const FaVials: IconType;
export declare const FaVideoSlash: IconType;
export declare const FaVideo: IconType;
export declare const FaVihara: IconType;
export declare const FaVirusCovidSlash: IconType;
export declare const FaVirusCovid: IconType;
export declare const FaVirusSlash: IconType;
export declare const FaVirus: IconType;
export declare const FaViruses: IconType;
export declare const FaVoicemail: IconType;
export declare const FaVolcano: IconType;
export declare const FaVolleyball: IconType;
export declare const FaVolumeHigh: IconType;
export declare const FaVolumeLow: IconType;
export declare const FaVolumeOff: IconType;
export declare const FaVolumeXmark: IconType;
export declare const FaVrCardboard: IconType;
export declare const FaW: IconType;
export declare const FaWalkieTalkie: IconType;
export declare const FaWallet: IconType;
export declare const FaWandMagicSparkles: IconType;
export declare const FaWandMagic: IconType;
export declare const FaWandSparkles: IconType;
export declare const FaWarehouse: IconType;
export declare const FaWaterLadder: IconType;
export declare const FaWater: IconType;
export declare const FaWaveSquare: IconType;
export declare const FaWeightHanging: IconType;
export declare const FaWeightScale: IconType;
export declare const FaWheatAwnCircleExclamation: IconType;
export declare const FaWheatAwn: IconType;
export declare const FaWheelchairMove: IconType;
export declare const FaWheelchair: IconType;
export declare const FaWhiskeyGlass: IconType;
export declare const FaWifi: IconType;
export declare const FaWind: IconType;
export declare const FaWindowMaximize: IconType;
export declare const FaWindowMinimize: IconType;
export declare const FaWindowRestore: IconType;
export declare const FaWineBottle: IconType;
export declare const FaWineGlassEmpty: IconType;
export declare const FaWineGlass: IconType;
export declare const FaWonSign: IconType;
export declare const FaWorm: IconType;
export declare const FaWrench: IconType;
export declare const FaXRay: IconType;
export declare const FaX: IconType;
export declare const FaXmark: IconType;
export declare const FaXmarksLines: IconType;
export declare const FaY: IconType;
export declare const FaYenSign: IconType;
export declare const FaYinYang: IconType;
export declare const FaZ: IconType;
export declare const FaRegAddressBook: IconType;
export declare const FaRegAddressCard: IconType;
export declare const FaRegBellSlash: IconType;
export declare const FaRegBell: IconType;
export declare const FaRegBookmark: IconType;
export declare const FaRegBuilding: IconType;
export declare const FaRegCalendarCheck: IconType;
export declare const FaRegCalendarDays: IconType;
export declare const FaRegCalendarMinus: IconType;
export declare const FaRegCalendarPlus: IconType;
export declare const FaRegCalendarXmark: IconType;
export declare const FaRegCalendar: IconType;
export declare const FaRegChartBar: IconType;
export declare const FaRegChessBishop: IconType;
export declare const FaRegChessKing: IconType;
export declare const FaRegChessKnight: IconType;
export declare const FaRegChessPawn: IconType;
export declare const FaRegChessQueen: IconType;
export declare const FaRegChessRook: IconType;
export declare const FaRegCircleCheck: IconType;
export declare const FaRegCircleDot: IconType;
export declare const FaRegCircleDown: IconType;
export declare const FaRegCircleLeft: IconType;
export declare const FaRegCirclePause: IconType;
export declare const FaRegCirclePlay: IconType;
export declare const FaRegCircleQuestion: IconType;
export declare const FaRegCircleRight: IconType;
export declare const FaRegCircleStop: IconType;
export declare const FaRegCircleUp: IconType;
export declare const FaRegCircleUser: IconType;
export declare const FaRegCircleXmark: IconType;
export declare const FaRegCircle: IconType;
export declare const FaRegClipboard: IconType;
export declare const FaRegClock: IconType;
export declare const FaRegClone: IconType;
export declare const FaRegClosedCaptioning: IconType;
export declare const FaRegCommentDots: IconType;
export declare const FaRegComment: IconType;
export declare const FaRegComments: IconType;
export declare const FaRegCompass: IconType;
export declare const FaRegCopy: IconType;
export declare const FaRegCopyright: IconType;
export declare const FaRegCreditCard: IconType;
export declare const FaRegEnvelopeOpen: IconType;
export declare const FaRegEnvelope: IconType;
export declare const FaRegEyeSlash: IconType;
export declare const FaRegEye: IconType;
export declare const FaRegFaceAngry: IconType;
export declare const FaRegFaceDizzy: IconType;
export declare const FaRegFaceFlushed: IconType;
export declare const FaRegFaceFrownOpen: IconType;
export declare const FaRegFaceFrown: IconType;
export declare const FaRegFaceGrimace: IconType;
export declare const FaRegFaceGrinBeamSweat: IconType;
export declare const FaRegFaceGrinBeam: IconType;
export declare const FaRegFaceGrinHearts: IconType;
export declare const FaRegFaceGrinSquintTears: IconType;
export declare const FaRegFaceGrinSquint: IconType;
export declare const FaRegFaceGrinStars: IconType;
export declare const FaRegFaceGrinTears: IconType;
export declare const FaRegFaceGrinTongueSquint: IconType;
export declare const FaRegFaceGrinTongueWink: IconType;
export declare const FaRegFaceGrinTongue: IconType;
export declare const FaRegFaceGrinWide: IconType;
export declare const FaRegFaceGrinWink: IconType;
export declare const FaRegFaceGrin: IconType;
export declare const FaRegFaceKissBeam: IconType;
export declare const FaRegFaceKissWinkHeart: IconType;
export declare const FaRegFaceKiss: IconType;
export declare const FaRegFaceLaughBeam: IconType;
export declare const FaRegFaceLaughSquint: IconType;
export declare const FaRegFaceLaughWink: IconType;
export declare const FaRegFaceLaugh: IconType;
export declare const FaRegFaceMehBlank: IconType;
export declare const FaRegFaceMeh: IconType;
export declare const FaRegFaceRollingEyes: IconType;
export declare const FaRegFaceSadCry: IconType;
export declare const FaRegFaceSadTear: IconType;
export declare const FaRegFaceSmileBeam: IconType;
export declare const FaRegFaceSmileWink: IconType;
export declare const FaRegFaceSmile: IconType;
export declare const FaRegFaceSurprise: IconType;
export declare const FaRegFaceTired: IconType;
export declare const FaRegFileAudio: IconType;
export declare const FaRegFileCode: IconType;
export declare const FaRegFileExcel: IconType;
export declare const FaRegFileImage: IconType;
export declare const FaRegFileLines: IconType;
export declare const FaRegFilePdf: IconType;
export declare const FaRegFilePowerpoint: IconType;
export declare const FaRegFileVideo: IconType;
export declare const FaRegFileWord: IconType;
export declare const FaRegFileZipper: IconType;
export declare const FaRegFile: IconType;
export declare const FaRegFlag: IconType;
export declare const FaRegFloppyDisk: IconType;
export declare const FaRegFolderClosed: IconType;
export declare const FaRegFolderOpen: IconType;
export declare const FaRegFolder: IconType;
export declare const FaRegFontAwesome: IconType;
export declare const FaRegFutbol: IconType;
export declare const FaRegGem: IconType;
export declare const FaRegHandBackFist: IconType;
export declare const FaRegHandLizard: IconType;
export declare const FaRegHandPeace: IconType;
export declare const FaRegHandPointDown: IconType;
export declare const FaRegHandPointLeft: IconType;
export declare const FaRegHandPointRight: IconType;
export declare const FaRegHandPointUp: IconType;
export declare const FaRegHandPointer: IconType;
export declare const FaRegHandScissors: IconType;
export declare const FaRegHandSpock: IconType;
export declare const FaRegHand: IconType;
export declare const FaRegHandshake: IconType;
export declare const FaRegHardDrive: IconType;
export declare const FaRegHeart: IconType;
export declare const FaRegHospital: IconType;
export declare const FaRegHourglassHalf: IconType;
export declare const FaRegHourglass: IconType;
export declare const FaRegIdBadge: IconType;
export declare const FaRegIdCard: IconType;
export declare const FaRegImage: IconType;
export declare const FaRegImages: IconType;
export declare const FaRegKeyboard: IconType;
export declare const FaRegLemon: IconType;
export declare const FaRegLifeRing: IconType;
export declare const FaRegLightbulb: IconType;
export declare const FaRegMap: IconType;
export declare const FaRegMessage: IconType;
export declare const FaRegMoneyBill1: IconType;
export declare const FaRegMoon: IconType;
export declare const FaRegNewspaper: IconType;
export declare const FaRegNoteSticky: IconType;
export declare const FaRegObjectGroup: IconType;
export declare const FaRegObjectUngroup: IconType;
export declare const FaRegPaperPlane: IconType;
export declare const FaRegPaste: IconType;
export declare const FaRegPenToSquare: IconType;
export declare const FaRegRectangleList: IconType;
export declare const FaRegRectangleXmark: IconType;
export declare const FaRegRegistered: IconType;
export declare const FaRegShareFromSquare: IconType;
export declare const FaRegSnowflake: IconType;
export declare const FaRegSquareCaretDown: IconType;
export declare const FaRegSquareCaretLeft: IconType;
export declare const FaRegSquareCaretRight: IconType;
export declare const FaRegSquareCaretUp: IconType;
export declare const FaRegSquareCheck: IconType;
export declare const FaRegSquareFull: IconType;
export declare const FaRegSquareMinus: IconType;
export declare const FaRegSquarePlus: IconType;
export declare const FaRegSquare: IconType;
export declare const FaRegStarHalfStroke: IconType;
export declare const FaRegStarHalf: IconType;
export declare const FaRegStar: IconType;
export declare const FaRegSun: IconType;
export declare const FaRegThumbsDown: IconType;
export declare const FaRegThumbsUp: IconType;
export declare const FaRegTrashCan: IconType;
export declare const FaRegUser: IconType;
export declare const FaRegWindowMaximize: IconType;
export declare const FaRegWindowMinimize: IconType;
export declare const FaRegWindowRestore: IconType;
