package sales.savvy.dto;

public class PaymentRequest {
    private Double amount;
    private String username;
    private String receipt;
    
    // Constructors
    public PaymentRequest() {}
    
    public PaymentRequest(Double amount, String username, String receipt) {
        this.amount = amount;
        this.username = username;
        this.receipt = receipt;
    }
    
    // Getters and Setters
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getReceipt() {
        return receipt;
    }
    
    public void setReceipt(String receipt) {
        this.receipt = receipt;
    }
    
    @Override
    public String toString() {
        return "PaymentRequest{" +
                "amount=" + amount +
                ", username='" + username + '\'' +
                ", receipt='" + receipt + '\'' +
                '}';
    }
}
