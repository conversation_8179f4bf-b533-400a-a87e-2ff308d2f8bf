{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\AdminPages\\\\AllProducts.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function AllProducts() {\n  _s();\n  const [products, setProducts] = useState([]);\n  const navigate = useNavigate();\n  const fetchProducts = () => {\n    axios.get('http://localhost:8080/getAllProducts').then(response => {\n      setProducts(response.data);\n    }).catch(error => {\n      console.error('Error fetching products:', error);\n    });\n  };\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const handleDelete = id => {\n    axios.delete('http://localhost:8080/deleteProduct', {\n      params: {\n        id\n      }\n    }).then(() => fetchProducts()).catch(err => console.error('Delete failed:', err));\n  };\n  const handleUpdate = product => {\n    navigate('/updateproduct', {\n      state: {\n        product\n      }\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"You are at All Products Page\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        overflowX: 'auto',\n        overflowY: 'auto',\n        height: '500px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        border: 1,\n        cellPadding: 10,\n        cellSpacing: 0,\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Id\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Description\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Price\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Image\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 29\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          children: products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              children: product.id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"b\", {\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: product.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: [product.price, \"\\u20B9\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: \"product\",\n                style: {\n                  width: '100px',\n                  height: '100px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleUpdate(product),\n                children: \"Update\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDelete(product.id),\n                children: \"Delete\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 33\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 9\n  }, this);\n}\n_s(AllProducts, \"IM33jVfoypN7Irf5pwXOwm+Oe18=\", false, function () {\n  return [useNavigate];\n});\n_c = AllProducts;\nvar _c;\n$RefreshReg$(_c, \"AllProducts\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "useNavigate", "jsxDEV", "_jsxDEV", "AllProducts", "_s", "products", "setProducts", "navigate", "fetchProducts", "get", "then", "response", "data", "catch", "error", "console", "handleDelete", "id", "delete", "params", "err", "handleUpdate", "product", "state", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "overflowX", "overflowY", "height", "border", "cellPadding", "cellSpacing", "map", "name", "description", "price", "src", "image", "alt", "width", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/AdminPages/AllProducts.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nexport default function AllProducts() {\r\n    const [products, setProducts] = useState([]);\r\n    const navigate = useNavigate();\r\n\r\n    const fetchProducts = () => {\r\n        axios.get('http://localhost:8080/getAllProducts')\r\n            .then(response => {\r\n                setProducts(response.data);\r\n            })\r\n            .catch(error => {\r\n                console.error('Error fetching products:', error);\r\n            });\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchProducts();\r\n    }, []);\r\n\r\n    const handleDelete = (id) => {\r\n        axios\r\n            .delete('http://localhost:8080/deleteProduct', { params: { id } })\r\n            .then(() => fetchProducts())\r\n            .catch(err => console.error('Delete failed:', err));\r\n    };\r\n\r\n    const handleUpdate = (product) => {\r\n        navigate('/updateproduct', { state: { product } });\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            <h3>You are at All Products Page</h3>\r\n            <div style={{ overflowX: 'auto', overflowY: 'auto', height: '500px' }}>\r\n                <table border={1} cellPadding={10} cellSpacing={0}>\r\n                    <thead>\r\n                        <tr>\r\n                            <th>Id</th>\r\n                            <th>Name</th>\r\n                            <th>Description</th>\r\n                            <th>Price</th>\r\n                            <th>Image</th>\r\n                            <th>Actions</th> {/* Added header for action buttons */}\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        {products.map(product => (\r\n                            <tr key={product.id}>\r\n                                <td>{product.id}</td>\r\n                                <td><b>{product.name}</b></td>\r\n                                <td>{product.description}</td>\r\n                                <td>{product.price}₹</td>\r\n                                <td>\r\n                                    <img \r\n                                        src={product.image} \r\n                                        alt=\"product\" \r\n                                        style={{ width: '100px', height: '100px' }} \r\n                                    />\r\n                                </td>\r\n                                <td>\r\n                                    <button onClick={() => handleUpdate(product)}>Update</button>\r\n                                    <br/>\r\n                                    <button onClick={() => handleDelete(product.id)}>Delete</button>\r\n                                </td>\r\n                            </tr>\r\n                        ))}\r\n                    </tbody>\r\n                </table>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAMS,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,MAAMQ,aAAa,GAAGA,CAAA,KAAM;IACxBT,KAAK,CAACU,GAAG,CAAC,sCAAsC,CAAC,CAC5CC,IAAI,CAACC,QAAQ,IAAI;MACdL,WAAW,CAACK,QAAQ,CAACC,IAAI,CAAC;IAC9B,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAI;MACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD,CAAC,CAAC;EACV,CAAC;EAEDjB,SAAS,CAAC,MAAM;IACZW,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,YAAY,GAAIC,EAAE,IAAK;IACzBlB,KAAK,CACAmB,MAAM,CAAC,qCAAqC,EAAE;MAAEC,MAAM,EAAE;QAAEF;MAAG;IAAE,CAAC,CAAC,CACjEP,IAAI,CAAC,MAAMF,aAAa,CAAC,CAAC,CAAC,CAC3BK,KAAK,CAACO,GAAG,IAAIL,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEM,GAAG,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMC,YAAY,GAAIC,OAAO,IAAK;IAC9Bf,QAAQ,CAAC,gBAAgB,EAAE;MAAEgB,KAAK,EAAE;QAAED;MAAQ;IAAE,CAAC,CAAC;EACtD,CAAC;EAED,oBACIpB,OAAA;IAAAsB,QAAA,gBACItB,OAAA;MAAAsB,QAAA,EAAI;IAA4B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACrC1B,OAAA;MAAK2B,KAAK,EAAE;QAAEC,SAAS,EAAE,MAAM;QAAEC,SAAS,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAR,QAAA,eAClEtB,OAAA;QAAO+B,MAAM,EAAE,CAAE;QAACC,WAAW,EAAE,EAAG;QAACC,WAAW,EAAE,CAAE;QAAAX,QAAA,gBAC9CtB,OAAA;UAAAsB,QAAA,eACItB,OAAA;YAAAsB,QAAA,gBACItB,OAAA;cAAAsB,QAAA,EAAI;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACX1B,OAAA;cAAAsB,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACb1B,OAAA;cAAAsB,QAAA,EAAI;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB1B,OAAA;cAAAsB,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd1B,OAAA;cAAAsB,QAAA,EAAI;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACd1B,OAAA;cAAAsB,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACR1B,OAAA;UAAAsB,QAAA,EACKnB,QAAQ,CAAC+B,GAAG,CAACd,OAAO,iBACjBpB,OAAA;YAAAsB,QAAA,gBACItB,OAAA;cAAAsB,QAAA,EAAKF,OAAO,CAACL;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrB1B,OAAA;cAAAsB,QAAA,eAAItB,OAAA;gBAAAsB,QAAA,EAAIF,OAAO,CAACe;cAAI;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9B1B,OAAA;cAAAsB,QAAA,EAAKF,OAAO,CAACgB;YAAW;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9B1B,OAAA;cAAAsB,QAAA,GAAKF,OAAO,CAACiB,KAAK,EAAC,QAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzB1B,OAAA;cAAAsB,QAAA,eACItB,OAAA;gBACIsC,GAAG,EAAElB,OAAO,CAACmB,KAAM;gBACnBC,GAAG,EAAC,SAAS;gBACbb,KAAK,EAAE;kBAAEc,KAAK,EAAE,OAAO;kBAAEX,MAAM,EAAE;gBAAQ;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACL1B,OAAA;cAAAsB,QAAA,gBACItB,OAAA;gBAAQ0C,OAAO,EAAEA,CAAA,KAAMvB,YAAY,CAACC,OAAO,CAAE;gBAAAE,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7D1B,OAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1B,OAAA;gBAAQ0C,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAACM,OAAO,CAACL,EAAE,CAAE;gBAAAO,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA,GAhBAN,OAAO,CAACL,EAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBf,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACxB,EAAA,CAtEuBD,WAAW;EAAA,QAEdH,WAAW;AAAA;AAAA6C,EAAA,GAFR1C,WAAW;AAAA,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}