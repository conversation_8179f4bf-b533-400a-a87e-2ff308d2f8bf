{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\homePage\\\\CustomerDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport ProductsList from '../shared/ProductsList';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function CustomerDashboard() {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"navbar\",\n      style: {\n        background: 'linear-gradient(135deg, #28a745, #20c997)',\n        padding: '15px 30px',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',\n        position: 'sticky',\n        top: 0,\n        zIndex: 1000,\n        fontFamily: 'Poppins, sans-serif'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'white',\n          fontSize: '1.8em',\n          fontWeight: '700',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [\"\\uD83D\\uDED2 \", /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '0.9em'\n          },\n          children: \"Sales Savvy - Customer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 24\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          const confirmed = window.confirm(\"Are you sure you want to logout?\");\n          if (confirmed) {\n            localStorage.removeItem('user');\n            sessionStorage.clear();\n            navigate('/');\n          }\n        },\n        style: {\n          background: 'rgba(255, 255, 255, 0.2)',\n          color: 'white',\n          border: '2px solid rgba(255, 255, 255, 0.3)',\n          padding: '8px 16px',\n          borderRadius: '8px',\n          fontSize: '0.95em',\n          fontWeight: '500',\n          cursor: 'pointer',\n          transition: 'all 0.3s ease',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          fontFamily: 'Poppins, sans-serif'\n        },\n        onMouseOver: e => {\n          e.target.style.background = 'rgba(255, 255, 255, 0.3)';\n          e.target.style.borderColor = 'rgba(255, 255, 255, 0.5)';\n          e.target.style.transform = 'translateY(-2px)';\n        },\n        onMouseOut: e => {\n          e.target.style.background = 'rgba(255, 255, 255, 0.2)';\n          e.target.style.borderColor = 'rgba(255, 255, 255, 0.3)';\n          e.target.style.transform = 'translateY(0)';\n        },\n        children: \"\\uD83D\\uDEAA Logout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '1200px',\n        margin: '0 auto',\n        padding: '30px 20px',\n        fontFamily: 'Poppins, sans-serif',\n        backgroundColor: '#f8f9fa',\n        minHeight: 'calc(100vh - 80px)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: '30px',\n          padding: '20px 0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            color: '#333',\n            fontSize: '2.5rem',\n            marginBottom: '10px',\n            fontWeight: '600',\n            background: 'linear-gradient(135deg, #28a745, #20c997)',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n            backgroundClip: 'text'\n          },\n          children: \"Customer Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#666',\n            fontSize: '1.1rem',\n            margin: 0\n          },\n          children: \"Browse and explore our amazing products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(ProductsList, {\n        userRole: \"customer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n}\n_s(CustomerDashboard, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = CustomerDashboard;\nvar _c;\n$RefreshReg$(_c, \"CustomerDashboard\");", "map": {"version": 3, "names": ["React", "useNavigate", "ProductsList", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CustomerDashboard", "_s", "navigate", "children", "className", "style", "background", "padding", "display", "justifyContent", "alignItems", "boxShadow", "position", "top", "zIndex", "fontFamily", "color", "fontSize", "fontWeight", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "confirmed", "window", "confirm", "localStorage", "removeItem", "sessionStorage", "clear", "border", "borderRadius", "cursor", "transition", "onMouseOver", "e", "target", "borderColor", "transform", "onMouseOut", "max<PERSON><PERSON><PERSON>", "margin", "backgroundColor", "minHeight", "textAlign", "marginBottom", "WebkitBackgroundClip", "WebkitTextFillColor", "backgroundClip", "userRole", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/homePage/CustomerDashboard.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport ProductsList from '../shared/ProductsList';\r\n\r\nexport default function CustomerDashboard() {\r\n    const navigate = useNavigate();\r\n\r\n    return (\r\n        <>\r\n            <nav className=\"navbar\" style={{\r\n                background: 'linear-gradient(135deg, #28a745, #20c997)',\r\n                padding: '15px 30px',\r\n                display: 'flex',\r\n                justifyContent: 'space-between',\r\n                alignItems: 'center',\r\n                boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',\r\n                position: 'sticky',\r\n                top: 0,\r\n                zIndex: 1000,\r\n                fontFamily: 'Poppins, sans-serif'\r\n            }}>\r\n                <div style={{\r\n                    color: 'white',\r\n                    fontSize: '1.8em',\r\n                    fontWeight: '700',\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    gap: '8px'\r\n                }}>\r\n                    🛒 <span style={{fontSize: '0.9em'}}>Sales Savvy - Customer</span>\r\n                </div>\r\n                <button\r\n                    onClick={() => {\r\n                        const confirmed = window.confirm(\"Are you sure you want to logout?\");\r\n                        if (confirmed) {\r\n                            localStorage.removeItem('user');\r\n                            sessionStorage.clear();\r\n                            navigate('/');\r\n                        }\r\n                    }}\r\n                    style={{\r\n                        background: 'rgba(255, 255, 255, 0.2)',\r\n                        color: 'white',\r\n                        border: '2px solid rgba(255, 255, 255, 0.3)',\r\n                        padding: '8px 16px',\r\n                        borderRadius: '8px',\r\n                        fontSize: '0.95em',\r\n                        fontWeight: '500',\r\n                        cursor: 'pointer',\r\n                        transition: 'all 0.3s ease',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        gap: '6px',\r\n                        fontFamily: 'Poppins, sans-serif'\r\n                    }}\r\n                    onMouseOver={(e) => {\r\n                        e.target.style.background = 'rgba(255, 255, 255, 0.3)';\r\n                        e.target.style.borderColor = 'rgba(255, 255, 255, 0.5)';\r\n                        e.target.style.transform = 'translateY(-2px)';\r\n                    }}\r\n                    onMouseOut={(e) => {\r\n                        e.target.style.background = 'rgba(255, 255, 255, 0.2)';\r\n                        e.target.style.borderColor = 'rgba(255, 255, 255, 0.3)';\r\n                        e.target.style.transform = 'translateY(0)';\r\n                    }}\r\n                >\r\n                    🚪 Logout\r\n                </button>\r\n            </nav>\r\n\r\n            <div style={{\r\n                maxWidth: '1200px',\r\n                margin: '0 auto',\r\n                padding: '30px 20px',\r\n                fontFamily: 'Poppins, sans-serif',\r\n                backgroundColor: '#f8f9fa',\r\n                minHeight: 'calc(100vh - 80px)'\r\n            }}>\r\n                <div style={{\r\n                    textAlign: 'center',\r\n                    marginBottom: '30px',\r\n                    padding: '20px 0'\r\n                }}>\r\n                    <h1 style={{\r\n                        color: '#333',\r\n                        fontSize: '2.5rem',\r\n                        marginBottom: '10px',\r\n                        fontWeight: '600',\r\n                        background: 'linear-gradient(135deg, #28a745, #20c997)',\r\n                        WebkitBackgroundClip: 'text',\r\n                        WebkitTextFillColor: 'transparent',\r\n                        backgroundClip: 'text'\r\n                    }}>\r\n                        Customer Dashboard\r\n                    </h1>\r\n                    <p style={{\r\n                        color: '#666',\r\n                        fontSize: '1.1rem',\r\n                        margin: 0\r\n                    }}>\r\n                        Browse and explore our amazing products\r\n                    </p>\r\n                </div>\r\n\r\n                <ProductsList userRole=\"customer\" />\r\n            </div>\r\n        </>\r\n    );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,YAAY,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,eAAe,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,oBACIG,OAAA,CAAAE,SAAA;IAAAI,QAAA,gBACIN,OAAA;MAAKO,SAAS,EAAC,QAAQ;MAACC,KAAK,EAAE;QAC3BC,UAAU,EAAE,2CAA2C;QACvDC,OAAO,EAAE,WAAW;QACpBC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,+BAA+B;QAC1CC,QAAQ,EAAE,QAAQ;QAClBC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MAChB,CAAE;MAAAZ,QAAA,gBACEN,OAAA;QAAKQ,KAAK,EAAE;UACRW,KAAK,EAAE,OAAO;UACdC,QAAQ,EAAE,OAAO;UACjBC,UAAU,EAAE,KAAK;UACjBV,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBS,GAAG,EAAE;QACT,CAAE;QAAAhB,QAAA,GAAC,eACI,eAAAN,OAAA;UAAMQ,KAAK,EAAE;YAACY,QAAQ,EAAE;UAAO,CAAE;UAAAd,QAAA,EAAC;QAAsB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eACN1B,OAAA;QACI2B,OAAO,EAAEA,CAAA,KAAM;UACX,MAAMC,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC;UACpE,IAAIF,SAAS,EAAE;YACXG,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;YAC/BC,cAAc,CAACC,KAAK,CAAC,CAAC;YACtB7B,QAAQ,CAAC,GAAG,CAAC;UACjB;QACJ,CAAE;QACFG,KAAK,EAAE;UACHC,UAAU,EAAE,0BAA0B;UACtCU,KAAK,EAAE,OAAO;UACdgB,MAAM,EAAE,oCAAoC;UAC5CzB,OAAO,EAAE,UAAU;UACnB0B,YAAY,EAAE,KAAK;UACnBhB,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,KAAK;UACjBgB,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE,eAAe;UAC3B3B,OAAO,EAAE,MAAM;UACfE,UAAU,EAAE,QAAQ;UACpBS,GAAG,EAAE,KAAK;UACVJ,UAAU,EAAE;QAChB,CAAE;QACFqB,WAAW,EAAGC,CAAC,IAAK;UAChBA,CAAC,CAACC,MAAM,CAACjC,KAAK,CAACC,UAAU,GAAG,0BAA0B;UACtD+B,CAAC,CAACC,MAAM,CAACjC,KAAK,CAACkC,WAAW,GAAG,0BAA0B;UACvDF,CAAC,CAACC,MAAM,CAACjC,KAAK,CAACmC,SAAS,GAAG,kBAAkB;QACjD,CAAE;QACFC,UAAU,EAAGJ,CAAC,IAAK;UACfA,CAAC,CAACC,MAAM,CAACjC,KAAK,CAACC,UAAU,GAAG,0BAA0B;UACtD+B,CAAC,CAACC,MAAM,CAACjC,KAAK,CAACkC,WAAW,GAAG,0BAA0B;UACvDF,CAAC,CAACC,MAAM,CAACjC,KAAK,CAACmC,SAAS,GAAG,eAAe;QAC9C,CAAE;QAAArC,QAAA,EACL;MAED;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAEN1B,OAAA;MAAKQ,KAAK,EAAE;QACRqC,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE,QAAQ;QAChBpC,OAAO,EAAE,WAAW;QACpBQ,UAAU,EAAE,qBAAqB;QACjC6B,eAAe,EAAE,SAAS;QAC1BC,SAAS,EAAE;MACf,CAAE;MAAA1C,QAAA,gBACEN,OAAA;QAAKQ,KAAK,EAAE;UACRyC,SAAS,EAAE,QAAQ;UACnBC,YAAY,EAAE,MAAM;UACpBxC,OAAO,EAAE;QACb,CAAE;QAAAJ,QAAA,gBACEN,OAAA;UAAIQ,KAAK,EAAE;YACPW,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,QAAQ;YAClB8B,YAAY,EAAE,MAAM;YACpB7B,UAAU,EAAE,KAAK;YACjBZ,UAAU,EAAE,2CAA2C;YACvD0C,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE,aAAa;YAClCC,cAAc,EAAE;UACpB,CAAE;UAAA/C,QAAA,EAAC;QAEH;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1B,OAAA;UAAGQ,KAAK,EAAE;YACNW,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,QAAQ;YAClB0B,MAAM,EAAE;UACZ,CAAE;UAAAxC,QAAA,EAAC;QAEH;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1B,OAAA,CAACF,YAAY;QAACwD,QAAQ,EAAC;MAAU;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAAA,eACR,CAAC;AAEX;AAACtB,EAAA,CAxGuBD,iBAAiB;EAAA,QACpBN,WAAW;AAAA;AAAA0D,EAAA,GADRpD,iBAAiB;AAAA,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}