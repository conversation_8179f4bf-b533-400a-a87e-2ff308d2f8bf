{"ast": null, "code": "// Cart utility functions for consistent cart operations across components\n\nexport const triggerCartUpdate = () => {\n  // Trigger custom event to update cart count in navbar\n  window.dispatchEvent(new Event('cartUpdated'));\n};\nexport const getUsername = () => {\n  return localStorage.getItem('user');\n};\nexport const isLoggedIn = () => {\n  return !!localStorage.getItem('user');\n};\nexport const handleApiError = (error, defaultMessage = 'An error occurred') => {\n  console.error('API Error:', error);\n  if (error.response) {\n    // Server responded with error status\n    const message = error.response.data || `Server error: ${error.response.status}`;\n    alert(typeof message === 'string' ? message : defaultMessage);\n  } else if (error.request) {\n    // Request was made but no response received\n    alert('Network error. Please check your connection and try again.');\n  } else {\n    // Something else happened\n    alert(defaultMessage);\n  }\n};\nexport const showSuccessMessage = message => {\n  alert(message);\n};\nexport const showErrorMessage = message => {\n  alert(message);\n};", "map": {"version": 3, "names": ["triggerCartUpdate", "window", "dispatchEvent", "Event", "getUsername", "localStorage", "getItem", "isLoggedIn", "handleApiError", "error", "defaultMessage", "console", "response", "message", "data", "status", "alert", "request", "showSuccessMessage", "showErrorMessage"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/shared/CartUtils.js"], "sourcesContent": ["// Cart utility functions for consistent cart operations across components\n\nexport const triggerCartUpdate = () => {\n    // Trigger custom event to update cart count in navbar\n    window.dispatchEvent(new Event('cartUpdated'));\n};\n\nexport const getUsername = () => {\n    return localStorage.getItem('user');\n};\n\nexport const isLoggedIn = () => {\n    return !!localStorage.getItem('user');\n};\n\nexport const handleApiError = (error, defaultMessage = 'An error occurred') => {\n    console.error('API Error:', error);\n    \n    if (error.response) {\n        // Server responded with error status\n        const message = error.response.data || `Server error: ${error.response.status}`;\n        alert(typeof message === 'string' ? message : defaultMessage);\n    } else if (error.request) {\n        // Request was made but no response received\n        alert('Network error. Please check your connection and try again.');\n    } else {\n        // Something else happened\n        alert(defaultMessage);\n    }\n};\n\nexport const showSuccessMessage = (message) => {\n    alert(message);\n};\n\nexport const showErrorMessage = (message) => {\n    alert(message);\n};\n"], "mappings": "AAAA;;AAEA,OAAO,MAAMA,iBAAiB,GAAGA,CAAA,KAAM;EACnC;EACAC,MAAM,CAACC,aAAa,CAAC,IAAIC,KAAK,CAAC,aAAa,CAAC,CAAC;AAClD,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAC7B,OAAOC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;AACvC,CAAC;AAED,OAAO,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAC5B,OAAO,CAAC,CAACF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;AACzC,CAAC;AAED,OAAO,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,cAAc,GAAG,mBAAmB,KAAK;EAC3EC,OAAO,CAACF,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EAElC,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAChB;IACA,MAAMC,OAAO,GAAGJ,KAAK,CAACG,QAAQ,CAACE,IAAI,IAAI,iBAAiBL,KAAK,CAACG,QAAQ,CAACG,MAAM,EAAE;IAC/EC,KAAK,CAAC,OAAOH,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGH,cAAc,CAAC;EACjE,CAAC,MAAM,IAAID,KAAK,CAACQ,OAAO,EAAE;IACtB;IACAD,KAAK,CAAC,4DAA4D,CAAC;EACvE,CAAC,MAAM;IACH;IACAA,KAAK,CAACN,cAAc,CAAC;EACzB;AACJ,CAAC;AAED,OAAO,MAAMQ,kBAAkB,GAAIL,OAAO,IAAK;EAC3CG,KAAK,CAACH,OAAO,CAAC;AAClB,CAAC;AAED,OAAO,MAAMM,gBAAgB,GAAIN,OAAO,IAAK;EACzCG,KAAK,CAACH,OAAO,CAAC;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}