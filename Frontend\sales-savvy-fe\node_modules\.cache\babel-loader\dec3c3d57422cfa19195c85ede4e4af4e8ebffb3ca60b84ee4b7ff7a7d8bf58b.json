{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\App.js\";\nimport './App.css';\nimport CustomerDashboard from './Components/homePage/CustomerDashboard';\nimport Home from './Components/homePage/Home';\nimport Login from './Components/login/Login';\nimport Signup from './Components/signUp/SignUp';\nimport AdminDashboard from './Components/homePage/AdminDashboard_new';\nimport AddProducts from './Components/AdminPages/AddProducts';\nimport AllProducts from './Components/AdminPages/AllProducts';\nimport UpdateProduct from './Components/AdminPages/UpdateProduct';\nimport DeleteProduct from './Components/AdminPages/DeleteProduct';\nimport Cart from './Components/Customer/Cart';\nimport { BrowserRouter, Route, Routes } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 32\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 37\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/signup\",\n        element: /*#__PURE__*/_jsxDEV(Signup, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/CustomerDashboard\",\n        element: /*#__PURE__*/_jsxDEV(CustomerDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 49\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/AdminDashboard\",\n        element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 46\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/AddProducts\",\n        element: /*#__PURE__*/_jsxDEV(AddProducts, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/AllProducts\",\n        element: /*#__PURE__*/_jsxDEV(AllProducts, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/updateproduct\",\n        element: /*#__PURE__*/_jsxDEV(UpdateProduct, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/DeleteProduct\",\n        element: /*#__PURE__*/_jsxDEV(DeleteProduct, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/cart\",\n        element: /*#__PURE__*/_jsxDEV(Cart, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 36\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["CustomerDashboard", "Home", "<PERSON><PERSON>", "Signup", "AdminDashboard", "AddProducts", "AllProducts", "UpdateProduct", "DeleteProduct", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Route", "Routes", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/App.js"], "sourcesContent": ["import './App.css';\nimport CustomerDashboard from './Components/homePage/CustomerDashboard';\nimport Home from './Components/homePage/Home';\nimport Login from './Components/login/Login';\nimport Signup from './Components/signUp/SignUp';\nimport AdminDashboard from './Components/homePage/AdminDashboard_new';\nimport AddProducts from './Components/AdminPages/AddProducts';\nimport AllProducts from './Components/AdminPages/AllProducts';\nimport UpdateProduct from './Components/AdminPages/UpdateProduct';\nimport DeleteProduct from './Components/AdminPages/DeleteProduct';\nimport Cart from './Components/Customer/Cart';\nimport {BrowserRouter, Route, Routes} from 'react-router-dom';\nfunction App() {\n  return (\n    <BrowserRouter>\n    <Routes>\n      <Route path=\"/\" element={<Home/>}/>\n      <Route path=\"/login\" element={<Login/>}/>\n      <Route path=\"/signup\" element={<Signup />}/>\n      <Route path=\"/CustomerDashboard\" element={<CustomerDashboard />}/>\n      <Route path=\"/AdminDashboard\" element={<AdminDashboard />}/>\n      <Route path=\"/AddProducts\" element={<AddProducts />}/>\n      <Route path=\"/AllProducts\" element={<AllProducts />}/>\n      <Route path=\"/updateproduct\" element={<UpdateProduct />}/>\n      <Route path=\"/DeleteProduct\" element={<DeleteProduct />}/>\n      <Route path=\"/cart\" element={<Cart />}/>\n    </Routes>\n    </BrowserRouter>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,OAAOA,iBAAiB,MAAM,yCAAyC;AACvE,OAAOC,IAAI,MAAM,4BAA4B;AAC7C,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,IAAI,MAAM,4BAA4B;AAC7C,SAAQC,aAAa,EAAEC,KAAK,EAAEC,MAAM,QAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC9D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACJ,aAAa;IAAAM,QAAA,eACdF,OAAA,CAACF,MAAM;MAAAI,QAAA,gBACLF,OAAA,CAACH,KAAK;QAACM,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACb,IAAI;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACnCR,OAAA,CAACH,KAAK;QAACM,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEJ,OAAA,CAACZ,KAAK;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACzCR,OAAA,CAACH,KAAK;QAACM,IAAI,EAAC,SAAS;QAACC,OAAO,eAAEJ,OAAA,CAACX,MAAM;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC5CR,OAAA,CAACH,KAAK;QAACM,IAAI,EAAC,oBAAoB;QAACC,OAAO,eAAEJ,OAAA,CAACd,iBAAiB;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAClER,OAAA,CAACH,KAAK;QAACM,IAAI,EAAC,iBAAiB;QAACC,OAAO,eAAEJ,OAAA,CAACV,cAAc;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC5DR,OAAA,CAACH,KAAK;QAACM,IAAI,EAAC,cAAc;QAACC,OAAO,eAAEJ,OAAA,CAACT,WAAW;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACtDR,OAAA,CAACH,KAAK;QAACM,IAAI,EAAC,cAAc;QAACC,OAAO,eAAEJ,OAAA,CAACR,WAAW;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eACtDR,OAAA,CAACH,KAAK;QAACM,IAAI,EAAC,gBAAgB;QAACC,OAAO,eAAEJ,OAAA,CAACP,aAAa;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC1DR,OAAA,CAACH,KAAK;QAACM,IAAI,EAAC,gBAAgB;QAACC,OAAO,eAAEJ,OAAA,CAACN,aAAa;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC,eAC1DR,OAAA,CAACH,KAAK;QAACM,IAAI,EAAC,OAAO;QAACC,OAAO,eAAEJ,OAAA,CAACL,IAAI;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEpB;AAACC,EAAA,GAjBQR,GAAG;AAmBZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}