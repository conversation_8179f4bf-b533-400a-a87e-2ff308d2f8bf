/* Professional MyOrders Component */
.orders-container {
    min-height: calc(100vh - 80px);
    background: var(--gray-50);
    padding-bottom: var(--spacing-20);
}

/* Header */
.orders-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-8) 0;
    box-shadow: var(--shadow-sm);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    text-align: center;
}

.header-content h1 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-content p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
    margin: 0;
}

/* Order Statistics */
.order-stats {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-8) var(--spacing-6);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    font-size: 2.5rem;
    padding: var(--spacing-3);
    border-radius: var(--radius-lg);
    background: var(--gray-100);
}

.stat-info h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-1) 0;
}

.stat-info p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* Filter Controls */
.filter-controls {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6) var(--spacing-6);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.filter-group label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.filter-select {
    padding: var(--spacing-2) var(--spacing-4);
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    background: var(--white);
    transition: all var(--transition-fast);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(102 126 234 / 0.1);
}

/* Orders Content */
.orders-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

.orders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--spacing-6);
}

.order-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.order-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
}

.order-number .label {
    color: var(--gray-500);
    font-size: var(--font-size-xs);
    display: block;
}

.order-number .value {
    font-weight: 700;
    color: var(--gray-900);
    font-size: var(--font-size-base);
}

.order-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

/* Status Colors */
.status-pending { background: #fef3c7; color: #92400e; }
.status-confirmed { background: #d1fae5; color: #065f46; }
.status-processing { background: #dbeafe; color: #1e40af; }
.status-shipped { background: #e0e7ff; color: #3730a3; }
.status-delivered { background: #dcfce7; color: #166534; }
.status-cancelled { background: #fee2e2; color: #991b1b; }
.status-default { background: var(--gray-200); color: var(--gray-700); }

.order-info {
    margin-bottom: var(--spacing-4);
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2);
}

.info-row .label {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.info-row .value {
    font-weight: 500;
    color: var(--gray-900);
    font-size: var(--font-size-sm);
}

.info-row .amount {
    font-weight: 700;
    color: var(--primary-color);
    font-size: var(--font-size-base);
}

.info-row .tracking {
    font-family: monospace;
    background: var(--gray-100);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
}

.order-actions {
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
}

/* Empty State */
.empty-orders {
    text-align: center;
    padding: var(--spacing-20) var(--spacing-6);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-6);
    opacity: 0.5;
}

.empty-orders h3 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--spacing-4);
}

.empty-orders p {
    color: var(--gray-500);
    font-size: var(--font-size-base);
    max-width: 500px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Loading Spinner */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    gap: var(--spacing-4);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

.order-details-modal {
    background: var(--white);
    border-radius: var(--radius-2xl);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-2xl);
    animation: slideUp 0.3s ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-6) var(--spacing-8);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.modal-content {
    padding: var(--spacing-8);
}

.order-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-8);
    padding-bottom: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.order-summary h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.order-details-grid {
    display: grid;
    gap: var(--spacing-8);
}

.detail-section h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3) 0;
    border-bottom: 1px solid var(--gray-100);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item span:first-child {
    color: var(--gray-600);
    font-weight: 500;
}

.detail-item span:last-child {
    color: var(--gray-900);
    font-weight: 600;
}

.order-items {
    display: grid;
    gap: var(--spacing-4);
}

.order-item {
    display: flex;
    gap: var(--spacing-4);
    padding: var(--spacing-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
}

.item-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--radius-md);
}

.item-details h5 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-2) 0;
}

.item-details p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0 0 var(--spacing-1) 0;
}

.item-details .subtotal {
    font-weight: 600;
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .orders-grid {
        grid-template-columns: 1fr;
    }
    
    .order-stats {
        grid-template-columns: repeat(2, 1fr);
        padding: var(--spacing-6) var(--spacing-4);
    }
    
    .orders-content,
    .filter-controls {
        padding: 0 var(--spacing-4);
    }
    
    .order-actions {
        flex-direction: column;
    }
    
    .order-details-modal {
        margin: var(--spacing-4);
        width: calc(100% - 2rem);
    }
    
    .modal-content {
        padding: var(--spacing-6);
    }
    
    .order-item {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .order-stats {
        grid-template-columns: 1fr;
    }
    
    .order-header {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: flex-start;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: flex-start;
    }
}
