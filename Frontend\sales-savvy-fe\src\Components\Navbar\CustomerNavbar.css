/* Full Screen Customer Navbar Styles */
.customer-navbar {
    background: linear-gradient(135deg, #28a745, #20c997);
    padding: 20px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    position: sticky;
    top: 0;
    z-index: 1000;
    font-family: 'Poppins', sans-serif;
    width: 100%;
    min-height: 80px;
}

.navbar-container {
    width: 100%;
    max-width: 1400px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
}

.navbar-logo {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.navbar-logo:hover {
    transform: scale(1.05);
}

.logo-text {
    font-size: 0.9em;
    letter-spacing: 1px;
}

.user-badge {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.navbar-menu {
    display: flex;
    align-items: center;
}

.navbar-links {
    list-style: none;
    display: flex;
    gap: 35px;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-item {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    padding: 12px 20px;
    border-radius: 12px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    position: relative;
    backdrop-filter: blur(10px);
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.nav-item::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%) scaleX(0);
    width: 80%;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: transform 0.3s ease;
}

.nav-item:hover::before {
    transform: translateX(-50%) scaleX(1);
}

.cart-item {
    position: relative;
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.cart-item:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.5);
}

.logout-btn {
    background: rgba(220, 53, 69, 0.8);
    color: white;
    border: 2px solid rgba(220, 53, 69, 0.6);
    padding: 12px 20px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: 'Poppins', sans-serif;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.logout-btn:hover {
    background: rgba(220, 53, 69, 1);
    border-color: rgba(220, 53, 69, 1);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* Mobile Menu Toggle */
.navbar-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.navbar-toggle span {
    width: 25px;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .customer-navbar {
        padding: 15px 0;
        min-height: 70px;
    }

    .navbar-container {
        padding: 0 20px;
    }

    .navbar-logo {
        font-size: 1.6rem;
        gap: 15px;
    }

    .user-badge {
        font-size: 0.6rem;
        padding: 6px 12px;
    }

    .navbar-toggle {
        display: flex;
    }

    .navbar-menu {
        position: fixed;
        top: 70px;
        right: -100%;
        width: 320px;
        height: calc(100vh - 70px);
        background: linear-gradient(135deg, #28a745, #20c997);
        transition: right 0.3s ease;
        padding: 40px 0;
        box-shadow: -10px 0 30px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(20px);
    }

    .navbar-menu.active {
        right: 0;
    }

    .navbar-links {
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }

    .navbar-links li {
        width: 100%;
    }

    .nav-item {
        padding: 20px 30px;
        width: 100%;
        border-radius: 0;
        justify-content: flex-start;
        font-size: 1.1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-item::before {
        display: none;
    }

    .nav-item:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: none;
        padding-left: 40px;
        box-shadow: none;
    }

    .cart-item {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .logout-btn {
        margin: 30px 30px 20px;
        justify-content: center;
        width: calc(100% - 60px);
        padding: 15px 20px;
        font-size: 1.1rem;
    }

    /* Hamburger Animation */
    .navbar-menu.active ~ .navbar-toggle span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .navbar-menu.active ~ .navbar-toggle span:nth-child(2) {
        opacity: 0;
    }

    .navbar-menu.active ~ .navbar-toggle span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

@media (max-width: 480px) {
    .customer-navbar {
        padding: 12px 15px;
    }
    
    .navbar-logo {
        font-size: 1.3em;
    }
    
    .user-badge {
        font-size: 0.4em;
        padding: 3px 8px;
    }
    
    .navbar-menu {
        width: 100%;
        right: -100%;
    }
}
