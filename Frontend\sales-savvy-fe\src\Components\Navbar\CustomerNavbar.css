/* Base Styles */
.customer-navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    border-radius: 1rem;
    margin-top: 1rem;
}

.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Brand Styles */
.navbar-brand {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    cursor: pointer;
    color: rgb(194, 236, 6);
    font-weight: 600;
    font-size: 1.2rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin: auto;
}

.brand-icon {
    font-size: 1.5rem;
}

/* User Menu Styles */
.user-menu-container {
    position: relative;
    margin-left: auto;
    margin-right: 1rem;
    margin-top: 1rem;
}

.user-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: .7rem;
}

.user-badge:hover {
    background: rgba(206, 38, 38, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.user-icon {
    font-size: 1rem;
}

.dropdown-icon {
    transition: transform 0.3s ease;
}

.dropdown-icon.open {
    transform: rotate(180deg);
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgb(255, 255, 255);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
    min-width: 150px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    margin-top: 0.5rem;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: fit-content;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    color: red;
    font-size: 1rem;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
    
}

.dropdown-item:hover {
    background: #f5f5f5;
    color: #000;
}

/* Rest of your existing navbar styles remain the same */

/* Navigation Links */
.navbar-links {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.6rem 1rem;
    /* border-radius: 0.5rem; */
    background: none;
    border: none;
    color: white;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: .5rem;
    width: fit-content;
}

.nav-link:hover {
    background: rgba(240, 235, 235, 0.2);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    font-weight: 600;
}

.cart-link {
    position: relative;
}

.link-icon {
    font-size: 1rem;
    width: 1.2rem;
}

/* Logout Button */
.logout-btn {
    background: rgba(220, 53, 69, 0.8);
    color: white;
    border: none;
    padding: 0.6rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background: rgba(220, 53, 69, 1);
}

/* Mobile Menu */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.navbar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

/* Mobile Styles */
@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .navbar-links {
        position: fixed;
        top: 0;
        right: -100%;
        width: 70%;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        flex-direction: column;
        padding: 2rem;
        gap: 1rem;
        transition: right 0.3s ease;
        z-index: 1000;
    }

    .navbar-links.active {
        right: 0;
    }

    .nav-link {
        width: 100%;
        justify-content: flex-start;
    }

    .logout-btn {
        margin-top: 1rem;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .navbar-links {
        width: 85%;
    }
}