/* Customer Navbar Styles */
.customer-navbar {
    background: linear-gradient(135deg, #28a745, #20c997);
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    font-family: 'Poppins', sans-serif;
}

.navbar-logo {
    color: white;
    font-size: 1.8em;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-text {
    font-size: 0.9em;
}

.user-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.5em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.navbar-menu {
    display: flex;
    align-items: center;
}

.navbar-links {
    list-style: none;
    display: flex;
    gap: 25px;
    margin: 0;
    padding: 0;
    align-items: center;
}

.nav-item {
    color: white;
    font-size: 0.95em;
    font-weight: 500;
    padding: 10px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.cart-item {
    position: relative;
}

.cart-item:hover {
    background: rgba(255, 255, 255, 0.25) !important;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 0.95em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-family: 'Poppins', sans-serif;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.navbar-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.navbar-toggle span {
    width: 25px;
    height: 3px;
    background: white;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .customer-navbar {
        padding: 15px 20px;
    }
    
    .navbar-logo {
        font-size: 1.5em;
    }
    
    .navbar-toggle {
        display: flex;
    }
    
    .navbar-menu {
        position: fixed;
        top: 70px;
        right: -100%;
        width: 280px;
        height: calc(100vh - 70px);
        background: linear-gradient(135deg, #28a745, #20c997);
        transition: right 0.3s ease;
        padding: 30px 0;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    }
    
    .navbar-menu.active {
        right: 0;
    }
    
    .navbar-links {
        flex-direction: column;
        gap: 0;
        width: 100%;
    }
    
    .navbar-links li {
        width: 100%;
    }
    
    .nav-item {
        padding: 15px 30px;
        width: 100%;
        border-radius: 0;
        justify-content: flex-start;
    }
    
    .nav-item:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: none;
        padding-left: 40px;
    }
    
    .logout-btn {
        margin: 20px 30px;
        justify-content: center;
    }
    
    /* Hamburger Animation */
    .navbar-menu.active ~ .navbar-toggle span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .navbar-menu.active ~ .navbar-toggle span:nth-child(2) {
        opacity: 0;
    }
    
    .navbar-menu.active ~ .navbar-toggle span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

@media (max-width: 480px) {
    .customer-navbar {
        padding: 12px 15px;
    }
    
    .navbar-logo {
        font-size: 1.3em;
    }
    
    .user-badge {
        font-size: 0.4em;
        padding: 3px 8px;
    }
    
    .navbar-menu {
        width: 100%;
        right: -100%;
    }
}
