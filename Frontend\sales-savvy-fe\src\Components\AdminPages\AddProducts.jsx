import { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import Navbar from '../Navbar/Navbar';
import './AddProducts.css';
export default function AddProducts() {
    const navigate = useNavigate();
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        price: '',
        image: ''
      });
    
      const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
      };
    
      const handleSubmit = (e) => {
        e.preventDefault();
    
        axios.post('http://localhost:8080/addProduct', formData)
          .then(response => {
            alert('Product added successfully!');
            navigate('/AllProducts');
          })
          .catch(error => {
            console.error('Product addition error:', error);
            alert('Product addition failed. See console for details.');
          });
      };
      return (
        <>
            <Navbar />
            <div className="add-products-container">
                <div className="page-header">
                    <div className="page-header-content">
                        <div className="header-text">
                            <h1>Add New Product</h1>
                            <p>Add a new product to your inventory and start selling</p>
                        </div>
                        <div className="header-actions">
                            <button
                                className="btn btn-secondary"
                                onClick={() => navigate('/AllProducts')}
                            >
                                <span>📦</span>
                                View All Products
                            </button>
                        </div>
                    </div>
                </div>

                <div className="main-content-area">
                    <div className="form-container">
                        <div className="form-card">
                            <div className="form-header">
                                <h2>Product Information</h2>
                                <p>Fill in the details below to add your product</p>
                            </div>

                            <form onSubmit={handleSubmit} className="product-form">
                                <div className="form-group">
                                    <label className="form-label" htmlFor="name">Product Name</label>
                                    <input
                                        type="text"
                                        id="name"
                                        name="name"
                                        className="form-input"
                                        placeholder="Enter product name"
                                        required
                                        onChange={handleChange}
                                        value={formData.name}
                                    />
                                </div>

                                <div className="form-group">
                                    <label className="form-label" htmlFor="description">Description</label>
                                    <textarea
                                        id="description"
                                        name="description"
                                        className="form-input form-textarea"
                                        placeholder="Enter product description"
                                        required
                                        onChange={handleChange}
                                        value={formData.description}
                                        rows="4"
                                    />
                                </div>

                                <div className="form-group">
                                    <label className="form-label" htmlFor="price">Price (₹)</label>
                                    <input
                                        type="number"
                                        id="price"
                                        name="price"
                                        className="form-input"
                                        placeholder="Enter price"
                                        required
                                        onChange={handleChange}
                                        value={formData.price}
                                        min="0"
                                        step="0.01"
                                    />
                                </div>

                                <div className="form-group">
                                    <label className="form-label" htmlFor="image">Image URL</label>
                                    <input
                                        type="url"
                                        id="image"
                                        name="image"
                                        className="form-input"
                                        placeholder="Enter image URL"
                                        required
                                        onChange={handleChange}
                                        value={formData.image}
                                    />
                                </div>

                                <div className="form-actions">
                                    <button type="submit" className="btn btn-primary btn-lg">
                                        <span>✨</span>
                                        Add Product
                                    </button>
                                    <button
                                        type="button"
                                        className="btn btn-secondary btn-lg"
                                        onClick={() => navigate('/AllProducts')}
                                    >
                                        <span>❌</span>
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}