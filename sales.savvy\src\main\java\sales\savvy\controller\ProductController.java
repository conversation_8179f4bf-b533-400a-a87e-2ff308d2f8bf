package sales.savvy.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import sales.savvy.entity.Cart;
import sales.savvy.entity.User;
import sales.savvy.entity.CartItem;
import sales.savvy.entity.Product;
import sales.savvy.service.CartService;
import sales.savvy.service.ProductService;
import sales.savvy.service.UserService;
import org.springframework.web.bind.annotation.*;
// CrossOrigin annotation is used to allow cross-origin requests from any origin like http://localhost:3000 
@CrossOrigin("*")
@RestController
public class ProductController {

    @Autowired
ProductService service;

@Autowired
	UserService uService;
	
	@Autowired
	CartService cService;
	

@PostMapping("/addProduct")
public String addProduct(@RequestBody Product product) {
service.addProduct(product);
return "Product added successfully";
}

@GetMapping("/searchProduct")
public Product searchProduct(@RequestParam Long id) {
return service.searchProduct(id);
}

@GetMapping("/getAllProducts")
public List<Product> getAllProducts() {
return service.getAllProducts();
}

@PutMapping("/updateProduct")
public String updateProduct(@RequestBody Product product) {
service.updatePoduct(product);
return "Product updated successfully";
}

@DeleteMapping("/deleteProduct")
public String deleteProduct(@RequestParam Long id) {
service.deleteProduct(id);
return "Product deleted successfully";
}
  @PostMapping("/addToCart")
	    public String addToCart(@RequestBody CartItem item) {
	        User user = uService.getUser(item.getUsername());
	        if (user == null) return "user not found";

	        Product product = service.searchProduct(item.getProductId());
	        if (product == null) return "product not found";

	        Cart cart = user.getCart();
	        if (cart == null) {
	            cart = new Cart();
	            cart.setUser(user);
	            user.setCart(cart);
	            cService.addCart(cart);  // persist empty cart first
	        }

	        List<CartItem> items = cart.getCartItems();
	        if (items == null) items = new ArrayList<>();

	        boolean found = false;
	        for (CartItem ci : items) {
	            if (ci.getProduct().getId().equals(product.getId())) {
	                ci.setQuantity(ci.getQuantity() + item.getQuantity());
	                found = true;
	                break;
	            }
	        }

	        if (!found) {
	            CartItem newItem = new CartItem();
	            newItem.setCart(cart);
	            newItem.setProduct(product);
	            newItem.setQuantity(item.getQuantity());
	            items.add(newItem);
	        }

	        cart.setCartItems(items);
	        cService.addCart(cart);  // cascade saves CartItems
	        return "cart added";
	    }

	    @GetMapping("/getCart/{username}")
	    public List<CartItem> getCart(@PathVariable String username) {
	        User u = uService.getUser(username);
	        if (u == null || u.getCart() == null) return new ArrayList<>();
	        return u.getCart().getCartItems();
	    }


}
