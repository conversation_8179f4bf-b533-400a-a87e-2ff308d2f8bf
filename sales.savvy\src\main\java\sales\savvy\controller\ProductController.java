package sales.savvy.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import sales.savvy.entity.Product;
import sales.savvy.service.ProductService;
import org.springframework.web.bind.annotation.*;
// CrossOrigin annotation is used to allow cross-origin requests from any origin like http://localhost:3000 
@CrossOrigin("*")
@RestController
public class ProductController {
@Autowired
ProductService service;
@PostMapping("/addProduct")
public String addProduct(@RequestBody Product product) {
service.addProduct(product);
return "Product added successfully";
}

@GetMapping("/searchProduct")
public Product searchProduct(@RequestParam Long id) {
return service.searchProduct(id);
}

@GetMapping("/getAllProducts")
public List<Product> getAllProducts() {
return service.getAllProducts();
}

@PutMapping("/updateProduct")
public String updateProduct(@RequestBody Product product) {
service.updatePoduct(product);
return "Product updated successfully";
}

@DeleteMapping("/deleteProduct")
public String deleteProduct(@RequestParam Long id) {
service.deleteProduct(id);
return "Product deleted successfully";
}

}
