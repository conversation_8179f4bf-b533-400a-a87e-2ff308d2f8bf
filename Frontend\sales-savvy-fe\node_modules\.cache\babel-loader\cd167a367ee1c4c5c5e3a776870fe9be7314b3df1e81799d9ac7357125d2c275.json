{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\homePage\\\\CustomerDashboard.jsx\";\nimport React from 'react';\nimport CustomerNavbar from '../Navbar/CustomerNavbar';\nimport ProductsList from '../shared/ProductsList';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function CustomerDashboard() {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(CustomerNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ProductsList, {\n      userRole: \"customer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n}\n_c = CustomerDashboard;\nvar _c;\n$RefreshReg$(_c, \"CustomerDashboard\");", "map": {"version": 3, "names": ["React", "CustomerNavbar", "ProductsList", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CustomerDashboard", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "userRole", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/homePage/CustomerDashboard.jsx"], "sourcesContent": ["import React from 'react';\r\nimport CustomerNavbar from '../Navbar/CustomerNavbar';\r\nimport ProductsList from '../shared/ProductsList';\r\n\r\nexport default function CustomerDashboard() {\r\n    return (\r\n        <>\r\n            <CustomerNavbar />\r\n            <ProductsList userRole=\"customer\" />\r\n        </>\r\n    );\r\n}"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,YAAY,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,eAAe,SAASC,iBAAiBA,CAAA,EAAG;EACxC,oBACIH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACIJ,OAAA,CAACH,cAAc;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBR,OAAA,CAACF,YAAY;MAACW,QAAQ,EAAC;IAAU;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACtC,CAAC;AAEX;AAACE,EAAA,GAPuBP,iBAAiB;AAAA,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}