{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\Navbar\\\\CustomerNavbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport './CustomerNavbar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CustomerNavbar() {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [cartCount, setCartCount] = useState(0);\n  const navigate = useNavigate();\n  useEffect(() => {\n    // Update cart count when component mounts and when localStorage changes\n    const updateCartCount = async () => {\n      const username = localStorage.getItem('user');\n      if (!username) {\n        setCartCount(0);\n        return;\n      }\n      try {\n        const response = await axios.get(`http://localhost:8080/getCart/${username}`);\n        const cartItems = response.data || [];\n        const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);\n        setCartCount(totalItems);\n      } catch (error) {\n        console.error('Error fetching cart count:', error);\n        setCartCount(0);\n      }\n    };\n    updateCartCount();\n\n    // Custom event for same-page cart updates\n    window.addEventListener('cartUpdated', updateCartCount);\n    return () => {\n      window.removeEventListener('cartUpdated', updateCartCount);\n    };\n  }, []);\n  const handleLogout = () => {\n    const confirmed = window.confirm(\"Are you sure you want to logout?\");\n    if (confirmed) {\n      localStorage.removeItem('user');\n      localStorage.removeItem('cart');\n      sessionStorage.clear();\n      navigate('/');\n    }\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"customer-navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-logo\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\uD83D\\uDED2 \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"Sales Savvy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 26\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"user-badge\",\n        children: localStorage.getItem('user')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `navbar-menu ${isMenuOpen ? 'active' : ''}`,\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"navbar-links\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-item\",\n            onClick: () => navigate('/CustomerDashboard'),\n            children: \"\\uD83C\\uDFE0 Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-item cart-item\",\n            onClick: () => navigate('/cart'),\n            children: [\"\\uD83D\\uDED2 Cart (\", cartCount, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-item\",\n            children: \"\\uD83D\\uDCE6 My Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"logout-btn\",\n            onClick: handleLogout,\n            children: \"\\uD83D\\uDEAA Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-toggle\",\n      onClick: toggleMenu,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 9\n  }, this);\n}\n_s(CustomerNavbar, \"Sav86P+VcZZ+WSIiN1aGBu7F9z8=\", false, function () {\n  return [useNavigate];\n});\n_c = CustomerNavbar;\nvar _c;\n$RefreshReg$(_c, \"CustomerNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "axios", "jsxDEV", "_jsxDEV", "CustomerNavbar", "_s", "isMenuOpen", "setIsMenuOpen", "cartCount", "setCartCount", "navigate", "updateCartCount", "username", "localStorage", "getItem", "response", "get", "cartItems", "data", "totalItems", "reduce", "sum", "item", "quantity", "error", "console", "window", "addEventListener", "removeEventListener", "handleLogout", "confirmed", "confirm", "removeItem", "sessionStorage", "clear", "toggleMenu", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/Navbar/CustomerNavbar.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport './CustomerNavbar.css';\n\nexport default function CustomerNavbar() {\n    const [isMenuOpen, setIsMenuOpen] = useState(false);\n    const [cartCount, setCartCount] = useState(0);\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        // Update cart count when component mounts and when localStorage changes\n        const updateCartCount = async () => {\n            const username = localStorage.getItem('user');\n            if (!username) {\n                setCartCount(0);\n                return;\n            }\n\n            try {\n                const response = await axios.get(`http://localhost:8080/getCart/${username}`);\n                const cartItems = response.data || [];\n                const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);\n                setCartCount(totalItems);\n            } catch (error) {\n                console.error('Error fetching cart count:', error);\n                setCartCount(0);\n            }\n        };\n\n        updateCartCount();\n\n        // Custom event for same-page cart updates\n        window.addEventListener('cartUpdated', updateCartCount);\n\n        return () => {\n            window.removeEventListener('cartUpdated', updateCartCount);\n        };\n    }, []);\n\n    const handleLogout = () => {\n        const confirmed = window.confirm(\"Are you sure you want to logout?\");\n        if (confirmed) {\n            localStorage.removeItem('user');\n            localStorage.removeItem('cart');\n            sessionStorage.clear();\n            navigate('/');\n        }\n    };\n\n    const toggleMenu = () => {\n        setIsMenuOpen(!isMenuOpen);\n    };\n\n    return (\n        <nav className=\"customer-navbar\">\n            <div className=\"navbar-logo\">\n                <span>🛒 <span className=\"logo-text\">Sales Savvy</span></span>\n                <span className=\"user-badge\">{localStorage.getItem('user')}</span>\n            </div>\n            \n            <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>\n                <ul className=\"navbar-links\">\n                    <li>\n                        <span\n                            className=\"nav-item\"\n                            onClick={() => navigate('/CustomerDashboard')}\n                        >\n                            🏠 Home\n                        </span>\n                    </li>\n                    <li>\n                        <span\n                            className=\"nav-item cart-item\"\n                            onClick={() => navigate('/cart')}\n                        >\n                            🛒 Cart ({cartCount})\n                        </span>\n                    </li>\n                    <li>\n                        <span className=\"nav-item\">\n                            📦 My Orders\n                        </span>\n                    </li>\n                    <li>\n                        <button \n                            className=\"logout-btn\"\n                            onClick={handleLogout}\n                        >\n                            🚪 Logout\n                        </button>\n                    </li>\n                </ul>\n            </div>\n\n            <div className=\"navbar-toggle\" onClick={toggleMenu}>\n                <span></span>\n                <span></span>\n                <span></span>\n            </div>\n        </nav>\n    );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACrC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAMY,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZ;IACA,MAAMY,eAAe,GAAG,MAAAA,CAAA,KAAY;MAChC,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,IAAI,CAACF,QAAQ,EAAE;QACXH,YAAY,CAAC,CAAC,CAAC;QACf;MACJ;MAEA,IAAI;QACA,MAAMM,QAAQ,GAAG,MAAMd,KAAK,CAACe,GAAG,CAAC,iCAAiCJ,QAAQ,EAAE,CAAC;QAC7E,MAAMK,SAAS,GAAGF,QAAQ,CAACG,IAAI,IAAI,EAAE;QACrC,MAAMC,UAAU,GAAGF,SAAS,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC;QAC1Ed,YAAY,CAACU,UAAU,CAAC;MAC5B,CAAC,CAAC,OAAOK,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDf,YAAY,CAAC,CAAC,CAAC;MACnB;IACJ,CAAC;IAEDE,eAAe,CAAC,CAAC;;IAEjB;IACAe,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEhB,eAAe,CAAC;IAEvD,OAAO,MAAM;MACTe,MAAM,CAACE,mBAAmB,CAAC,aAAa,EAAEjB,eAAe,CAAC;IAC9D,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAGJ,MAAM,CAACK,OAAO,CAAC,kCAAkC,CAAC;IACpE,IAAID,SAAS,EAAE;MACXjB,YAAY,CAACmB,UAAU,CAAC,MAAM,CAAC;MAC/BnB,YAAY,CAACmB,UAAU,CAAC,MAAM,CAAC;MAC/BC,cAAc,CAACC,KAAK,CAAC,CAAC;MACtBxB,QAAQ,CAAC,GAAG,CAAC;IACjB;EACJ,CAAC;EAED,MAAMyB,UAAU,GAAGA,CAAA,KAAM;IACrB5B,aAAa,CAAC,CAACD,UAAU,CAAC;EAC9B,CAAC;EAED,oBACIH,OAAA;IAAKiC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC5BlC,OAAA;MAAKiC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBlC,OAAA;QAAAkC,QAAA,GAAM,eAAG,eAAAlC,OAAA;UAAMiC,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9DtC,OAAA;QAAMiC,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAExB,YAAY,CAACC,OAAO,CAAC,MAAM;MAAC;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAENtC,OAAA;MAAKiC,SAAS,EAAE,eAAe9B,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAA+B,QAAA,eACxDlC,OAAA;QAAIiC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACxBlC,OAAA;UAAAkC,QAAA,eACIlC,OAAA;YACIiC,SAAS,EAAC,UAAU;YACpBM,OAAO,EAAEA,CAAA,KAAMhC,QAAQ,CAAC,oBAAoB,CAAE;YAAA2B,QAAA,EACjD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLtC,OAAA;UAAAkC,QAAA,eACIlC,OAAA;YACIiC,SAAS,EAAC,oBAAoB;YAC9BM,OAAO,EAAEA,CAAA,KAAMhC,QAAQ,CAAC,OAAO,CAAE;YAAA2B,QAAA,GACpC,qBACY,EAAC7B,SAAS,EAAC,GACxB;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLtC,OAAA;UAAAkC,QAAA,eACIlC,OAAA;YAAMiC,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAE3B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLtC,OAAA;UAAAkC,QAAA,eACIlC,OAAA;YACIiC,SAAS,EAAC,YAAY;YACtBM,OAAO,EAAEb,YAAa;YAAAQ,QAAA,EACzB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENtC,OAAA;MAAKiC,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEP,UAAW;MAAAE,QAAA,gBAC/ClC,OAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtC,OAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtC,OAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACpC,EAAA,CAjGuBD,cAAc;EAAA,QAGjBJ,WAAW;AAAA;AAAA2C,EAAA,GAHRvC,cAAc;AAAA,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}