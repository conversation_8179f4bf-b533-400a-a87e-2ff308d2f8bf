package sales.savvy.dto;

import java.util.List;

public class OrderRequest {
    private String username;
    private String paymentId;
    private String razorpayOrderId;
    private Double totalAmount;
    private String shippingAddress;
    private String phoneNumber;
    private String email;
    private List<OrderItemDTO> orderItems;
    
    // Constructors
    public OrderRequest() {}
    
    public OrderRequest(String username, String paymentId, String razorpayOrderId, 
                       Double totalAmount, String shippingAddress, String phoneNumber, 
                       String email, List<OrderItemDTO> orderItems) {
        this.username = username;
        this.paymentId = paymentId;
        this.razorpayOrderId = razorpayOrderId;
        this.totalAmount = totalAmount;
        this.shippingAddress = shippingAddress;
        this.phoneNumber = phoneNumber;
        this.email = email;
        this.orderItems = orderItems;
    }
    
    // Getters and Setters
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPaymentId() {
        return paymentId;
    }
    
    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }
    
    public String getRazorpayOrderId() {
        return razorpayOrderId;
    }
    
    public void setRazorpayOrderId(String razorpayOrderId) {
        this.razorpayOrderId = razorpayOrderId;
    }
    
    public Double getTotalAmount() {
        return totalAmount;
    }
    
    public void setTotalAmount(Double totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public String getShippingAddress() {
        return shippingAddress;
    }
    
    public void setShippingAddress(String shippingAddress) {
        this.shippingAddress = shippingAddress;
    }
    
    public String getPhoneNumber() {
        return phoneNumber;
    }
    
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public List<OrderItemDTO> getOrderItems() {
        return orderItems;
    }
    
    public void setOrderItems(List<OrderItemDTO> orderItems) {
        this.orderItems = orderItems;
    }
    
    @Override
    public String toString() {
        return "OrderRequest{" +
                "username='" + username + '\'' +
                ", paymentId='" + paymentId + '\'' +
                ", razorpayOrderId='" + razorpayOrderId + '\'' +
                ", totalAmount=" + totalAmount +
                ", shippingAddress='" + shippingAddress + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", email='" + email + '\'' +
                ", orderItems=" + orderItems +
                '}';
    }
}
