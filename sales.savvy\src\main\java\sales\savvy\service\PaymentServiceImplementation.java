package sales.savvy.service;

import com.razorpay.Order;
import com.razorpay.RazorpayClient;
import com.razorpay.RazorpayException;
import com.razorpay.Utils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import sales.savvy.dto.PaymentRequest;
import sales.savvy.dto.PaymentResponse;
import sales.savvy.dto.PaymentVerificationRequest;
import sales.savvy.entity.Payment;
import sales.savvy.repository.PaymentRepository;

import java.util.List;
import java.util.Optional;

@Service
public class PaymentServiceImplementation implements PaymentService {
    
    @Autowired
    private PaymentRepository paymentRepository;
    
    @Value("${razorpay.key.id}")
    private String razorpayKeyId;
    
    @Value("${razorpay.key.secret}")
    private String razorpayKeySecret;
    
    private RazorpayClient getRazorpayClient() throws RazorpayException {
        return new RazorpayClient(razorpayKeyId, razorpayKeySecret);
    }
    
    @Override
    public PaymentResponse createOrder(PaymentRequest paymentRequest) throws Exception {
        try {
            RazorpayClient razorpayClient = getRazorpayClient();
            
            // Create order request
            JSONObject orderRequest = new JSONObject();
            orderRequest.put("amount", paymentRequest.getAmount() * 100); // Amount in paise
            orderRequest.put("currency", "INR");
            orderRequest.put("receipt", paymentRequest.getReceipt());
            
            // Create order
            Order order = razorpayClient.orders.create(orderRequest);
            
            // Save payment record
            Payment payment = new Payment(
                order.get("id"),
                paymentRequest.getAmount(),
                paymentRequest.getUsername(),
                paymentRequest.getReceipt()
            );
            paymentRepository.save(payment);
            
            // Return response
            return new PaymentResponse(
                order.get("id"),
                paymentRequest.getAmount(),
                "INR",
                razorpayKeyId,
                paymentRequest.getReceipt(),
                paymentRequest.getUsername()
            );
            
        } catch (RazorpayException e) {
            throw new Exception("Failed to create Razorpay order: " + e.getMessage());
        }
    }
    
    @Override
    public String verifyPayment(PaymentVerificationRequest verificationRequest) throws Exception {
        try {
            // Verify signature
            JSONObject options = new JSONObject();
            options.put("razorpay_order_id", verificationRequest.getRazorpayOrderId());
            options.put("razorpay_payment_id", verificationRequest.getRazorpayPaymentId());
            options.put("razorpay_signature", verificationRequest.getRazorpaySignature());
            
            boolean isValidSignature = Utils.verifyPaymentSignature(options, razorpayKeySecret);
            
            if (isValidSignature) {
                // Update payment record
                Optional<Payment> paymentOpt = paymentRepository.findByRazorpayOrderId(
                    verificationRequest.getRazorpayOrderId()
                );
                
                if (paymentOpt.isPresent()) {
                    Payment payment = paymentOpt.get();
                    payment.setRazorpayPaymentId(verificationRequest.getRazorpayPaymentId());
                    payment.setRazorpaySignature(verificationRequest.getRazorpaySignature());
                    payment.setStatus("PAID");
                    paymentRepository.save(payment);
                    
                    return "Payment verified successfully";
                } else {
                    throw new Exception("Payment record not found");
                }
            } else {
                // Update payment status to failed
                Optional<Payment> paymentOpt = paymentRepository.findByRazorpayOrderId(
                    verificationRequest.getRazorpayOrderId()
                );
                
                if (paymentOpt.isPresent()) {
                    Payment payment = paymentOpt.get();
                    payment.setStatus("FAILED");
                    paymentRepository.save(payment);
                }
                
                throw new Exception("Invalid payment signature");
            }
            
        } catch (RazorpayException e) {
            throw new Exception("Payment verification failed: " + e.getMessage());
        }
    }
    
    @Override
    public Payment getPaymentByOrderId(String orderId) {
        return paymentRepository.findByRazorpayOrderId(orderId).orElse(null);
    }
    
    @Override
    public Payment getPaymentByPaymentId(String paymentId) {
        return paymentRepository.findByRazorpayPaymentId(paymentId).orElse(null);
    }
    
    @Override
    public List<Payment> getPaymentsByUsername(String username) {
        return paymentRepository.findByUsername(username);
    }
    
    @Override
    public List<Payment> getPaymentHistory(String username) {
        return paymentRepository.findByUsernameOrderByCreatedAtDesc(username);
    }
    
    @Override
    public List<Payment> getPaymentsByStatus(String status) {
        return paymentRepository.findByStatus(status);
    }
    
    @Override
    public Payment updatePaymentStatus(String orderId, String status) {
        Optional<Payment> paymentOpt = paymentRepository.findByRazorpayOrderId(orderId);
        if (paymentOpt.isPresent()) {
            Payment payment = paymentOpt.get();
            payment.setStatus(status);
            return paymentRepository.save(payment);
        }
        return null;
    }
}
