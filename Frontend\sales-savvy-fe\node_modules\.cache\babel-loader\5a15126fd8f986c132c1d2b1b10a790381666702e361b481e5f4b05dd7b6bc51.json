{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\homePage\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport AddProducts from '../AdminPages/AddProducts';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function AdminDashboard() {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"You are at Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            navigate(\"/addProduct\");\n          },\n          children: \"Add Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate(\"/updateProduct\"),\n          children: \"Update Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate(\"/deleteProduct\"),\n          children: \"Delete Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n}\n_s(AdminDashboard, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useNavigate", "AddProducts", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminDashboard", "_s", "navigate", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/homePage/AdminDashboard.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport AddProducts from '../AdminPages/AddProducts';\r\nexport default function AdminDashboard() {\r\n    const navigate = useNavigate();\r\n    return (\r\n        <>\r\n        <div>\r\n            <h3>\r\n                You are at Admin Dashboard\r\n            </h3>\r\n            <div>\r\n                <button onClick={()=>{navigate(\"/addProduct\")}}>\r\n                    Add Product\r\n                </button>\r\n                <button onClick={() => navigate(\"/updateProduct\")}>\r\n                    Update Product\r\n                </button>\r\n                <button onClick={() => navigate(\"/deleteProduct\")}>\r\n                    Delete Product\r\n                </button>\r\n            </div>\r\n        </div>\r\n        </>\r\n    )\r\n}   "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACpD,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,oBACIG,OAAA,CAAAE,SAAA;IAAAI,QAAA,eACAN,OAAA;MAAAM,QAAA,gBACIN,OAAA;QAAAM,QAAA,EAAI;MAEJ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLV,OAAA;QAAAM,QAAA,gBACIN,OAAA;UAAQW,OAAO,EAAEA,CAAA,KAAI;YAACN,QAAQ,CAAC,aAAa,CAAC;UAAA,CAAE;UAAAC,QAAA,EAAC;QAEhD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTV,OAAA;UAAQW,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAAC,gBAAgB,CAAE;UAAAC,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTV,OAAA;UAAQW,OAAO,EAAEA,CAAA,KAAMN,QAAQ,CAAC,gBAAgB,CAAE;UAAAC,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC,gBACJ,CAAC;AAEX;AAACN,EAAA,CAtBuBD,cAAc;EAAA,QACjBN,WAAW;AAAA;AAAAe,EAAA,GADRT,cAAc;AAAA,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}