{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\Navbar\\\\CustomerNavbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from 'axios';\nimport './CustomerNavbar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CustomerNavbar() {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [cartCount, setCartCount] = useState(0);\n  const navigate = useNavigate();\n  const location = useLocation();\n  useEffect(() => {\n    // Update cart count when component mounts and when localStorage changes\n    const updateCartCount = async () => {\n      const username = localStorage.getItem('user');\n      if (!username) {\n        setCartCount(0);\n        return;\n      }\n      try {\n        const response = await axios.get(`http://localhost:8080/getCartCount/${username}`);\n        setCartCount(response.data || 0);\n      } catch (error) {\n        console.error('Error fetching cart count:', error);\n        setCartCount(0);\n      }\n    };\n    updateCartCount();\n\n    // Custom event for same-page cart updates\n    window.addEventListener('cartUpdated', updateCartCount);\n    return () => {\n      window.removeEventListener('cartUpdated', updateCartCount);\n    };\n  }, []);\n  const handleLogout = () => {\n    const confirmed = window.confirm(\"Are you sure you want to logout?\");\n    if (confirmed) {\n      localStorage.removeItem('user');\n      localStorage.removeItem('cart');\n      sessionStorage.clear();\n      navigate('/');\n    }\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  const isActive = path => {\n    return location.pathname === path;\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"customer-navbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDED2 \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"Sales Savvy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 30\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"user-badge\",\n          children: localStorage.getItem('user')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `navbar-menu ${isMenuOpen ? 'active' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"navbar-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `nav-item ${isActive('/CustomerDashboard') ? 'active' : ''}`,\n              onClick: () => {\n                navigate('/CustomerDashboard');\n                setIsMenuOpen(false);\n              },\n              children: \"\\uD83C\\uDFE0 Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `nav-item cart-item ${isActive('/cart') ? 'active' : ''}`,\n              onClick: () => {\n                navigate('/cart');\n                setIsMenuOpen(false);\n              },\n              children: [\"\\uD83D\\uDED2 Cart (\", cartCount, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-item\",\n              children: \"\\uD83D\\uDCE6 My Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"logout-btn\",\n              onClick: handleLogout,\n              children: \"\\uD83D\\uDEAA Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-toggle\",\n        onClick: toggleMenu,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 9\n  }, this);\n}\n_s(CustomerNavbar, \"eQC/yaSqEXtT+4XYAuu0u+Rv4Gg=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = CustomerNavbar;\nvar _c;\n$RefreshReg$(_c, \"CustomerNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "axios", "jsxDEV", "_jsxDEV", "CustomerNavbar", "_s", "isMenuOpen", "setIsMenuOpen", "cartCount", "setCartCount", "navigate", "location", "updateCartCount", "username", "localStorage", "getItem", "response", "get", "data", "error", "console", "window", "addEventListener", "removeEventListener", "handleLogout", "confirmed", "confirm", "removeItem", "sessionStorage", "clear", "toggleMenu", "isActive", "path", "pathname", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/Navbar/CustomerNavbar.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from 'axios';\nimport './CustomerNavbar.css';\n\nexport default function CustomerNavbar() {\n    const [isMenuOpen, setIsMenuOpen] = useState(false);\n    const [cartCount, setCartCount] = useState(0);\n    const navigate = useNavigate();\n    const location = useLocation();\n\n    useEffect(() => {\n        // Update cart count when component mounts and when localStorage changes\n        const updateCartCount = async () => {\n            const username = localStorage.getItem('user');\n            if (!username) {\n                setCartCount(0);\n                return;\n            }\n\n            try {\n                const response = await axios.get(`http://localhost:8080/getCartCount/${username}`);\n                setCartCount(response.data || 0);\n            } catch (error) {\n                console.error('Error fetching cart count:', error);\n                setCartCount(0);\n            }\n        };\n\n        updateCartCount();\n\n        // Custom event for same-page cart updates\n        window.addEventListener('cartUpdated', updateCartCount);\n\n        return () => {\n            window.removeEventListener('cartUpdated', updateCartCount);\n        };\n    }, []);\n\n    const handleLogout = () => {\n        const confirmed = window.confirm(\"Are you sure you want to logout?\");\n        if (confirmed) {\n            localStorage.removeItem('user');\n            localStorage.removeItem('cart');\n            sessionStorage.clear();\n            navigate('/');\n        }\n    };\n\n    const toggleMenu = () => {\n        setIsMenuOpen(!isMenuOpen);\n    };\n\n    const isActive = (path) => {\n        return location.pathname === path;\n    };\n\n    return (\n        <nav className=\"customer-navbar\">\n            <div className=\"navbar-container\">\n                <div className=\"navbar-logo\">\n                    <span>🛒 <span className=\"logo-text\">Sales Savvy</span></span>\n                    <span className=\"user-badge\">{localStorage.getItem('user')}</span>\n                </div>\n\n                <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>\n                    <ul className=\"navbar-links\">\n                        <li>\n                            <span\n                                className={`nav-item ${isActive('/CustomerDashboard') ? 'active' : ''}`}\n                                onClick={() => {\n                                    navigate('/CustomerDashboard');\n                                    setIsMenuOpen(false);\n                                }}\n                            >\n                                🏠 Home\n                            </span>\n                        </li>\n                        <li>\n                            <span\n                                className={`nav-item cart-item ${isActive('/cart') ? 'active' : ''}`}\n                                onClick={() => {\n                                    navigate('/cart');\n                                    setIsMenuOpen(false);\n                                }}\n                            >\n                                🛒 Cart ({cartCount})\n                            </span>\n                        </li>\n                        <li>\n                            <span className=\"nav-item\">\n                                📦 My Orders\n                            </span>\n                        </li>\n                        <li>\n                            <button\n                                className=\"logout-btn\"\n                                onClick={handleLogout}\n                            >\n                                🚪 Logout\n                            </button>\n                        </li>\n                    </ul>\n                </div>\n\n                <div className=\"navbar-toggle\" onClick={toggleMenu}>\n                    <span></span>\n                    <span></span>\n                    <span></span>\n                </div>\n            </div>\n        </nav>\n    );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACrC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAMa,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACZ;IACA,MAAMc,eAAe,GAAG,MAAAA,CAAA,KAAY;MAChC,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,IAAI,CAACF,QAAQ,EAAE;QACXJ,YAAY,CAAC,CAAC,CAAC;QACf;MACJ;MAEA,IAAI;QACA,MAAMO,QAAQ,GAAG,MAAMf,KAAK,CAACgB,GAAG,CAAC,sCAAsCJ,QAAQ,EAAE,CAAC;QAClFJ,YAAY,CAACO,QAAQ,CAACE,IAAI,IAAI,CAAC,CAAC;MACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDV,YAAY,CAAC,CAAC,CAAC;MACnB;IACJ,CAAC;IAEDG,eAAe,CAAC,CAAC;;IAEjB;IACAS,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEV,eAAe,CAAC;IAEvD,OAAO,MAAM;MACTS,MAAM,CAACE,mBAAmB,CAAC,aAAa,EAAEX,eAAe,CAAC;IAC9D,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAGJ,MAAM,CAACK,OAAO,CAAC,kCAAkC,CAAC;IACpE,IAAID,SAAS,EAAE;MACXX,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;MAC/Bb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;MAC/BC,cAAc,CAACC,KAAK,CAAC,CAAC;MACtBnB,QAAQ,CAAC,GAAG,CAAC;IACjB;EACJ,CAAC;EAED,MAAMoB,UAAU,GAAGA,CAAA,KAAM;IACrBvB,aAAa,CAAC,CAACD,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMyB,QAAQ,GAAIC,IAAI,IAAK;IACvB,OAAOrB,QAAQ,CAACsB,QAAQ,KAAKD,IAAI;EACrC,CAAC;EAED,oBACI7B,OAAA;IAAK+B,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC5BhC,OAAA;MAAK+B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BhC,OAAA;QAAK+B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBhC,OAAA;UAAAgC,QAAA,GAAM,eAAG,eAAAhC,OAAA;YAAM+B,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9DpC,OAAA;UAAM+B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAErB,YAAY,CAACC,OAAO,CAAC,MAAM;QAAC;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eAENpC,OAAA;QAAK+B,SAAS,EAAE,eAAe5B,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAA6B,QAAA,eACxDhC,OAAA;UAAI+B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACxBhC,OAAA;YAAAgC,QAAA,eACIhC,OAAA;cACI+B,SAAS,EAAE,YAAYH,QAAQ,CAAC,oBAAoB,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cACxES,OAAO,EAAEA,CAAA,KAAM;gBACX9B,QAAQ,CAAC,oBAAoB,CAAC;gBAC9BH,aAAa,CAAC,KAAK,CAAC;cACxB,CAAE;cAAA4B,QAAA,EACL;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACLpC,OAAA;YAAAgC,QAAA,eACIhC,OAAA;cACI+B,SAAS,EAAE,sBAAsBH,QAAQ,CAAC,OAAO,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cACrES,OAAO,EAAEA,CAAA,KAAM;gBACX9B,QAAQ,CAAC,OAAO,CAAC;gBACjBH,aAAa,CAAC,KAAK,CAAC;cACxB,CAAE;cAAA4B,QAAA,GACL,qBACY,EAAC3B,SAAS,EAAC,GACxB;YAAA;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACLpC,OAAA;YAAAgC,QAAA,eACIhC,OAAA;cAAM+B,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAE3B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACLpC,OAAA;YAAAgC,QAAA,eACIhC,OAAA;cACI+B,SAAS,EAAC,YAAY;cACtBM,OAAO,EAAEhB,YAAa;cAAAW,QAAA,EACzB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENpC,OAAA;QAAK+B,SAAS,EAAC,eAAe;QAACM,OAAO,EAAEV,UAAW;QAAAK,QAAA,gBAC/ChC,OAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpC,OAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpC,OAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAAClC,EAAA,CA5GuBD,cAAc;EAAA,QAGjBL,WAAW,EACXC,WAAW;AAAA;AAAAyC,EAAA,GAJRrC,cAAc;AAAA,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}