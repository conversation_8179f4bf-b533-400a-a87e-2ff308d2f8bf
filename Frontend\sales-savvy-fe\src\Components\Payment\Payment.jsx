import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { getUsername, showSuccessMessage, handleApiError } from '../shared/CartUtils';
import './Payment.css';

export default function Payment({ amount, cartItems, onPaymentSuccess, onPaymentFailure }) {
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    const loadRazorpayScript = () => {
        return new Promise((resolve) => {
            const script = document.createElement('script');
            script.src = 'https://checkout.razorpay.com/v1/checkout.js';
            script.onload = () => resolve(true);
            script.onerror = () => resolve(false);
            document.body.appendChild(script);
        });
    };

    const handlePayment = async () => {
        const username = getUsername();
        if (!username) {
            showSuccessMessage('Please login to make payment');
            navigate('/login');
            return;
        }

        if (!amount || amount <= 0) {
            showSuccessMessage('Invalid payment amount');
            return;
        }

        setLoading(true);

        try {
            // Load Razorpay script
            const scriptLoaded = await loadRazorpayScript();
            if (!scriptLoaded) {
                throw new Error('Failed to load Razorpay SDK');
            }

            // Create order
            const orderResponse = await axios.post('http://localhost:8080/api/payment/create-order', {
                amount: amount,
                username: username,
                receipt: `receipt_${Date.now()}`
            });

            const orderData = orderResponse.data;

            // Razorpay options
            const options = {
                key: orderData.key,
                amount: orderData.amount * 100, // Amount in paise
                currency: orderData.currency,
                name: 'Sales Savvy',
                description: 'Payment for your order',
                order_id: orderData.orderId,
                handler: async function (response) {
                    try {
                        // Verify payment
                        const verificationResponse = await axios.post('http://localhost:8080/api/payment/verify', {
                            razorpayOrderId: response.razorpay_order_id,
                            razorpayPaymentId: response.razorpay_payment_id,
                            razorpaySignature: response.razorpay_signature,
                            username: username
                        });

                        if (verificationResponse.data.status === 'success') {
                            // Create order after successful payment
                            try {
                                const orderItems = cartItems?.map(item => ({
                                    productId: item.product.id,
                                    productName: item.product.name,
                                    productImage: item.product.image,
                                    price: item.product.price,
                                    quantity: item.quantity,
                                    subtotal: item.product.price * item.quantity
                                })) || [];

                                const orderRequest = {
                                    username: username,
                                    paymentId: response.razorpay_payment_id,
                                    razorpayOrderId: response.razorpay_order_id,
                                    totalAmount: amount,
                                    shippingAddress: "Default Address", // You can make this dynamic
                                    phoneNumber: "9999999999", // You can make this dynamic
                                    email: `${username}@example.com`, // You can make this dynamic
                                    orderItems: orderItems
                                };

                                await axios.post('http://localhost:8080/api/orders/create', orderRequest);
                                showSuccessMessage('Payment successful! Order created.');
                            } catch (orderError) {
                                console.error('Order creation error:', orderError);
                                showSuccessMessage('Payment successful! Order creation pending.');
                            }

                            if (onPaymentSuccess) {
                                onPaymentSuccess(response);
                            }
                        } else {
                            throw new Error('Payment verification failed');
                        }
                    } catch (error) {
                        console.error('Payment verification error:', error);
                        handleApiError(error, 'Payment verification failed');
                        if (onPaymentFailure) {
                            onPaymentFailure(error);
                        }
                    }
                },
                prefill: {
                    name: username,
                    email: `${username}@example.com`,
                    contact: '9999999999'
                },
                notes: {
                    address: 'Sales Savvy Corporate Office'
                },
                theme: {
                    color: '#667eea'
                },
                modal: {
                    ondismiss: function() {
                        setLoading(false);
                        if (onPaymentFailure) {
                            onPaymentFailure(new Error('Payment cancelled by user'));
                        }
                    }
                }
            };

            const razorpay = new window.Razorpay(options);
            razorpay.open();

        } catch (error) {
            console.error('Payment error:', error);
            handleApiError(error, 'Failed to initiate payment');
            if (onPaymentFailure) {
                onPaymentFailure(error);
            }
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="payment-component">
            <div className="payment-summary">
                <h3>Payment Summary</h3>
                <div className="amount-display">
                    <span className="currency">₹</span>
                    <span className="amount">{amount?.toFixed(2) || '0.00'}</span>
                </div>
            </div>
            
            <button 
                className="btn btn-primary btn-lg payment-btn"
                onClick={handlePayment}
                disabled={loading || !amount || amount <= 0}
            >
                {loading ? (
                    <>
                        <span className="loading-spinner"></span>
                        Processing...
                    </>
                ) : (
                    <>
                        <span>💳</span>
                        Pay Now
                    </>
                )}
            </button>
            
            <div className="payment-info">
                <p className="secure-text">
                    <span>🔒</span>
                    Secure payment powered by Razorpay
                </p>
                <div className="accepted-methods">
                    <span>Accepted: </span>
                    <span className="payment-methods">
                        💳 Cards • 🏦 UPI • 💰 Net Banking • 📱 Wallets
                    </span>
                </div>
            </div>
        </div>
    );
}
