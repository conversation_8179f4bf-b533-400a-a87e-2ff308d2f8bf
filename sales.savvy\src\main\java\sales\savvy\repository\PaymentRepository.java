package sales.savvy.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import sales.savvy.entity.Payment;

import java.util.List;
import java.util.Optional;

@Repository
public interface PaymentRepository extends JpaRepository<Payment, Long> {
    
    Optional<Payment> findByRazorpayOrderId(String razorpayOrderId);
    
    Optional<Payment> findByRazorpayPaymentId(String razorpayPaymentId);
    
    List<Payment> findByUsername(String username);
    
    List<Payment> findByUsernameOrderByCreatedAtDesc(String username);
    
    List<Payment> findByStatus(String status);
    
    List<Payment> findByUsernameAndStatus(String username, String status);
}
