import React from 'react';
import { useNavigate } from 'react-router-dom';
import AllProducts from '../AdminPages/AllProducts';

export default function CustomerDashboard() {
    const navigate = useNavigate();

    return (
        <>
            <nav className="navbar" style={{
                background: 'linear-gradient(135deg, #28a745, #20c997)',
                padding: '15px 30px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                position: 'sticky',
                top: 0,
                zIndex: 1000,
                fontFamily: 'Poppins, sans-serif'
            }}>
                <div style={{
                    color: 'white',
                    fontSize: '1.8em',
                    fontWeight: '700',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                }}>
                    🛒 <span style={{fontSize: '0.9em'}}>Sales Savvy - Customer</span>
                </div>
                <button
                    onClick={() => {
                        const confirmed = window.confirm("Are you sure you want to logout?");
                        if (confirmed) {
                            localStorage.removeItem('user');
                            sessionStorage.clear();
                            navigate('/');
                        }
                    }}
                    style={{
                        background: 'rgba(255, 255, 255, 0.2)',
                        color: 'white',
                        border: '2px solid rgba(255, 255, 255, 0.3)',
                        padding: '8px 16px',
                        borderRadius: '8px',
                        fontSize: '0.95em',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '6px',
                        fontFamily: 'Poppins, sans-serif'
                    }}
                    onMouseOver={(e) => {
                        e.target.style.background = 'rgba(255, 255, 255, 0.3)';
                        e.target.style.borderColor = 'rgba(255, 255, 255, 0.5)';
                        e.target.style.transform = 'translateY(-2px)';
                    }}
                    onMouseOut={(e) => {
                        e.target.style.background = 'rgba(255, 255, 255, 0.2)';
                        e.target.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                        e.target.style.transform = 'translateY(0)';
                    }}
                >
                    🚪 Logout
                </button>
            </nav>

            <div style={{
                maxWidth: '1200px',
                margin: '0 auto',
                padding: '30px 20px',
                fontFamily: 'Poppins, sans-serif',
                backgroundColor: '#f8f9fa',
                minHeight: 'calc(100vh - 80px)'
            }}>
                <div style={{
                    textAlign: 'center',
                    marginBottom: '30px',
                    padding: '20px 0'
                }}>
                    <h1 style={{
                        color: '#333',
                        fontSize: '2.5rem',
                        marginBottom: '10px',
                        fontWeight: '600',
                        background: 'linear-gradient(135deg, #28a745, #20c997)',
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        backgroundClip: 'text'
                    }}>
                        Customer Dashboard
                    </h1>
                    <p style={{
                        color: '#666',
                        fontSize: '1.1rem',
                        margin: 0
                    }}>
                        Browse and explore our amazing products
                    </p>
                </div>

                <AllProducts />
            </div>
        </>
    );
}