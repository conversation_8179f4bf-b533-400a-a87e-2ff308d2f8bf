/* Professional Home Page Styles */
.home-container {
  min-height: 100vh;
  background: var(--gray-50);
}

/* Hero Section */
.hero-section {
  background: var(--primary-gradient);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="white" stop-opacity="0.1"/><stop offset="100%" stop-color="white" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
  pointer-events: none;
}

.hero-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-16);
  align-items: center;
  position: relative;
  z-index: 1;
}

.brand-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.logo-icon {
  font-size: 3rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--white);
  letter-spacing: -0.02em;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: 800;
  color: var(--white);
  line-height: 1.1;
  margin-bottom: var(--spacing-6);
  letter-spacing: -0.02em;
}

.gradient-text {
  background: linear-gradient(135deg, #ffeaa7, #fab1a0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: var(--font-size-xl);
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: var(--spacing-8);
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

/* Hero Visual */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.feature-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
  max-width: 400px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  text-align: center;
  transition: all var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
}

.feature-card:nth-child(3) {
  grid-column: 1 / -1;
  max-width: 200px;
  margin: 0 auto;
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-2);
}

.feature-card h3 {
  color: var(--white);
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-1);
}

.feature-card p {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-size-sm);
}

/* Features Section */
.features-section {
  padding: var(--spacing-20) 0;
  background: var(--white);
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-16);
}

.section-header h2 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
}

.section-header p {
  font-size: var(--font-size-xl);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-8);
}

.feature-item {
  text-align: center;
  padding: var(--spacing-8);
  border-radius: var(--radius-2xl);
  transition: all var(--transition-normal);
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.feature-icon-large {
  font-size: 4rem;
  margin-bottom: var(--spacing-6);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.feature-item h3 {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
}

.feature-item p {
  font-size: var(--font-size-base);
  color: var(--gray-600);
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: var(--spacing-20) 0;
  background: var(--gray-900);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  pointer-events: none;
}

.cta-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.cta-content h2 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--white);
  margin-bottom: var(--spacing-4);
}

.cta-content p {
  font-size: var(--font-size-xl);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  gap: var(--spacing-4);
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
    text-align: center;
  }
  
  .hero-title {
    font-size: var(--font-size-4xl);
  }
  
  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
  }
}

@media (max-width: 768px) {
  .hero-content {
    padding: 0 var(--spacing-4);
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .hero-description {
    font-size: var(--font-size-lg);
  }
  
  .hero-actions {
    justify-content: center;
  }
  
  .feature-cards {
    grid-template-columns: 1fr;
    max-width: 250px;
  }
  
  .feature-card:nth-child(3) {
    grid-column: 1;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .section-header h2,
  .cta-content h2 {
    font-size: var(--font-size-3xl);
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: var(--font-size-2xl);
  }
  
  .section-header h2,
  .cta-content h2 {
    font-size: var(--font-size-2xl);
  }
  
  .feature-item {
    padding: var(--spacing-6);
  }
}
