/* Professional Admin Dashboard */
.admin-dashboard-container {
  min-height: 100vh;
  background: var(--gray-50);
}

.admin-dashboard {
  padding-bottom: var(--spacing-20);
}

/* Dashboard Header */
.dashboard-header {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  padding: var(--spacing-8) 0;
  box-shadow: var(--shadow-sm);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-6);
}

.header-text h1 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text p {
  color: var(--gray-600);
  font-size: var(--font-size-lg);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-4);
}

.primary-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4) var(--spacing-6);
  background: var(--primary-gradient);
  color: var(--white);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-base);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-md);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-icon {
  font-size: var(--font-size-lg);
}

/* Stats Section */
.stats-section {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--spacing-12) var(--spacing-6) 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-16);
}

.stat-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.stat-icon {
  font-size: 2.5rem;
  padding: var(--spacing-3);
  border-radius: var(--radius-lg);
  background: var(--gray-100);
}

.stat-icon.products {
  background: linear-gradient(135deg, #667eea20, #764ba220);
}

.stat-icon.revenue {
  background: linear-gradient(135deg, #56ab2f20, #a8e6cf20);
}

.stat-icon.orders {
  background: linear-gradient(135deg, #4facfe20, #00f2fe20);
}

.stat-icon.customers {
  background: linear-gradient(135deg, #f093fb20, #f5576c20);
}

.stat-value {
  font-size: var(--font-size-3xl);
  font-weight: 800;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  color: var(--gray-600);
  font-size: var(--font-size-base);
  font-weight: 500;
}

.stat-change {
  font-size: var(--font-size-sm);
  font-weight: 600;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
}

.stat-change.positive {
  color: var(--success-color);
  background: #56ab2f20;
}

.stat-change.negative {
  color: var(--warning-color);
  background: #f5576c20;
}

/* Management Section */
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);
}

.management-section h2 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-8);
  text-align: center;
}

.management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-8);
}

.management-card {
  background: var(--white);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  cursor: pointer;
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
}

.management-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.management-card:hover::before {
  transform: scaleX(1);
}

.management-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-6);
}

.card-icon-large {
  font-size: 3rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.card-badge {
  background: var(--primary-gradient);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 600;
  box-shadow: var(--shadow-md);
}

.card-content h3 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-3);
}

.card-content p {
  color: var(--gray-600);
  font-size: var(--font-size-base);
  line-height: 1.6;
  margin-bottom: var(--spacing-4);
}

.card-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.card-features li {
  color: var(--gray-700);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.card-features li::before {
  content: '✓';
  color: var(--success-color);
  font-weight: 700;
  font-size: var(--font-size-base);
}

.card-footer {
  margin-top: var(--spacing-6);
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--gray-200);
}

.card-action {
  color: var(--primary-color);
  font-weight: 600;
  font-size: var(--font-size-base);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-4);
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .management-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header-content,
  .stats-section,
  .main-content {
    padding: 0 var(--spacing-4);
  }
  
  .header-text h1 {
    font-size: var(--font-size-3xl);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
  }
  
  .management-card {
    padding: var(--spacing-6);
  }
}

@media (max-width: 480px) {
  .header-text h1 {
    font-size: var(--font-size-2xl);
  }
  
  .stat-card {
    padding: var(--spacing-6);
  }
  
  .management-section h2 {
    font-size: var(--font-size-2xl);
  }
}
