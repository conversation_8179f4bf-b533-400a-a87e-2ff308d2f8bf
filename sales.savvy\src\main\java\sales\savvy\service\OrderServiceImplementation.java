package sales.savvy.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import sales.savvy.dto.OrderRequest;
import sales.savvy.entity.Order;
import sales.savvy.repository.OrderRepository;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.Random;

@Service
public class OrderServiceImplementation implements OrderService {
    
    @Autowired
    private OrderRepository orderRepository;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public Order createOrder(OrderRequest orderRequest) throws Exception {
        try {
            // Generate unique order number
            String orderNumber = generateOrderNumber();
            
            // Convert order items to JSON string
            String orderItemsJson = objectMapper.writeValueAsString(orderRequest.getOrderItems());
            
            // Create new order
            Order order = new Order(
                orderNumber,
                orderRequest.getUsername(),
                orderRequest.getTotalAmount(),
                orderRequest.getPaymentId(),
                orderRequest.getRazorpayOrderId()
            );
            
            order.setShippingAddress(orderRequest.getShippingAddress());
            order.setPhoneNumber(orderRequest.getPhoneNumber());
            order.setEmail(orderRequest.getEmail());
            order.setOrderItems(orderItemsJson);
            order.setStatus("CONFIRMED"); // Order is confirmed after payment
            
            // Generate tracking number
            order.setTrackingNumber("TRK" + System.currentTimeMillis());
            
            return orderRepository.save(order);
            
        } catch (Exception e) {
            throw new Exception("Failed to create order: " + e.getMessage());
        }
    }
    
    @Override
    public Order getOrderByOrderNumber(String orderNumber) {
        return orderRepository.findByOrderNumber(orderNumber).orElse(null);
    }
    
    @Override
    public Order getOrderByRazorpayOrderId(String razorpayOrderId) {
        return orderRepository.findByRazorpayOrderId(razorpayOrderId).orElse(null);
    }
    
    @Override
    public Order getOrderByPaymentId(String paymentId) {
        return orderRepository.findByPaymentId(paymentId).orElse(null);
    }
    
    @Override
    public List<Order> getOrdersByUsername(String username) {
        return orderRepository.findByUsername(username);
    }
    
    @Override
    public List<Order> getOrderHistory(String username) {
        return orderRepository.findByUsernameOrderByCreatedAtDesc(username);
    }
    
    @Override
    public List<Order> getOrdersByStatus(String status) {
        return orderRepository.findByStatus(status);
    }
    
    @Override
    public List<Order> getOrdersByUsernameAndStatus(String username, String status) {
        return orderRepository.findByUsernameAndStatus(username, status);
    }
    
    @Override
    public List<Order> getOrdersByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return orderRepository.findByDateRange(startDate, endDate);
    }
    
    @Override
    public List<Order> getUserOrdersByDateRange(String username, LocalDateTime startDate, LocalDateTime endDate) {
        return orderRepository.findByUsernameAndDateRange(username, startDate, endDate);
    }
    
    @Override
    public Order updateOrderStatus(String orderNumber, String status) {
        Optional<Order> orderOpt = orderRepository.findByOrderNumber(orderNumber);
        if (orderOpt.isPresent()) {
            Order order = orderOpt.get();
            order.setStatus(status);
            
            // Update estimated delivery based on status
            if ("SHIPPED".equals(status)) {
                order.setEstimatedDelivery(LocalDateTime.now().plusDays(3));
            } else if ("DELIVERED".equals(status)) {
                order.setEstimatedDelivery(LocalDateTime.now());
            }
            
            return orderRepository.save(order);
        }
        return null;
    }
    
    @Override
    public Order updateTrackingNumber(String orderNumber, String trackingNumber) {
        Optional<Order> orderOpt = orderRepository.findByOrderNumber(orderNumber);
        if (orderOpt.isPresent()) {
            Order order = orderOpt.get();
            order.setTrackingNumber(trackingNumber);
            return orderRepository.save(order);
        }
        return null;
    }
    
    @Override
    public Order getOrderByTrackingNumber(String trackingNumber) {
        return orderRepository.findByTrackingNumber(trackingNumber).orElse(null);
    }
    
    @Override
    public OrderStatistics getUserOrderStatistics(String username) {
        Long totalOrders = orderRepository.countByUsername(username);
        Double totalSpent = orderRepository.getTotalSpentByUsername(username);
        
        Long pendingOrders = (long) orderRepository.findByUsernameAndStatus(username, "PENDING").size() +
                            (long) orderRepository.findByUsernameAndStatus(username, "CONFIRMED").size() +
                            (long) orderRepository.findByUsernameAndStatus(username, "PROCESSING").size();
        
        Long completedOrders = (long) orderRepository.findByUsernameAndStatus(username, "DELIVERED").size();
        Long cancelledOrders = (long) orderRepository.findByUsernameAndStatus(username, "CANCELLED").size();
        
        return new OrderStatistics(totalOrders, totalSpent != null ? totalSpent : 0.0, 
                                  pendingOrders, completedOrders, cancelledOrders);
    }
    
    @Override
    public Order cancelOrder(String orderNumber, String username) throws Exception {
        Optional<Order> orderOpt = orderRepository.findByOrderNumber(orderNumber);
        if (orderOpt.isPresent()) {
            Order order = orderOpt.get();
            
            // Verify ownership
            if (!order.getUsername().equals(username)) {
                throw new Exception("Unauthorized: You can only cancel your own orders");
            }
            
            // Check if order can be cancelled
            if ("DELIVERED".equals(order.getStatus()) || "CANCELLED".equals(order.getStatus())) {
                throw new Exception("Cannot cancel order with status: " + order.getStatus());
            }
            
            order.setStatus("CANCELLED");
            return orderRepository.save(order);
        } else {
            throw new Exception("Order not found");
        }
    }
    
    @Override
    public List<Order> getOverdueOrders() {
        return orderRepository.findOverdueOrders(LocalDateTime.now());
    }
    
    @Override
    public String generateOrderNumber() {
        // Generate order number: ORD + YYYYMMDD + Random 4 digits
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        Random random = new Random();
        int randomNum = 1000 + random.nextInt(9000); // 4-digit random number
        return "ORD" + dateStr + randomNum;
    }
}
