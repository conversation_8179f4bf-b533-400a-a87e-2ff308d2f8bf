import React, { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './Login.css';
export default function Login() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();

    try {
      const response = await axios.post('http://localhost:8080/login', {
        username,
        password
      });

      console.log('Login success:', response.data);
      console.log(response.data);
      if(response.data === "Admin"){
          navigate('/AdminDashboard');
          alert('Login successful');
        } else if(response.data === "User"){
            navigate('/CustomerDashboard');
            localStorage.setItem('user', username);
            alert('Login successful');
      } else {
          alert('Login failed. Check your credentials.');
      }
    } catch (error) {
      console.error('Login error:', error);
      alert('Login failed. Check your credentials.');
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <div className="brand-logo">
            <span className="logo-icon">🛒</span>
            <span className="logo-text">Sales Savvy</span>
          </div>
          <h2>Welcome Back</h2>
          <p>Sign in to your account to continue</p>
        </div>

        <form onSubmit={handleLogin} className="login-form">
          <div className="form-group">
            <label className="form-label">Username</label>
            <input
              type="text"
              className="form-input"
              placeholder="Enter your username"
              required
              onChange={(e) => setUsername(e.target.value)}
            />
          </div>

          <div className="form-group">
            <label className="form-label">Password</label>
            <input
              type="password"
              className="form-input"
              placeholder="Enter your password"
              required
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>

          <button type="submit" className="btn btn-primary btn-lg login-btn">
            <span>🚀</span>
            Sign In
          </button>

          <div className="login-footer">
            <p>Don't have an account?
              <button
                type="button"
                className="link-btn"
                onClick={() => navigate('/signup')}
              >
                Create one here
              </button>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}
