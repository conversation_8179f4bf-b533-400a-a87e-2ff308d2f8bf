import React, { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './Login.css';
export default function Login() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const navigate = useNavigate(); 

  const handleLogin = async (e) => {
    e.preventDefault();

    try {
      const response = await axios.post('http://localhost:8080/login', {
        username,
        password
      });

      console.log('Login success:', response.data);
      console.log(response.data);
      if(response.data === "Admin"){
          navigate('/AdminDashboard');
          alert('Login successful');
        } else if(response.data === "User"){
            navigate('/CustomerDashboard');
            alert('Login successful');
      } else {
          alert('Login failed. Check your credentials.');
      }
    } catch (error) {
      console.error('Login error:', error);
      alert('<PERSON><PERSON> failed. Check your credentials.');
    }
  };

  return (
    <div className="login-container">
      <form onSubmit={handleLogin}>
        <h2>Login</h2>
        <input
          type="text"
          placeholder="Username"
          required
          onChange={(e) => setUsername(e.target.value)}
        />
        <input
          type="password"
          placeholder="Password"
          required
          onChange={(e) => setPassword(e.target.value)}
        />
        <button type="submit">Login</button>
      </form>
    </div>
  );
}
