import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import './Navbar.css';

export default function Navbar() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const navigate = useNavigate();
    const location = useLocation();

    const handleLogout = () => {
        const confirmed = window.confirm("Are you sure you want to logout?");
        if (confirmed) {
            // Clear any stored user data if you have any
            localStorage.removeItem('user');
            sessionStorage.clear();
            navigate('/');
        }
    };

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    const isActive = (path) => {
        return location.pathname === path;
    };

    return (
        <nav className="navbar">
            <div className="navbar-logo">
                <Link to="/AdminDashboard">
                    🛒 <span>Sales Savvy</span>
                </Link>
            </div>

            <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>
                <ul className="navbar-links">
                    <li>
                        <Link
                            to="/AdminDashboard"
                            className={isActive('/AdminDashboard') ? 'active' : ''}
                            onClick={() => setIsMenuOpen(false)}
                        >
                            📊 Dashboard
                        </Link>
                    </li>
                    <li>
                        <Link
                            to="/AllProducts"
                            className={isActive('/AllProducts') ? 'active' : ''}
                            onClick={() => setIsMenuOpen(false)}
                        >
                            📦 All Products
                        </Link>
                    </li>
                    <li>
                        <Link
                            to="/AddProducts"
                            className={isActive('/AddProducts') ? 'active' : ''}
                            onClick={() => setIsMenuOpen(false)}
                        >
                            ➕ Add Product
                        </Link>
                    </li>
                    <li>
                        <button
                            className="logout-btn"
                            onClick={handleLogout}
                        >
                            🚪 Logout
                        </button>
                    </li>
                </ul>
            </div>

            <div className="navbar-toggle" onClick={toggleMenu}>
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    );
}
