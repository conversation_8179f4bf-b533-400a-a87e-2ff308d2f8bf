// Cart utility functions for consistent cart operations across components

export const triggerCartUpdate = () => {
    // Trigger custom event to update cart count in navbar
    window.dispatchEvent(new Event('cartUpdated'));
};

export const getUsername = () => {
    return localStorage.getItem('user');
};

export const isLoggedIn = () => {
    return !!localStorage.getItem('user');
};

export const handleApiError = (error, defaultMessage = 'An error occurred') => {
    console.error('API Error:', error);
    
    if (error.response) {
        // Server responded with error status
        const message = error.response.data || `Server error: ${error.response.status}`;
        alert(typeof message === 'string' ? message : defaultMessage);
    } else if (error.request) {
        // Request was made but no response received
        alert('Network error. Please check your connection and try again.');
    } else {
        // Something else happened
        alert(defaultMessage);
    }
};

export const showSuccessMessage = (message) => {
    alert(message);
};

export const showErrorMessage = (message) => {
    alert(message);
};
