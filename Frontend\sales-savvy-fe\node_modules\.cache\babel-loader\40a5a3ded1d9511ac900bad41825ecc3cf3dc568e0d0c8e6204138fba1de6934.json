{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\homePage\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport AddProducts from '../AdminPages/AddProducts';\nimport AllProducts from '../AdminPages/AllProducts';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function AdminDashboard() {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"You are at Admin Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            navigate(\"/AddProducts\");\n          },\n          children: \"Add Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(AllProducts, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 16\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 9\n    }, this)\n  }, void 0, false);\n}\n_s(AdminDashboard, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useNavigate", "AddProducts", "AllProducts", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminDashboard", "_s", "navigate", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/homePage/AdminDashboard.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport AddProducts from '../AdminPages/AddProducts';\r\nimport AllProducts from '../AdminPages/AllProducts';\r\nexport default function AdminDashboard() {\r\n    const navigate = useNavigate();\r\n    return (\r\n        <>\r\n        <div>\r\n            <h3>\r\n                You are at Admin Dashboard\r\n            </h3>\r\n            <div>\r\n                <button onClick={()=>{navigate(\"/AddProducts\")}}>\r\n                    Add Product\r\n                </button>\r\n                <br/>\r\n               <AllProducts />\r\n            </div>\r\n        </div>\r\n        </>\r\n    )\r\n}   "], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,WAAW,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACpD,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,oBACII,OAAA,CAAAE,SAAA;IAAAI,QAAA,eACAN,OAAA;MAAAM,QAAA,gBACIN,OAAA;QAAAM,QAAA,EAAI;MAEJ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLV,OAAA;QAAAM,QAAA,gBACIN,OAAA;UAAQW,OAAO,EAAEA,CAAA,KAAI;YAACN,QAAQ,CAAC,cAAc,CAAC;UAAA,CAAE;UAAAC,QAAA,EAAC;QAEjD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTV,OAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACNV,OAAA,CAACF,WAAW;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC,gBACJ,CAAC;AAEX;AAACN,EAAA,CAlBuBD,cAAc;EAAA,QACjBP,WAAW;AAAA;AAAAgB,EAAA,GADRT,cAAc;AAAA,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}