{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\AdminPages\\\\AddProducts.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport Navbar from '../Navbar/Navbar';\nimport './AddProducts.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function AddProducts() {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    price: '',\n    image: ''\n  });\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    axios.post('http://localhost:8080/addProduct', formData).then(response => {\n      alert('Product added successfully!');\n      navigate('/AllProducts');\n    }).catch(error => {\n      console.error('Product addition error:', error);\n      alert('Product addition failed. See console for details.');\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 10\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"You are at Add Products Page\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"name\",\n            placeholder: \"Name\",\n            required: true,\n            onChange: handleChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"description\",\n            placeholder: \"Description\",\n            required: true,\n            onChange: handleChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            name: \"price\",\n            placeholder: \"Price\",\n            required: true,\n            onChange: handleChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            name: \"image\",\n            placeholder: \"Image\",\n            required: true,\n            onChange: handleChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            children: \"Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n}\n_s(AddProducts, \"1b6pU0N1CrFxDTMaOooi/mrMl7Q=\", false, function () {\n  return [useNavigate];\n});\n_c = AddProducts;\nvar _c;\n$RefreshReg$(_c, \"AddProducts\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useNavigate", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AddProducts", "_s", "navigate", "formData", "setFormData", "name", "description", "price", "image", "handleChange", "e", "value", "target", "prev", "handleSubmit", "preventDefault", "post", "then", "response", "alert", "catch", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "required", "onChange", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/AdminPages/AddProducts.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport Navbar from '../Navbar/Navbar';\r\nimport './AddProducts.css';\r\nexport default function AddProducts() {\r\n    const navigate = useNavigate();\r\n    const [formData, setFormData] = useState({\r\n        name: '',\r\n        description: '',\r\n        price: '',\r\n        image: ''\r\n      });\r\n    \r\n      const handleChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setFormData(prev => ({ ...prev, [name]: value }));\r\n      };\r\n    \r\n      const handleSubmit = (e) => {\r\n        e.preventDefault();\r\n    \r\n        axios.post('http://localhost:8080/addProduct', formData)\r\n          .then(response => {\r\n            alert('Product added successfully!');\r\n            navigate('/AllProducts');\r\n          })\r\n          .catch(error => {\r\n            console.error('Product addition error:', error);\r\n            alert('Product addition failed. See console for details.');\r\n          });\r\n      };\r\n      return (\r\n        <>\r\n         <Navbar />\r\n        <div>\r\n            <h3>\r\n                You are at Add Products Page\r\n            </h3>\r\n            <div>\r\n                <form onSubmit={handleSubmit}>\r\n                    <input type=\"text\" name=\"name\" placeholder=\"Name\" required onChange={handleChange} />\r\n                    <input type=\"text\" name=\"description\" placeholder=\"Description\" required onChange={handleChange} />\r\n                    <input type=\"number\" name=\"price\" placeholder=\"Price\" required onChange={handleChange} />\r\n                    <input type=\"text\" name=\"image\" placeholder=\"Image\" required onChange={handleChange} />\r\n                    <button type=\"submit\">Add Product</button>\r\n                </form>\r\n            </div>\r\n        </div>\r\n        </>\r\n    )\r\n}           "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAC3B,eAAe,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC;IACrCa,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEL,IAAI;MAAEM;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCR,WAAW,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACR,IAAI,GAAGM;IAAM,CAAC,CAAC,CAAC;EACnD,CAAC;EAED,MAAMG,YAAY,GAAIJ,CAAC,IAAK;IAC1BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElBtB,KAAK,CAACuB,IAAI,CAAC,kCAAkC,EAAEb,QAAQ,CAAC,CACrDc,IAAI,CAACC,QAAQ,IAAI;MAChBC,KAAK,CAAC,6BAA6B,CAAC;MACpCjB,QAAQ,CAAC,cAAc,CAAC;IAC1B,CAAC,CAAC,CACDkB,KAAK,CAACC,KAAK,IAAI;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CF,KAAK,CAAC,mDAAmD,CAAC;IAC5D,CAAC,CAAC;EACN,CAAC;EACD,oBACEtB,OAAA,CAAAE,SAAA;IAAAwB,QAAA,gBACC1B,OAAA,CAACF,MAAM;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACX9B,OAAA;MAAA0B,QAAA,gBACI1B,OAAA;QAAA0B,QAAA,EAAI;MAEJ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL9B,OAAA;QAAA0B,QAAA,eACI1B,OAAA;UAAM+B,QAAQ,EAAEd,YAAa;UAAAS,QAAA,gBACzB1B,OAAA;YAAOgC,IAAI,EAAC,MAAM;YAACxB,IAAI,EAAC,MAAM;YAACyB,WAAW,EAAC,MAAM;YAACC,QAAQ;YAACC,QAAQ,EAAEvB;UAAa;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrF9B,OAAA;YAAOgC,IAAI,EAAC,MAAM;YAACxB,IAAI,EAAC,aAAa;YAACyB,WAAW,EAAC,aAAa;YAACC,QAAQ;YAACC,QAAQ,EAAEvB;UAAa;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnG9B,OAAA;YAAOgC,IAAI,EAAC,QAAQ;YAACxB,IAAI,EAAC,OAAO;YAACyB,WAAW,EAAC,OAAO;YAACC,QAAQ;YAACC,QAAQ,EAAEvB;UAAa;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzF9B,OAAA;YAAOgC,IAAI,EAAC,MAAM;YAACxB,IAAI,EAAC,OAAO;YAACyB,WAAW,EAAC,OAAO;YAACC,QAAQ;YAACC,QAAQ,EAAEvB;UAAa;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvF9B,OAAA;YAAQgC,IAAI,EAAC,QAAQ;YAAAN,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACJ,CAAC;AAEX;AAAC1B,EAAA,CA9CuBD,WAAW;EAAA,QACdN,WAAW;AAAA;AAAAuC,EAAA,GADRjC,WAAW;AAAA,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}