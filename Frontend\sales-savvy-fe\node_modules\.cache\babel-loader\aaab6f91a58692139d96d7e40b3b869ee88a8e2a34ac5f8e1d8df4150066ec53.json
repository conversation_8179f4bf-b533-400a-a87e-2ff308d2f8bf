{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\Navbar\\\\Navbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport './Navbar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Navbar() {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const navigate = useNavigate();\n  const location = useLocation();\n  const handleLogout = () => {\n    const confirmed = window.confirm(\"Are you sure you want to logout?\");\n    if (confirmed) {\n      // Clear any stored user data if you have any\n      localStorage.removeItem('user');\n      sessionStorage.clear();\n      navigate('/');\n    }\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  const isActive = path => {\n    return location.pathname === path;\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-logo\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/AdminDashboard\",\n        children: [\"\\uD83D\\uDED2 \", /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Sales Savvy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 24\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `navbar-menu ${isMenuOpen ? 'active' : ''}`,\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"navbar-links\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/AdminDashboard\",\n            className: isActive('/AdminDashboard') ? 'active' : '',\n            onClick: () => setIsMenuOpen(false),\n            children: \"\\uD83D\\uDCCA Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/AllProducts\",\n            className: isActive('/AllProducts') ? 'active' : '',\n            onClick: () => setIsMenuOpen(false),\n            children: \"\\uD83D\\uDCE6 All Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/AddProducts\",\n            className: isActive('/AddProducts') ? 'active' : '',\n            onClick: () => setIsMenuOpen(false),\n            children: \"\\u2795 Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"logout-btn\",\n            onClick: handleLogout,\n            children: \"\\uD83D\\uDEAA Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-toggle\",\n      onClick: toggleMenu,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n}\n_s(Navbar, \"m7Pq2tqtc0h3hrmWDMTgtLrNUsE=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "useLocation", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "isMenuOpen", "setIsMenuOpen", "navigate", "location", "handleLogout", "confirmed", "window", "confirm", "localStorage", "removeItem", "sessionStorage", "clear", "toggleMenu", "isActive", "path", "pathname", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/Navbar/Navbar.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\r\nimport './Navbar.css';\r\n\r\nexport default function Navbar() {\r\n    const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n    const navigate = useNavigate();\r\n    const location = useLocation();\r\n\r\n    const handleLogout = () => {\r\n        const confirmed = window.confirm(\"Are you sure you want to logout?\");\r\n        if (confirmed) {\r\n            // Clear any stored user data if you have any\r\n            localStorage.removeItem('user');\r\n            sessionStorage.clear();\r\n            navigate('/');\r\n        }\r\n    };\r\n\r\n    const toggleMenu = () => {\r\n        setIsMenuOpen(!isMenuOpen);\r\n    };\r\n\r\n    const isActive = (path) => {\r\n        return location.pathname === path;\r\n    };\r\n\r\n    return (\r\n        <nav className=\"navbar\">\r\n            <div className=\"navbar-logo\">\r\n                <Link to=\"/AdminDashboard\">\r\n                    🛒 <span>Sales Savvy</span>\r\n                </Link>\r\n            </div>\r\n\r\n            <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>\r\n                <ul className=\"navbar-links\">\r\n                    <li>\r\n                        <Link\r\n                            to=\"/AdminDashboard\"\r\n                            className={isActive('/AdminDashboard') ? 'active' : ''}\r\n                            onClick={() => setIsMenuOpen(false)}\r\n                        >\r\n                            📊 Dashboard\r\n                        </Link>\r\n                    </li>\r\n                    <li>\r\n                        <Link\r\n                            to=\"/AllProducts\"\r\n                            className={isActive('/AllProducts') ? 'active' : ''}\r\n                            onClick={() => setIsMenuOpen(false)}\r\n                        >\r\n                            📦 All Products\r\n                        </Link>\r\n                    </li>\r\n                    <li>\r\n                        <Link\r\n                            to=\"/AddProducts\"\r\n                            className={isActive('/AddProducts') ? 'active' : ''}\r\n                            onClick={() => setIsMenuOpen(false)}\r\n                        >\r\n                            ➕ Add Product\r\n                        </Link>\r\n                    </li>\r\n                    <li>\r\n                        <button\r\n                            className=\"logout-btn\"\r\n                            onClick={handleLogout}\r\n                        >\r\n                            🚪 Logout\r\n                        </button>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n\r\n            <div className=\"navbar-toggle\" onClick={toggleMenu}>\r\n                <span></span>\r\n                <span></span>\r\n                <span></span>\r\n            </div>\r\n        </nav>\r\n    );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,eAAe,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMU,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC;IACpE,IAAIF,SAAS,EAAE;MACX;MACAG,YAAY,CAACC,UAAU,CAAC,MAAM,CAAC;MAC/BC,cAAc,CAACC,KAAK,CAAC,CAAC;MACtBT,QAAQ,CAAC,GAAG,CAAC;IACjB;EACJ,CAAC;EAED,MAAMU,UAAU,GAAGA,CAAA,KAAM;IACrBX,aAAa,CAAC,CAACD,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMa,QAAQ,GAAIC,IAAI,IAAK;IACvB,OAAOX,QAAQ,CAACY,QAAQ,KAAKD,IAAI;EACrC,CAAC;EAED,oBACIjB,OAAA;IAAKmB,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACnBpB,OAAA;MAAKmB,SAAS,EAAC,aAAa;MAAAC,QAAA,eACxBpB,OAAA,CAACJ,IAAI;QAACyB,EAAE,EAAC,iBAAiB;QAAAD,QAAA,GAAC,eACpB,eAAApB,OAAA;UAAAoB,QAAA,EAAM;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENzB,OAAA;MAAKmB,SAAS,EAAE,eAAehB,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAAiB,QAAA,eACxDpB,OAAA;QAAImB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACxBpB,OAAA;UAAAoB,QAAA,eACIpB,OAAA,CAACJ,IAAI;YACDyB,EAAE,EAAC,iBAAiB;YACpBF,SAAS,EAAEH,QAAQ,CAAC,iBAAiB,CAAC,GAAG,QAAQ,GAAG,EAAG;YACvDU,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAAC,KAAK,CAAE;YAAAgB,QAAA,EACvC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLzB,OAAA;UAAAoB,QAAA,eACIpB,OAAA,CAACJ,IAAI;YACDyB,EAAE,EAAC,cAAc;YACjBF,SAAS,EAAEH,QAAQ,CAAC,cAAc,CAAC,GAAG,QAAQ,GAAG,EAAG;YACpDU,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAAC,KAAK,CAAE;YAAAgB,QAAA,EACvC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLzB,OAAA;UAAAoB,QAAA,eACIpB,OAAA,CAACJ,IAAI;YACDyB,EAAE,EAAC,cAAc;YACjBF,SAAS,EAAEH,QAAQ,CAAC,cAAc,CAAC,GAAG,QAAQ,GAAG,EAAG;YACpDU,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAAC,KAAK,CAAE;YAAAgB,QAAA,EACvC;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLzB,OAAA;UAAAoB,QAAA,eACIpB,OAAA;YACImB,SAAS,EAAC,YAAY;YACtBO,OAAO,EAAEnB,YAAa;YAAAa,QAAA,EACzB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENzB,OAAA;MAAKmB,SAAS,EAAC,eAAe;MAACO,OAAO,EAAEX,UAAW;MAAAK,QAAA,gBAC/CpB,OAAA;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzB,OAAA;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzB,OAAA;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACvB,EAAA,CA9EuBD,MAAM;EAAA,QAETJ,WAAW,EACXC,WAAW;AAAA;AAAA6B,EAAA,GAHR1B,MAAM;AAAA,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}