import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import Navbar from '../Navbar/Navbar';
import './AdminDashboard.css';

export default function AdminDashboard() {
    const navigate = useNavigate();
    const [stats, setStats] = useState({
        totalProducts: 0,
        totalUsers: 0,
        recentOrders: 0,
        revenue: 0
    });

    useEffect(() => {
        fetchDashboardStats();
    }, []);

    const fetchDashboardStats = async () => {
        try {
            const productsResponse = await axios.get('http://localhost:8080/getAllProducts');
            const usersResponse = await axios.get('http://localhost:8080/getAllUsers');

            setStats({
                totalProducts: productsResponse.data.length || 0,
                totalUsers: usersResponse.data.length || 0,
                recentOrders: 25, // Mock data
                revenue: 15420 // Mock data
            });
        } catch (error) {
            console.error('Error fetching dashboard stats:', error);
        }
    };

    return (
        <div className="admin-dashboard-container">
            <Navbar />
            <div className="admin-dashboard">
                {/* Header Section */}
                <div className="dashboard-header">
                    <div className="header-content">
                        <div className="header-text">
                            <h1>Admin Dashboard</h1>
                            <p>Welcome back! Here's what's happening with your store today.</p>
                        </div>
                        <div className="header-actions">
                            <button
                                className="primary-btn"
                                onClick={() => navigate("/AddProducts")}
                            >
                                <span className="btn-icon">➕</span>
                                Add Product
                            </button>
                        </div>
                    </div>
                </div>

                {/* Stats Cards */}
                <div className="stats-section">
                    <div className="stats-grid">
                        <div className="stat-card">
                            <div className="stat-icon products">📦</div>
                            <div className="stat-content">
                                <h3>{stats.totalProducts}</h3>
                                <p>Total Products</p>
                            </div>
                        </div>
                        <div className="stat-card">
                            <div className="stat-icon users">👥</div>
                            <div className="stat-content">
                                <h3>{stats.totalUsers}</h3>
                                <p>Total Users</p>
                            </div>
                        </div>
                        <div className="stat-card">
                            <div className="stat-icon orders">📋</div>
                            <div className="stat-content">
                                <h3>{stats.recentOrders}</h3>
                                <p>Recent Orders</p>
                            </div>
                        </div>
                        <div className="stat-card">
                            <div className="stat-icon revenue">💰</div>
                            <div className="stat-content">
                                <h3>₹{stats.revenue.toLocaleString()}</h3>
                                <p>Total Revenue</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Main Content Grid */}
                <div className="main-content">
                    {/* Management Cards */}
                    <div className="management-section">
                        <h2>Management</h2>
                        <div className="management-grid">
                            <div className="management-card" onClick={() => navigate("/AllProducts")}>
                                <div className="card-header">
                                    <div className="card-icon-large">📦</div>
                                    <div className="card-badge">{stats.totalProducts}</div>
                                </div>
                                <div className="card-content">
                                    <h3>Product Management</h3>
                                    <p>View, edit, and manage your product inventory</p>
                                    <ul className="card-features">
                                        <li>• View all products</li>
                                        <li>• Edit product details</li>
                                        <li>• Delete products</li>
                                    </ul>
                                </div>
                                <div className="card-footer">
                                    <span className="card-action">Manage Products →</span>
                                </div>
                            </div>

                            <div className="management-card" onClick={() => navigate("/AddProducts")}>
                                <div className="card-header">
                                    <div className="card-icon-large">➕</div>
                                    <div className="card-badge">New</div>
                                </div>
                                <div className="card-content">
                                    <h3>Add New Product</h3>
                                    <p>Add new products to your store inventory</p>
                                    <ul className="card-features">
                                        <li>• Product details</li>
                                        <li>• Upload images</li>
                                        <li>• Set pricing</li>
                                    </ul>
                                </div>
                                <div className="card-footer">
                                    <span className="card-action">Add Product →</span>
                                </div>
                            </div>

                            <div className="management-card coming-soon">
                                <div className="card-header">
                                    <div className="card-icon-large">📊</div>
                                    <div className="card-badge">Soon</div>
                                </div>
                                <div className="card-content">
                                    <h3>Analytics & Reports</h3>
                                    <p>View detailed analytics and generate reports</p>
                                    <ul className="card-features">
                                        <li>• Sales analytics</li>
                                        <li>• User behavior</li>
                                        <li>• Revenue reports</li>
                                    </ul>
                                </div>
                                <div className="card-footer">
                                    <span className="card-action">Coming Soon</span>
                                </div>
                            </div>

                            <div className="management-card coming-soon">
                                <div className="card-header">
                                    <div className="card-icon-large">⚙️</div>
                                    <div className="card-badge">Soon</div>
                                </div>
                                <div className="card-content">
                                    <h3>Store Settings</h3>
                                    <p>Configure your store settings and preferences</p>
                                    <ul className="card-features">
                                        <li>• Store information</li>
                                        <li>• Payment settings</li>
                                        <li>• User management</li>
                                    </ul>
                                </div>
                                <div className="card-footer">
                                    <span className="card-action">Coming Soon</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Quick Actions Sidebar */}
                    <div className="sidebar-section">
                        <div className="quick-actions-card">
                            <h3>Quick Actions</h3>
                            <div className="quick-actions-list">
                                <button
                                    className="quick-action-btn"
                                    onClick={() => navigate("/AddProducts")}
                                >
                                    <span className="action-icon">➕</span>
                                    <span>Add Product</span>
                                </button>
                                <button
                                    className="quick-action-btn"
                                    onClick={() => navigate("/AllProducts")}
                                >
                                    <span className="action-icon">📦</span>
                                    <span>View Products</span>
                                </button>
                                <button
                                    className="quick-action-btn disabled"
                                    disabled
                                >
                                    <span className="action-icon">📊</span>
                                    <span>View Reports</span>
                                </button>
                                <button
                                    className="quick-action-btn disabled"
                                    disabled
                                >
                                    <span className="action-icon">👥</span>
                                    <span>Manage Users</span>
                                </button>
                            </div>
                        </div>

                        <div className="recent-activity-card">
                            <h3>Recent Activity</h3>
                            <div className="activity-list">
                                <div className="activity-item">
                                    <div className="activity-icon">📦</div>
                                    <div className="activity-content">
                                        <p>Product added</p>
                                        <span>2 hours ago</span>
                                    </div>
                                </div>
                                <div className="activity-item">
                                    <div className="activity-icon">👤</div>
                                    <div className="activity-content">
                                        <p>New user registered</p>
                                        <span>5 hours ago</span>
                                    </div>
                                </div>
                                <div className="activity-item">
                                    <div className="activity-icon">🛒</div>
                                    <div className="activity-content">
                                        <p>Order completed</p>
                                        <span>1 day ago</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
