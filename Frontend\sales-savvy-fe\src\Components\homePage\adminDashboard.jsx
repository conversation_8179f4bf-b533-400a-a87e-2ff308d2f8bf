import React from 'react';
import { useNavigate } from 'react-router-dom';
import AddProducts from '../AdminPages/AddProducts';
import AllProducts from '../AdminPages/AllProducts';
import Navbar from '../Navbar/Navbar';
export default function AdminDashboard() {
    const navigate = useNavigate();
    return (
        <>
        <Navbar />
        <div>
            <h3>
                You are at Admin Dashboard
            </h3>
            <div>
                <button onClick={()=>{navigate("/AddProducts")}}>
                    Add Product
                </button>
                <br/>
               <AllProducts />
            </div>
        </div>
        </>
    )
}   