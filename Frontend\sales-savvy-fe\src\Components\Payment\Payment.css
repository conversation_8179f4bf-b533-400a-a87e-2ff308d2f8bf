/* Professional Payment Component */
.payment-component {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    max-width: 400px;
    margin: 0 auto;
}

.payment-summary {
    text-align: center;
    margin-bottom: var(--spacing-8);
    padding-bottom: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.payment-summary h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--spacing-4);
}

.amount-display {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-1);
}

.currency {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-600);
}

.amount {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.payment-btn {
    width: 100%;
    margin-bottom: var(--spacing-6);
    position: relative;
    overflow: hidden;
}

.payment-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.payment-btn:disabled:hover {
    transform: none;
    box-shadow: var(--shadow-md);
}

.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s ease-in-out infinite;
    margin-right: var(--spacing-2);
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.payment-info {
    text-align: center;
}

.secure-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
    font-weight: 500;
}

.accepted-methods {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    line-height: 1.4;
}

.payment-methods {
    display: block;
    margin-top: var(--spacing-1);
    font-weight: 500;
}

/* Payment Modal Overlay */
.payment-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.payment-modal {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-10);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-2xl);
    animation: fadeIn 0.3s ease-out;
}

.payment-modal h2 {
    text-align: center;
    margin-bottom: var(--spacing-8);
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
}

/* Payment Success/Error States */
.payment-status {
    text-align: center;
    padding: var(--spacing-8);
}

.payment-status.success {
    color: var(--success-color);
}

.payment-status.error {
    color: var(--warning-color);
}

.payment-status-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-4);
}

.payment-status h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-2);
}

.payment-status p {
    font-size: var(--font-size-base);
    color: var(--gray-600);
    margin-bottom: var(--spacing-6);
}

/* Responsive Design */
@media (max-width: 480px) {
    .payment-component {
        padding: var(--spacing-6);
        margin: 0 var(--spacing-4);
    }
    
    .amount {
        font-size: var(--font-size-3xl);
    }
    
    .currency {
        font-size: var(--font-size-xl);
    }
    
    .payment-modal {
        padding: var(--spacing-6);
        margin: var(--spacing-4);
    }
}
