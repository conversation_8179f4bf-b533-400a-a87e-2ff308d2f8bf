import React, { useState } from 'react';
import axios from 'axios';
import './signUp.css';
import { useNavigate } from 'react-router-dom';
export default function SignUp() {
const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    dob: '',
    gender: '',
    role: ''
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    axios.post('http://localhost:8080/signUp', formData)
      .then(response => {
        alert('Signup successful!');
      })
      .catch(error => {
        console.error('Signup error:', error);
        alert('Signup failed. See console for details.');
      });
  };

  return (
    <div className="signup-container">
      <div className="signup-card">
        <div className="signup-header">
          <div className="brand-logo">
            <span className="logo-icon">🛒</span>
            <span className="logo-text">Sales Savvy</span>
          </div>
          <h2>Create Your Account</h2>
          <p>Join thousands of businesses already using Sales Savvy</p>
        </div>

        <form className="signup-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label">Username</label>
            <input
              type="text"
              name="username"
              className="form-input"
              placeholder="Choose a username"
              required
              onChange={handleChange}
            />
          </div>

          <div className="form-group">
            <label className="form-label">Email Address</label>
            <input
              type="email"
              name="email"
              className="form-input"
              placeholder="Enter your email"
              required
              onChange={handleChange}
            />
          </div>

          <div className="form-group">
            <label className="form-label">Password</label>
            <input
              type="password"
              name="password"
              className="form-input"
              placeholder="Create a strong password"
              required
              onChange={handleChange}
            />
          </div>

          <div className="form-group">
            <label className="form-label">Date of Birth</label>
            <input
              type="date"
              name="dob"
              className="form-input"
              required
              onChange={handleChange}
            />
          </div>

          <div className="form-group">
            <label className="form-label">Gender</label>
            <div className="radio-group">
              <label className="radio-option">
                <input type="radio" name="gender" value="Male" required onChange={handleChange} />
                <span className="radio-label">Male</span>
              </label>
              <label className="radio-option">
                <input type="radio" name="gender" value="Female" onChange={handleChange} />
                <span className="radio-label">Female</span>
              </label>
              <label className="radio-option">
                <input type="radio" name="gender" value="Other" onChange={handleChange} />
                <span className="radio-label">Other</span>
              </label>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Account Type</label>
            <select name="role" className="form-input" required onChange={handleChange}>
              <option value="">Select your role</option>
              <option value="User">👨‍💼 Customer</option>
              <option value="Admin">🛍️ Admin</option>
            </select>
          </div>

          <button type="submit" className="btn btn-primary btn-lg signup-btn">
            <span>✨</span>
            Create Account
          </button>

          <div className="signup-footer">
            <p>Already have an account?
              <button
                type="button"
                className="link-btn"
                onClick={() => navigate('/login')}
              >
                Sign in here
              </button>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
}
