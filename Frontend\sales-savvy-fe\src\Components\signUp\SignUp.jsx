import React, { useState } from 'react';
import axios from 'axios';
import './signUp.css';
import { useNavigate } from 'react-router-dom';
export default function SignUp() {
const navigate = useNavigate();
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    dob: '',
    gender: '',
    role: ''
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    axios.post('http://localhost:8080/signUp', formData)
      .then(response => {
        alert('Signup successful!');
      })
      .catch(error => {
        console.error('Signup error:', error);
        alert('Signup failed. See console for details.');
      });
  };

  return (
    <div className="awesome-signup-wrapper">
      <form className="awesome-signup-form" onSubmit={handleSubmit}>
        <h2>Create Account</h2>

        <input type="text" name="username" placeholder="Username" required onChange={handleChange} />
        <input type="email" name="email" placeholder="Email" required onChange={handleChange} />
        <input type="password" name="password" placeholder="Password" required onChange={handleChange} />
        <label>Date of Birth</label>
        <input type="date" name="dob" required onChange={handleChange} />

        <div className="radio-group">
          <label>Gender</label>
          <div className="radio-options">
            <label><input type="radio" name="gender" value="Male" onChange={handleChange} required /> Male</label>
            <label><input type="radio" name="gender" value="Female" onChange={handleChange} /> Female</label>
            <label><input type="radio" name="gender" value="Other" onChange={handleChange} /> Other</label>
          </div>
        </div>

        <label>Role</label>
        <select name="role" required onChange={handleChange}>
          <option value="">Select Role</option>
          <option value="User">User</option>
          <option value="Admin">Admin</option>
        </select>

        <button type="submit">Sign Up</button>
        
        <button onClick={() => navigate("/login")}>Login</button>
      </form>
    </div>
  );
}
