import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';

export default function DeleteProduct() {
    const { id } = useParams(); // get id from URL
    const navigate = useNavigate();

    useEffect(() => {
        const confirmDelete = window.confirm("Are you sure you want to delete this product?");
        if (!confirmDelete) {
            navigate('/all_products'); // Cancel -> go back
            return;
        }

        axios
            .delete(`http://localhost:8080/deleteProduct`, { params: { id } })
            .then(() => {
                alert('Product deleted successfully!');
                navigate('/all_products');
            })
            .catch(err => {
                console.error('Delete failed:', err);
                alert('Failed to delete product');
                navigate('/all_products');
            });
    }, [id, navigate]);

    return <div>Deleting product...</div>;
}
