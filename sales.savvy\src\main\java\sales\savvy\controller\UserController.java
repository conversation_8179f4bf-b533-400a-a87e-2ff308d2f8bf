package sales.savvy.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import sales.savvy.entity.User;
import sales.savvy.service.UserService;

import org.springframework.web.bind.annotation.*;

@CrossOrigin("*")
@RestController
public class UserController {
    @Autowired
    UserService service;

    @PostMapping("/signUp")
    public String signUp(@RequestBody User user) {
        String username = user.getUsername();
        User existingUser = service.getUser(username);
        if (existingUser != null) {
            return "User already exists";
        }
        else {

            service.addUser(user);
            return "User registered successfully";
        }
    }
    
    @PostMapping("/login")
    public String login(@RequestBody User user) {
        String username = user.getUsername();
        String password = user.getPassword();
        System.out.println(username + " " + password);
        User existingUser = service.CkeckUser(username, password);
        if (existingUser != null) {
            if(existingUser.getRole().equals("Admin")){
                System.out.println(existingUser.getRole());
                return "Admin";
            } else if(existingUser.getRole().equals("User")){
                return "User";
            }

                return "Enjoy browsing";
        }
        else {
            return "Invalid credentials";
        }
    }
    
     @GetMapping("/getAllUsers")
    public List<User> getAllUsers() {
        return service.getAllUsers();
    }

 @GetMapping("/test")
public String test(String username) {
    System.out.println(username);
    return "Requested using axios. Param was: "+ username;
}

    

}