{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\Navbar\\\\Navbar.jsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport './Navbar.css'; // Optional: For styling\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Navbar() {\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-logo\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        children: \"\\uD83D\\uDED2 MyShop\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"navbar-links\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/all_products\",\n          children: \"All Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/add_product\",\n          children: \"Add Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/update_product\",\n          children: \"Update Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/delete_product\",\n          children: \"Delete Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 9\n  }, this);\n}\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/Navbar/Navbar.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport './Navbar.css'; // Optional: For styling\r\n\r\nexport default function Navbar() {\r\n    return (\r\n        <nav className=\"navbar\">\r\n            <div className=\"navbar-logo\">\r\n                <Link to=\"/\">🛒 MyShop</Link>\r\n            </div>\r\n            <ul className=\"navbar-links\">\r\n                <li><Link to=\"/all_products\">All Products</Link></li>\r\n                <li><Link to=\"/add_product\">Add Product</Link></li>\r\n                <li><Link to=\"/update_product\">Update Product</Link></li>\r\n                <li><Link to=\"/delete_product\">Delete Product</Link></li>\r\n            </ul>\r\n        </nav>\r\n    );\r\n}\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAO,cAAc,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEvB,eAAe,SAASC,MAAMA,CAAA,EAAG;EAC7B,oBACID,OAAA;IAAKE,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACnBH,OAAA;MAAKE,SAAS,EAAC,aAAa;MAAAC,QAAA,eACxBH,OAAA,CAACF,IAAI;QAACM,EAAE,EAAC,GAAG;QAAAD,QAAA,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eACNR,OAAA;MAAIE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACxBH,OAAA;QAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;UAACM,EAAE,EAAC,eAAe;UAAAD,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrDR,OAAA;QAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;UAACM,EAAE,EAAC,cAAc;UAAAD,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnDR,OAAA;QAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;UAACM,EAAE,EAAC,iBAAiB;UAAAD,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzDR,OAAA;QAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;UAACM,EAAE,EAAC,iBAAiB;UAAAD,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEd;AAACC,EAAA,GAduBR,MAAM;AAAA,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}