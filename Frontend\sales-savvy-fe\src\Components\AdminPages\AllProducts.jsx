import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import Navbar from '../Navbar/Navbar';
import './AllProducts.css';

export default function AllProducts() {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const navigate = useNavigate();

    const fetchProducts = () => {
        setLoading(true);
        axios.get('http://localhost:8080/getAllProducts')
            .then(response => {
                setProducts(response.data);
                setLoading(false);
            })
            .catch(error => {
                console.error('Error fetching products:', error);
                setLoading(false);
            });
    };

    useEffect(() => {
        fetchProducts();
    }, []);

    const handleDelete = (id) => {
        const confirmed = window.confirm("Are you sure you want to delete this product?");
        if (!confirmed) return;

        axios
            .delete('http://localhost:8080/deleteProduct', { params: { id } })
            .then(() => {
                fetchProducts();
                alert('Product deleted successfully!');
            })
            .catch(err => {
                console.error('Delete failed:', err);
                alert('Failed to delete product. Please try again.');
            });
    };

    const handleUpdate = (product) => {
        navigate('/updateproduct', { state: { product } });
    };

    if (loading) {
        return (
            <>
                <Navbar />
                <div className="loading-container">
                    <div className="loading-spinner"></div>
                    <p>Loading products...</p>
                </div>
            </>
        );
    }

    return (
        <>
            <Navbar />
            <div className="all-products-container">
                <div className="page-header">
                    <h1>All Products</h1>
                    <p>Manage your product inventory</p>
                </div>

                {products.length === 0 ? (
                    <div className="no-products">
                        <h3>No products found</h3>
                        <p>Start by adding some products to your inventory.</p>
                        <button
                            className="add-product-btn"
                            onClick={() => navigate('/AddProducts')}
                        >
                            Add Your First Product
                        </button>
                    </div>
                ) : (
                    <div className="products-table-container">
                        <div className="table-header">
                            <h3>Products ({products.length})</h3>
                            <button
                                className="add-product-btn"
                                onClick={() => navigate('/AddProducts')}
                            >
                                + Add New Product
                            </button>
                        </div>

                        <div className="table-wrapper">
                            <table className="products-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Image</th>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Price</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {products.map(product => (
                                        <tr key={product.id}>
                                            <td className="product-id">#{product.id}</td>
                                            <td className="product-image">
                                                <img
                                                    src={product.image}
                                                    alt={product.name}
                                                    onError={(e) => {
                                                        e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';
                                                    }}
                                                />
                                            </td>
                                            <td className="product-name">
                                                <strong>{product.name}</strong>
                                            </td>
                                            <td className="product-description">
                                                {product.description}
                                            </td>
                                            <td className="product-price">
                                                ₹{product.price}
                                            </td>
                                            <td className="product-actions">
                                                <button
                                                    className="update-btn"
                                                    onClick={() => handleUpdate(product)}
                                                    title="Update Product"
                                                >
                                                    ✏️ Update
                                                </button>
                                                <button
                                                    className="delete-btn"
                                                    onClick={() => handleDelete(product.id)}
                                                    title="Delete Product"
                                                >
                                                    🗑️ Delete
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}
