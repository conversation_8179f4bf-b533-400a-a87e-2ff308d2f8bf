// import React, { useState, useEffect } from 'react';
// import { useNavigate, useLocation } from 'react-router-dom';
// import axios from 'axios';
// import './CustomerNavbar.css';

// export default function CustomerNavbar() {
//     const [isMenuOpen, setIsMenuOpen] = useState(false);
//     const [cartCount, setCartCount] = useState(0);
//     const navigate = useNavigate();
//     const location = useLocation();

//     useEffect(() => {
//         // Update cart count when component mounts and when localStorage changes
//         const updateCartCount = async () => {
//             const username = localStorage.getItem('user');
//             if (!username) {
//                 setCartCount(0);
//                 return;
//             }

//             try {
//                 const response = await axios.get(`http://localhost:8080/getCartCount/${username}`);
//                 setCartCount(response.data || 0);
//             } catch (error) {
//                 console.error('Error fetching cart count:', error);
//                 setCartCount(0);
//             }
//         };

//         updateCartCount();

//         // Custom event for same-page cart updates
//         window.addEventListener('cartUpdated', updateCartCount);

//         return () => {
//             window.removeEventListener('cartUpdated', updateCartCount);
//         };
//     }, []);

//     const handleLogout = () => {
//         const confirmed = window.confirm("Are you sure you want to logout?");
//         if (confirmed) {
//             localStorage.removeItem('user');
//             localStorage.removeItem('cart');
//             sessionStorage.clear();
//             navigate('/');
//         }
//     };

//     const toggleMenu = () => {
//         setIsMenuOpen(!isMenuOpen);
//     };

//     const isActive = (path) => {
//         return location.pathname === path;
//     };

//     return (
//         <nav className="customer-navbar">
//             <div className="navbar-container">
//                 <div className="navbar-logo">
//                     <span>🛒 <span className="logo-text">Sales Savvy</span></span>
//                     <span className="user-badge">{localStorage.getItem('user')}</span>
//                 </div>

//                 <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>
//                     <ul className="navbar-links">
//                         <li>
//                             <span
//                                 className={`nav-item ${isActive('/CustomerDashboard') ? 'active' : ''}`}
//                                 onClick={() => {
//                                     navigate('/CustomerDashboard');
//                                     setIsMenuOpen(false);
//                                 }}
//                             >
//                                 🏠 Home
//                             </span>
//                         </li>
//                         <li>
//                             <span
//                                 className={`nav-item cart-item ${isActive('/cart') ? 'active' : ''}`}
//                                 onClick={() => {
//                                     navigate('/cart');
//                                     setIsMenuOpen(false);
//                                 }}
//                             >
//                                 🛒 Cart ({cartCount})
//                             </span>
//                         </li>
//                         <li>
//                             <span className="nav-item">
//                                 📦 My Orders
//                             </span>
//                         </li>
//                         <li>
//                             <button
//                                 className="logout-btn"
//                                 onClick={handleLogout}
//                             >
//                                 🚪 Logout
//                             </button>
//                         </li>
//                     </ul>
//                 </div>

//                 <div className="navbar-toggle" onClick={toggleMenu}>
//                     <span></span>
//                     <span></span>
//                     <span></span>
//                 </div>
//             </div>
//         </nav>
//     );
// }
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { FiShoppingCart, FiHome, FiPackage, FiLogOut, FiMenu, FiUser, FiChevronDown } from 'react-icons/fi';
import './CustomerNavbar.css';

export default function CustomerNavbar() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
    const [cartCount, setCartCount] = useState(0);
    const [username, setUsername] = useState('');
    const navigate = useNavigate();
    const location = useLocation();

    useEffect(() => {
        const storedUsername = localStorage.getItem('user');
        setUsername(storedUsername || '');

        const updateCartCount = async () => {
            if (!storedUsername) return setCartCount(0);
            
            try {
                const response = await axios.get(`http://localhost:8080/getCartCount/${storedUsername}`);
                setCartCount(response.data || 0);
            } catch (error) {
                console.error('Error fetching cart count:', error);
                setCartCount(0);
            }
        };

        updateCartCount();
        window.addEventListener('cartUpdated', updateCartCount);
        return () => window.removeEventListener('cartUpdated', updateCartCount);
    }, []);

    const handleLogout = () => {
        if (window.confirm("Are you sure you want to logout?")) {
            localStorage.removeItem('user');
            localStorage.removeItem('cart');
            navigate('/');
        }
    };

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
        document.body.style.overflow = isMenuOpen ? 'auto' : 'hidden';
    };

    const toggleUserMenu = () => {
        setIsUserMenuOpen(!isUserMenuOpen);
    };

    const navigateTo = (path) => {
        navigate(path);
        setIsMenuOpen(false);
        setIsUserMenuOpen(false);
        document.body.style.overflow = 'auto';
    };

    const isActive = (path) => location.pathname === path;

    return (
        <nav className="customer-navbar">
            <div className="navbar-container">
                <div className="navbar-brand" onClick={() => navigateTo('/CustomerDashboard')}>
                    <FiShoppingCart className="brand-icon" />
                    <span className="brand-text">Sales Savvy</span>
                </div>

                <div className={`navbar-links ${isMenuOpen ? 'active' : ''}`}>
                    <button 
                        className={`nav-link ${isActive('/CustomerDashboard') ? 'active' : ''}`}
                        onClick={() => navigateTo('/CustomerDashboard')}
                    >
                        <FiHome className="link-icon" />
                        <span>Home</span>
                    </button>

                    <button 
                        className={`nav-link cart-link ${isActive('/cart') ? 'active' : ''}`}
                        onClick={() => navigateTo('/cart')}
                    >
                        <FiShoppingCart className="link-icon" />
                        <span>Cart ({cartCount})</span>
                    </button>

                    <button 
                        className={`nav-link ${isActive('/orders') ? 'active' : ''}`}
                        onClick={() => navigateTo('/orders')}
                    >
                        <FiPackage className="link-icon" />
                        <span>My Orders</span>
                    </button>
                </div>

                <div className="user-menu-container">
                    <div className="user-badge" onClick={toggleUserMenu}>
                        <FiUser className="user-icon" />
                        <span className="username">{username}</span>
                        <FiChevronDown className={`dropdown-icon ${isUserMenuOpen ? 'open' : ''}`} />
                    </div>
                    
                    {isUserMenuOpen && (
                        <div className="user-dropdown">
                            <button className="dropdown-item" onClick={handleLogout}>
                                <FiLogOut className="dropdown-icon" />
                                <span>Logout</span>
                            </button>
                        </div>
                    )}
                </div>

                <button className="menu-toggle" onClick={toggleMenu}>
                    <FiMenu />
                </button>
            </div>
            {(isMenuOpen || isUserMenuOpen) && (
                <div 
                    className="navbar-overlay" 
                    onClick={() => {
                        setIsMenuOpen(false);
                        setIsUserMenuOpen(false);
                    }} 
                />
            )}
        </nav>
    );
}