import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './CustomerNavbar.css';

export default function CustomerNavbar() {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [cartCount, setCartCount] = useState(0);
    const navigate = useNavigate();

    useEffect(() => {
        // Update cart count when component mounts and when localStorage changes
        const updateCartCount = () => {
            const cart = localStorage.getItem('cart');
            if (cart) {
                const cartItems = JSON.parse(cart);
                const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
                setCartCount(totalItems);
            } else {
                setCartCount(0);
            }
        };

        updateCartCount();

        // Listen for storage changes (when cart is updated from other components)
        window.addEventListener('storage', updateCartCount);

        // Custom event for same-page cart updates
        window.addEventListener('cartUpdated', updateCartCount);

        return () => {
            window.removeEventListener('storage', updateCartCount);
            window.removeEventListener('cartUpdated', updateCartCount);
        };
    }, []);

    const handleLogout = () => {
        const confirmed = window.confirm("Are you sure you want to logout?");
        if (confirmed) {
            localStorage.removeItem('user');
            localStorage.removeItem('cart');
            sessionStorage.clear();
            navigate('/');
        }
    };

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    return (
        <nav className="customer-navbar">
            <div className="navbar-logo">
                <span>🛒 <span className="logo-text">Sales Savvy</span></span>
                <span className="user-badge">Customer</span>
            </div>
            
            <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>
                <ul className="navbar-links">
                    <li>
                        <span
                            className="nav-item"
                            onClick={() => navigate('/CustomerDashboard')}
                        >
                            🏠 Home
                        </span>
                    </li>
                    <li>
                        <span
                            className="nav-item cart-item"
                            onClick={() => navigate('/cart')}
                        >
                            🛒 Cart ({cartCount})
                        </span>
                    </li>
                    <li>
                        <span className="nav-item">
                            📦 My Orders
                        </span>
                    </li>
                    <li>
                        <button 
                            className="logout-btn"
                            onClick={handleLogout}
                        >
                            🚪 Logout
                        </button>
                    </li>
                </ul>
            </div>

            <div className="navbar-toggle" onClick={toggleMenu}>
                <span></span>
                <span></span>
                <span></span>
            </div>
        </nav>
    );
}
