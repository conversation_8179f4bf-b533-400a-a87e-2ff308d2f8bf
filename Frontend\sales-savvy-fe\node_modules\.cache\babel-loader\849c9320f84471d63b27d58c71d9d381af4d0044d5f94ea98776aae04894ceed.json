{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\AdminPages\\\\UpdateProduct.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function UpdateProduct() {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    product\n  } = location.state || {}; // fallback if state is undefined\n\n  const [updatedProduct, setUpdatedProduct] = useState({\n    id: '',\n    name: '',\n    description: '',\n    price: '',\n    image: ''\n  });\n  useEffect(() => {\n    if (product) {\n      setUpdatedProduct(product);\n    }\n  }, [product]);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setUpdatedProduct(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    axios.put('http://localhost:8080/updateProduct', updatedProduct).then(() => {\n      alert('Product updated successfully!');\n      navigate('/AdminDashboard'); // or whatever your list page route is\n    }).catch(error => {\n      console.error('Error updating product:', error);\n      alert('Failed to update product');\n    });\n  };\n  if (!product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"No product selected for update.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Update Product\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"ID (read-only):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"id\",\n          value: updatedProduct.id,\n          readOnly: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Name:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"name\",\n          value: updatedProduct.name,\n          onChange: handleChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Description:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"description\",\n          value: updatedProduct.description,\n          onChange: handleChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Price (\\u20B9):\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          name: \"price\",\n          value: updatedProduct.price,\n          onChange: handleChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Image URL:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          name: \"image\",\n          value: updatedProduct.image,\n          onChange: handleChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Update\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"button\",\n        onClick: () => navigate('/all_products'),\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 9\n  }, this);\n}\n_s(UpdateProduct, \"I885L+azMx+rfsXrTHmlqa6B0iY=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = UpdateProduct;\nvar _c;\n$RefreshReg$(_c, \"UpdateProduct\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "useLocation", "useNavigate", "jsxDEV", "_jsxDEV", "UpdateProduct", "_s", "location", "navigate", "product", "state", "updatedProduct", "setUpdatedProduct", "id", "name", "description", "price", "image", "handleChange", "e", "value", "target", "prev", "handleSubmit", "preventDefault", "put", "then", "alert", "catch", "error", "console", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "readOnly", "onChange", "required", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/AdminPages/UpdateProduct.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\n\r\nexport default function UpdateProduct() {\r\n    const location = useLocation();\r\n    const navigate = useNavigate();\r\n    const { product } = location.state || {}; // fallback if state is undefined\r\n\r\n    const [updatedProduct, setUpdatedProduct] = useState({\r\n        id: '',\r\n        name: '',\r\n        description: '',\r\n        price: '',\r\n        image: ''\r\n    });\r\n\r\n    useEffect(() => {\r\n        if (product) {\r\n            setUpdatedProduct(product);\r\n        }\r\n    }, [product]);\r\n\r\n    const handleChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setUpdatedProduct(prev => ({\r\n            ...prev,\r\n            [name]: value\r\n        }));\r\n    };\r\n\r\n    const handleSubmit = (e) => {\r\n        e.preventDefault();\r\n\r\n        axios.put('http://localhost:8080/updateProduct', updatedProduct)\r\n            .then(() => {\r\n                alert('Product updated successfully!');\r\n                navigate('/AdminDashboard'); // or whatever your list page route is\r\n            })\r\n            .catch(error => {\r\n                console.error('Error updating product:', error);\r\n                alert('Failed to update product');\r\n            });\r\n    };\r\n\r\n    if (!product) {\r\n        return <div>No product selected for update.</div>;\r\n    }\r\n\r\n    return (\r\n        <div>\r\n            <h2>Update Product</h2>\r\n            <form onSubmit={handleSubmit}>\r\n                <div>\r\n                    <label>ID (read-only):</label>\r\n                    <input type=\"text\" name=\"id\" value={updatedProduct.id} readOnly />\r\n                </div>\r\n                <div>\r\n                    <label>Name:</label>\r\n                    <input type=\"text\" name=\"name\" value={updatedProduct.name} onChange={handleChange} required />\r\n                </div>\r\n                <div>\r\n                    <label>Description:</label>\r\n                    <input type=\"text\" name=\"description\" value={updatedProduct.description} onChange={handleChange} required />\r\n                </div>\r\n                <div>\r\n                    <label>Price (₹):</label>\r\n                    <input type=\"number\" name=\"price\" value={updatedProduct.price} onChange={handleChange} required />\r\n                </div>\r\n                <div>\r\n                    <label>Image URL:</label>\r\n                    <input type=\"text\" name=\"image\" value={updatedProduct.image} onChange={handleChange} required />\r\n                </div>\r\n                <br />\r\n                <button type=\"submit\">Update</button>\r\n                <button type=\"button\" onClick={() => navigate('/all_products')}>Cancel</button>\r\n            </form>\r\n        </div>\r\n    );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,eAAe,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEO;EAAQ,CAAC,GAAGF,QAAQ,CAACG,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC;;EAE1C,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC;IACjDe,EAAE,EAAE,EAAE;IACNC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE;EACX,CAAC,CAAC;EAEFlB,SAAS,CAAC,MAAM;IACZ,IAAIU,OAAO,EAAE;MACTG,iBAAiB,CAACH,OAAO,CAAC;IAC9B;EACJ,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAEb,MAAMS,YAAY,GAAIC,CAAC,IAAK;IACxB,MAAM;MAAEL,IAAI;MAAEM;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCT,iBAAiB,CAACU,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACR,IAAI,GAAGM;IACZ,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMG,YAAY,GAAIJ,CAAC,IAAK;IACxBA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElBxB,KAAK,CAACyB,GAAG,CAAC,qCAAqC,EAAEd,cAAc,CAAC,CAC3De,IAAI,CAAC,MAAM;MACRC,KAAK,CAAC,+BAA+B,CAAC;MACtCnB,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC;IACjC,CAAC,CAAC,CACDoB,KAAK,CAACC,KAAK,IAAI;MACZC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CF,KAAK,CAAC,0BAA0B,CAAC;IACrC,CAAC,CAAC;EACV,CAAC;EAED,IAAI,CAAClB,OAAO,EAAE;IACV,oBAAOL,OAAA;MAAA2B,QAAA,EAAK;IAA+B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACrD;EAEA,oBACI/B,OAAA;IAAA2B,QAAA,gBACI3B,OAAA;MAAA2B,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACvB/B,OAAA;MAAMgC,QAAQ,EAAEb,YAAa;MAAAQ,QAAA,gBACzB3B,OAAA;QAAA2B,QAAA,gBACI3B,OAAA;UAAA2B,QAAA,EAAO;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9B/B,OAAA;UAAOiC,IAAI,EAAC,MAAM;UAACvB,IAAI,EAAC,IAAI;UAACM,KAAK,EAAET,cAAc,CAACE,EAAG;UAACyB,QAAQ;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eACN/B,OAAA;QAAA2B,QAAA,gBACI3B,OAAA;UAAA2B,QAAA,EAAO;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpB/B,OAAA;UAAOiC,IAAI,EAAC,MAAM;UAACvB,IAAI,EAAC,MAAM;UAACM,KAAK,EAAET,cAAc,CAACG,IAAK;UAACyB,QAAQ,EAAErB,YAAa;UAACsB,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eACN/B,OAAA;QAAA2B,QAAA,gBACI3B,OAAA;UAAA2B,QAAA,EAAO;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3B/B,OAAA;UAAOiC,IAAI,EAAC,MAAM;UAACvB,IAAI,EAAC,aAAa;UAACM,KAAK,EAAET,cAAc,CAACI,WAAY;UAACwB,QAAQ,EAAErB,YAAa;UAACsB,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3G,CAAC,eACN/B,OAAA;QAAA2B,QAAA,gBACI3B,OAAA;UAAA2B,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzB/B,OAAA;UAAOiC,IAAI,EAAC,QAAQ;UAACvB,IAAI,EAAC,OAAO;UAACM,KAAK,EAAET,cAAc,CAACK,KAAM;UAACuB,QAAQ,EAAErB,YAAa;UAACsB,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjG,CAAC,eACN/B,OAAA;QAAA2B,QAAA,gBACI3B,OAAA;UAAA2B,QAAA,EAAO;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzB/B,OAAA;UAAOiC,IAAI,EAAC,MAAM;UAACvB,IAAI,EAAC,OAAO;UAACM,KAAK,EAAET,cAAc,CAACM,KAAM;UAACsB,QAAQ,EAAErB,YAAa;UAACsB,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC,eACN/B,OAAA;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN/B,OAAA;QAAQiC,IAAI,EAAC,QAAQ;QAAAN,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACrC/B,OAAA;QAAQiC,IAAI,EAAC,QAAQ;QAACI,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,eAAe,CAAE;QAAAuB,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7E,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd;AAAC7B,EAAA,CA3EuBD,aAAa;EAAA,QAChBJ,WAAW,EACXC,WAAW;AAAA;AAAAwC,EAAA,GAFRrC,aAAa;AAAA,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}