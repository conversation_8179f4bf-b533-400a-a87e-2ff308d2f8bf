/* Professional Add Products Page */
.add-products-container {
    font-family: var(--font-family);
    background: var(--gray-50);
    min-height: calc(100vh - 80px);
    padding: 0;
    margin: 0;
    width: 100%;
}

.page-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-8) 0;
    box-shadow: var(--shadow-sm);
}

.page-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-6);
}

.header-text h1 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-text p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: var(--spacing-4);
}

.main-content-area {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-12) var(--spacing-6);
}

.form-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.form-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 700px;
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.form-header {
    background: var(--gray-50);
    padding: var(--spacing-8);
    border-bottom: 1px solid var(--gray-200);
    text-align: center;
}

.form-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.form-header p {
    color: var(--gray-600);
    font-size: var(--font-size-base);
    margin: 0;
}

.product-form {
    padding: var(--spacing-8);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 5px;
}

.form-group input,
.form-group textarea {
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    margin-top: var(--spacing-8);
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.cancel-btn,
.submit-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
    min-width: 120px;
}

.cancel-btn {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.cancel-btn:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-2px);
}

.submit-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.submit-btn:active {
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .page-header-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-4);
    }

    .main-content-area {
        padding: var(--spacing-8) var(--spacing-4);
    }

    .form-card {
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 30px 0;
    }
    
    .page-header-content {
        padding: 0 20px;
    }
    
    .page-header h1 {
        font-size: 2.2rem;
    }
    
    .page-header p {
        font-size: 1rem;
    }
    
    .main-content-area {
        padding: 20px;
    }
    
    .form-container {
        padding: 30px 25px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .cancel-btn,
    .submit-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 20px 0;
    }
    
    .page-header-content {
        padding: 0 15px;
    }
    
    .page-header h1 {
        font-size: 1.8rem;
    }
    
    .page-header p {
        font-size: 0.9rem;
    }
    
    .main-content-area {
        padding: 15px;
    }
    
    .form-container {
        padding: 25px 20px;
    }
    
    .form-group input,
    .form-group textarea {
        padding: 12px 15px;
        font-size: 0.95rem;
    }
    
    .cancel-btn,
    .submit-btn {
        padding: 12px 25px;
        font-size: 0.95rem;
    }
}
