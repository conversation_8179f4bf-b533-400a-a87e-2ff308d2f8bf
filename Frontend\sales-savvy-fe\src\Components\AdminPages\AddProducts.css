/* Full Screen Add Products Styles */
.add-products-container {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: calc(100vh - 80px);
    padding: 0;
    margin: 0;
    width: 100%;
}

.page-header {
    background: white;
    padding: 40px 0;
    text-align: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0;
}

.page-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

.page-header h1 {
    color: #2c3e50;
    font-size: 3rem;
    margin-bottom: 15px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-header p {
    color: #6c757d;
    font-size: 1.2rem;
    margin: 0;
    font-weight: 400;
}

.main-content-area {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px;
    display: flex;
    justify-content: center;
}

.form-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f3f4;
    width: 100%;
    max-width: 600px;
}

.product-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 5px;
}

.form-group input,
.form-group textarea {
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

.cancel-btn,
.submit-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
    min-width: 120px;
}

.cancel-btn {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.cancel-btn:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-2px);
}

.submit-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.submit-btn:active {
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content-area {
        padding: 30px;
    }
    
    .page-header-content {
        padding: 0 30px;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 30px 0;
    }
    
    .page-header-content {
        padding: 0 20px;
    }
    
    .page-header h1 {
        font-size: 2.2rem;
    }
    
    .page-header p {
        font-size: 1rem;
    }
    
    .main-content-area {
        padding: 20px;
    }
    
    .form-container {
        padding: 30px 25px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .cancel-btn,
    .submit-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 20px 0;
    }
    
    .page-header-content {
        padding: 0 15px;
    }
    
    .page-header h1 {
        font-size: 1.8rem;
    }
    
    .page-header p {
        font-size: 0.9rem;
    }
    
    .main-content-area {
        padding: 15px;
    }
    
    .form-container {
        padding: 25px 20px;
    }
    
    .form-group input,
    .form-group textarea {
        padding: 12px 15px;
        font-size: 0.95rem;
    }
    
    .cancel-btn,
    .submit-btn {
        padding: 12px 25px;
        font-size: 0.95rem;
    }
}
