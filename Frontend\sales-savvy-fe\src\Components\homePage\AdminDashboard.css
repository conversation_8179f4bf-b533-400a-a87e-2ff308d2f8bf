/* Admin Dashboard Styles */
.admin-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
    font-family: 'Poppins', sans-serif;
    background-color: #f8f9fa;
    min-height: calc(100vh - 80px);
}

.dashboard-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px 0;
}

.dashboard-header h1 {
    color: #333;
    font-size: 2.8rem;
    margin-bottom: 15px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.dashboard-header p {
    color: #666;
    font-size: 1.2rem;
    margin: 0;
    font-weight: 400;
}

/* Dashboard Cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.dashboard-card {
    background: white;
    border-radius: 16px;
    padding: 30px 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.dashboard-card:hover::before {
    transform: scaleX(1);
}

.dashboard-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.dashboard-card h3 {
    color: #333;
    font-size: 1.4rem;
    margin-bottom: 12px;
    font-weight: 600;
}

.dashboard-card p {
    color: #666;
    font-size: 1rem;
    margin-bottom: 20px;
    line-height: 1.5;
}

.card-arrow {
    position: absolute;
    bottom: 20px;
    right: 25px;
    font-size: 1.5rem;
    color: #667eea;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
}

.dashboard-card:hover .card-arrow {
    opacity: 1;
    transform: translateX(0);
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.quick-actions h3 {
    color: #333;
    font-size: 1.6rem;
    margin-bottom: 25px;
    font-weight: 600;
    text-align: center;
}

.action-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-family: 'Poppins', sans-serif;
    min-width: 180px;
    justify-content: center;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e9ecef;
}

.action-btn.secondary:hover {
    background: #e9ecef;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-dashboard {
        padding: 20px 15px;
    }
    
    .dashboard-header h1 {
        font-size: 2.2rem;
    }
    
    .dashboard-header p {
        font-size: 1rem;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .dashboard-card {
        padding: 25px 20px;
    }
    
    .card-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }
    
    .dashboard-card h3 {
        font-size: 1.2rem;
    }
    
    .dashboard-card p {
        font-size: 0.9rem;
    }
    
    .quick-actions {
        padding: 25px 20px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 280px;
    }
}

@media (max-width: 480px) {
    .dashboard-header h1 {
        font-size: 1.8rem;
    }
    
    .dashboard-card {
        padding: 20px 15px;
    }
    
    .card-icon {
        font-size: 2rem;
    }
    
    .quick-actions h3 {
        font-size: 1.4rem;
    }
}
