/* Full Screen Admin Dashboard Styles */
.admin-dashboard-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Poppins', sans-serif;
}

.admin-dashboard {
    padding: 0;
    margin: 0;
    min-height: calc(100vh - 80px);
    background: transparent;
}

/* Header Section */
.dashboard-header {
    background: white;
    padding: 40px 60px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid #e9ecef;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header-text h1 {
    color: #2c3e50;
    font-size: 3rem;
    margin-bottom: 10px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-text p {
    color: #6c757d;
    font-size: 1.2rem;
    margin: 0;
    font-weight: 400;
}

.header-actions .primary-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.header-actions .primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.btn-icon {
    font-size: 1.2rem;
}

/* Stats Section */
.stats-section {
    padding: 40px 60px;
    background: transparent;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

.stat-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid #f1f3f4;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    font-weight: bold;
}

.stat-icon.products {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.users {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon.orders {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.revenue {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 5px 0;
}

.stat-content p {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
    font-weight: 500;
}

/* Main Content Layout */
.main-content {
    padding: 0 60px 60px;
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 40px;
    max-width: 1400px;
    margin: 0 auto;
}

.management-section h2 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 30px;
}

.management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.management-card {
    background: white;
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid #f1f3f4;
    min-height: 320px;
    display: flex;
    flex-direction: column;
}

.management-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.management-card.coming-soon {
    opacity: 0.7;
    cursor: not-allowed;
}

.management-card.coming-soon:hover {
    transform: none;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 30px 30px 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.card-icon-large {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
}

.card-badge {
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-content {
    padding: 0 30px;
    flex: 1;
}

.card-content h3 {
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.card-content p {
    color: #6c757d;
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.card-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.card-features li {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 8px;
    padding-left: 0;
}

.card-footer {
    padding: 20px 30px 30px;
    margin-top: auto;
}

.card-action {
    color: #667eea;
    font-weight: 600;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.management-card:hover .card-action {
    color: #764ba2;
}

/* Sidebar Section */
.sidebar-section {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.quick-actions-card,
.recent-activity-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #f1f3f4;
}

.quick-actions-card h3,
.recent-activity-card h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.quick-actions-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.quick-action-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 500;
    color: #495057;
}

.quick-action-btn:hover:not(.disabled) {
    background: #667eea;
    border-color: #667eea;
    color: white;
    transform: translateX(5px);
}

.quick-action-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-icon {
    font-size: 1.2rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: #e9ecef;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.activity-content p {
    color: #2c3e50;
    font-weight: 600;
    margin: 0 0 5px 0;
    font-size: 0.9rem;
}

.activity-content span {
    color: #6c757d;
    font-size: 0.8rem;
}

.dashboard-card h3 {
    color: #333;
    font-size: 1.4rem;
    margin-bottom: 12px;
    font-weight: 600;
}

.dashboard-card p {
    color: #666;
    font-size: 1rem;
    margin-bottom: 20px;
    line-height: 1.5;
}

.card-arrow {
    position: absolute;
    bottom: 20px;
    right: 25px;
    font-size: 1.5rem;
    color: #667eea;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
}

.dashboard-card:hover .card-arrow {
    opacity: 1;
    transform: translateX(0);
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
}

.quick-actions h3 {
    color: #333;
    font-size: 1.6rem;
    margin-bottom: 25px;
    font-weight: 600;
    text-align: center;
}

.action-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-family: 'Poppins', sans-serif;
    min-width: 180px;
    justify-content: center;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e9ecef;
}

.action-btn.secondary:hover {
    background: #e9ecef;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-dashboard {
        padding: 20px 15px;
    }
    
    .dashboard-header h1 {
        font-size: 2.2rem;
    }
    
    .dashboard-header p {
        font-size: 1rem;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .dashboard-card {
        padding: 25px 20px;
    }
    
    .card-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }
    
    .dashboard-card h3 {
        font-size: 1.2rem;
    }
    
    .dashboard-card p {
        font-size: 0.9rem;
    }
    
    .quick-actions {
        padding: 25px 20px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 280px;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .sidebar-section {
        flex-direction: row;
    }

    .quick-actions-card,
    .recent-activity-card {
        flex: 1;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 30px 20px;
    }

    .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .header-text h1 {
        font-size: 2.2rem;
    }

    .stats-section {
        padding: 30px 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .main-content {
        padding: 0 20px 40px;
        grid-template-columns: 1fr;
    }

    .management-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .sidebar-section {
        flex-direction: column;
        gap: 20px;
    }

    .quick-actions-card,
    .recent-activity-card {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: 20px 15px;
    }

    .header-text h1 {
        font-size: 1.8rem;
    }

    .header-text p {
        font-size: 1rem;
    }

    .stats-section {
        padding: 20px 15px;
    }

    .stat-card {
        padding: 20px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stat-content h3 {
        font-size: 2rem;
    }

    .main-content {
        padding: 0 15px 30px;
    }

    .management-card {
        min-height: auto;
    }

    .card-header,
    .card-content,
    .card-footer {
        padding-left: 20px;
        padding-right: 20px;
    }

    .card-header {
        padding-top: 20px;
        padding-bottom: 15px;
    }

    .card-footer {
        padding-bottom: 20px;
    }

    .quick-actions-card,
    .recent-activity-card {
        padding: 15px;
    }
}
