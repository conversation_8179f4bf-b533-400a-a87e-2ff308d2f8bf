{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\shared\\\\ProductsList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport './ProductsList.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function ProductsList({\n  userRole = 'customer',\n  showActions = false\n}) {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const fetchProducts = () => {\n    setLoading(true);\n    axios.get('http://localhost:8080/getAllProducts').then(response => {\n      setProducts(response.data);\n      setLoading(false);\n    }).catch(error => {\n      console.error('Error fetching products:', error);\n      setLoading(false);\n    });\n  };\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const handleDelete = id => {\n    if (userRole !== 'admin') return;\n    const confirmed = window.confirm(\"Are you sure you want to delete this product?\");\n    if (!confirmed) return;\n    axios.delete('http://localhost:8080/deleteProduct', {\n      params: {\n        id\n      }\n    }).then(() => {\n      fetchProducts();\n      alert('Product deleted successfully!');\n    }).catch(err => {\n      console.error('Delete failed:', err);\n      alert('Failed to delete product. Please try again.');\n    });\n  };\n  const handleUpdate = product => {\n    if (userRole !== 'admin') return;\n    navigate('/updateproduct', {\n      state: {\n        product\n      }\n    });\n  };\n  const handleAddToCart = product => {\n    if (userRole !== 'customer') return;\n\n    // Get existing cart from localStorage\n    const existingCart = localStorage.getItem('cart');\n    let cart = existingCart ? JSON.parse(existingCart) : [];\n\n    // Check if product already exists in cart\n    const existingItemIndex = cart.findIndex(item => item.id === product.id);\n    if (existingItemIndex > -1) {\n      // If product exists, increase quantity\n      cart[existingItemIndex].quantity += 1;\n      alert(`Increased ${product.name} quantity in cart!`);\n    } else {\n      // If product doesn't exist, add new item\n      cart.push({\n        id: product.id,\n        name: product.name,\n        description: product.description,\n        price: product.price,\n        image: product.image,\n        quantity: 1\n      });\n      alert(`Added ${product.name} to cart!`);\n    }\n\n    // Save updated cart to localStorage\n    localStorage.setItem('cart', JSON.stringify(cart));\n  };\n  const handleBuyNow = product => {\n    if (userRole !== 'customer') return;\n    // Buy now functionality for customers\n    alert(`Proceeding to buy ${product.name}!`);\n    // You can implement actual purchase functionality here\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"products-list-container\",\n    children: [userRole === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"All Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage your product inventory\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 17\n    }, this), userRole === 'customer' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header customer-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Our Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Discover amazing products at great prices\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 17\n    }, this), products.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-products\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"No products found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 21\n      }, this), userRole === 'admin' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Start by adding some products to your inventory.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"add-product-btn\",\n          onClick: () => navigate('/AddProducts'),\n          children: \"Add Your First Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Check back later for new products!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"products-container\",\n      children: [userRole === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Products (\", products.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"add-product-btn\",\n          onClick: () => navigate('/AddProducts'),\n          children: \"+ Add New Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 25\n      }, this), userRole === 'customer' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"products-count\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [products.length, \" Products Available\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 25\n      }, this), userRole === 'admin' ?\n      /*#__PURE__*/\n      // Admin Table View\n      _jsxDEV(\"div\", {\n        className: \"table-wrapper\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"products-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-id\",\n                children: [\"#\", product.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-image\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: product.image,\n                  alt: product.name,\n                  onError: e => {\n                    e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-name\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-description\",\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-price\",\n                children: [\"\\u20B9\", product.price]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"update-btn\",\n                  onClick: () => handleUpdate(product),\n                  title: \"Update Product\",\n                  children: \"\\u270F\\uFE0F Update\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"delete-btn\",\n                  onClick: () => handleDelete(product.id),\n                  title: \"Delete Product\",\n                  children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 45\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 41\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 25\n      }, this) :\n      /*#__PURE__*/\n      // Customer Card View\n      _jsxDEV(\"div\", {\n        className: \"products-grid\",\n        children: products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-image-container\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.image,\n              alt: product.name,\n              onError: e => {\n                e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"product-title\",\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"product-desc\",\n              children: product.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-price-tag\",\n              children: [\"\\u20B9\", product.price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cart-btn\",\n                onClick: () => handleAddToCart(product),\n                children: \"\\uD83D\\uDED2 Add to Cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"buy-btn\",\n                onClick: () => handleBuyNow(product),\n                children: \"\\uD83D\\uDCB3 Buy Now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 37\n          }, this)]\n        }, product.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 33\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 9\n  }, this);\n}\n_s(ProductsList, \"Ipduiw1a7ftVVJlTipoeXPWOzWQ=\", false, function () {\n  return [useNavigate];\n});\n_c = ProductsList;\nvar _c;\n$RefreshReg$(_c, \"ProductsList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductsList", "userRole", "showActions", "_s", "products", "setProducts", "loading", "setLoading", "navigate", "fetchProducts", "get", "then", "response", "data", "catch", "error", "console", "handleDelete", "id", "confirmed", "window", "confirm", "delete", "params", "alert", "err", "handleUpdate", "product", "state", "handleAddToCart", "existingCart", "localStorage", "getItem", "cart", "JSON", "parse", "existingItemIndex", "findIndex", "item", "quantity", "name", "push", "description", "price", "image", "setItem", "stringify", "handleBuyNow", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "map", "src", "alt", "onError", "e", "target", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/shared/ProductsList.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport './ProductsList.css';\n\nexport default function ProductsList({ userRole = 'customer', showActions = false }) {\n    const [products, setProducts] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const navigate = useNavigate();\n\n    const fetchProducts = () => {\n        setLoading(true);\n        axios.get('http://localhost:8080/getAllProducts')\n            .then(response => {\n                setProducts(response.data);\n                setLoading(false);\n            })\n            .catch(error => {\n                console.error('Error fetching products:', error);\n                setLoading(false);\n            });\n    };\n\n    useEffect(() => {\n        fetchProducts();\n    }, []);\n\n    const handleDelete = (id) => {\n        if (userRole !== 'admin') return;\n        \n        const confirmed = window.confirm(\"Are you sure you want to delete this product?\");\n        if (!confirmed) return;\n        \n        axios\n            .delete('http://localhost:8080/deleteProduct', { params: { id } })\n            .then(() => {\n                fetchProducts();\n                alert('Product deleted successfully!');\n            })\n            .catch(err => {\n                console.error('Delete failed:', err);\n                alert('Failed to delete product. Please try again.');\n            });\n    };\n\n    const handleUpdate = (product) => {\n        if (userRole !== 'admin') return;\n        navigate('/updateproduct', { state: { product } });\n    };\n\n    const handleAddToCart = (product) => {\n        if (userRole !== 'customer') return;\n\n        // Get existing cart from localStorage\n        const existingCart = localStorage.getItem('cart');\n        let cart = existingCart ? JSON.parse(existingCart) : [];\n\n        // Check if product already exists in cart\n        const existingItemIndex = cart.findIndex(item => item.id === product.id);\n\n        if (existingItemIndex > -1) {\n            // If product exists, increase quantity\n            cart[existingItemIndex].quantity += 1;\n            alert(`Increased ${product.name} quantity in cart!`);\n        } else {\n            // If product doesn't exist, add new item\n            cart.push({\n                id: product.id,\n                name: product.name,\n                description: product.description,\n                price: product.price,\n                image: product.image,\n                quantity: 1\n            });\n            alert(`Added ${product.name} to cart!`);\n        }\n\n        // Save updated cart to localStorage\n        localStorage.setItem('cart', JSON.stringify(cart));\n    };\n\n    const handleBuyNow = (product) => {\n        if (userRole !== 'customer') return;\n        // Buy now functionality for customers\n        alert(`Proceeding to buy ${product.name}!`);\n        // You can implement actual purchase functionality here\n    };\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"loading-spinner\"></div>\n                <p>Loading products...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"products-list-container\">\n            {userRole === 'admin' && (\n                <div className=\"page-header\">\n                    <h1>All Products</h1>\n                    <p>Manage your product inventory</p>\n                </div>\n            )}\n            \n            {userRole === 'customer' && (\n                <div className=\"page-header customer-header\">\n                    <h1>Our Products</h1>\n                    <p>Discover amazing products at great prices</p>\n                </div>\n            )}\n            \n            {products.length === 0 ? (\n                <div className=\"no-products\">\n                    <h3>No products found</h3>\n                    {userRole === 'admin' ? (\n                        <>\n                            <p>Start by adding some products to your inventory.</p>\n                            <button \n                                className=\"add-product-btn\"\n                                onClick={() => navigate('/AddProducts')}\n                            >\n                                Add Your First Product\n                            </button>\n                        </>\n                    ) : (\n                        <p>Check back later for new products!</p>\n                    )}\n                </div>\n            ) : (\n                <div className=\"products-container\">\n                    {userRole === 'admin' && (\n                        <div className=\"table-header\">\n                            <h3>Products ({products.length})</h3>\n                            <button \n                                className=\"add-product-btn\"\n                                onClick={() => navigate('/AddProducts')}\n                            >\n                                + Add New Product\n                            </button>\n                        </div>\n                    )}\n                    \n                    {userRole === 'customer' && (\n                        <div className=\"products-count\">\n                            <h3>{products.length} Products Available</h3>\n                        </div>\n                    )}\n                    \n                    {userRole === 'admin' ? (\n                        // Admin Table View\n                        <div className=\"table-wrapper\">\n                            <table className=\"products-table\">\n                                <thead>\n                                    <tr>\n                                        <th>ID</th>\n                                        <th>Image</th>\n                                        <th>Name</th>\n                                        <th>Description</th>\n                                        <th>Price</th>\n                                        <th>Actions</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {products.map(product => (\n                                        <tr key={product.id}>\n                                            <td className=\"product-id\">#{product.id}</td>\n                                            <td className=\"product-image\">\n                                                <img \n                                                    src={product.image} \n                                                    alt={product.name}\n                                                    onError={(e) => {\n                                                        e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';\n                                                    }}\n                                                />\n                                            </td>\n                                            <td className=\"product-name\">\n                                                <strong>{product.name}</strong>\n                                            </td>\n                                            <td className=\"product-description\">\n                                                {product.description}\n                                            </td>\n                                            <td className=\"product-price\">\n                                                ₹{product.price}\n                                            </td>\n                                            <td className=\"product-actions\">\n                                                <button \n                                                    className=\"update-btn\"\n                                                    onClick={() => handleUpdate(product)}\n                                                    title=\"Update Product\"\n                                                >\n                                                    ✏️ Update\n                                                </button>\n                                                <button \n                                                    className=\"delete-btn\"\n                                                    onClick={() => handleDelete(product.id)}\n                                                    title=\"Delete Product\"\n                                                >\n                                                    🗑️ Delete\n                                                </button>\n                                            </td>\n                                        </tr>\n                                    ))}\n                                </tbody>\n                            </table>\n                        </div>\n                    ) : (\n                        // Customer Card View\n                        <div className=\"products-grid\">\n                            {products.map(product => (\n                                <div key={product.id} className=\"product-card\">\n                                    <div className=\"product-image-container\">\n                                        <img \n                                            src={product.image} \n                                            alt={product.name}\n                                            onError={(e) => {\n                                                e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';\n                                            }}\n                                        />\n                                    </div>\n                                    <div className=\"product-info\">\n                                        <h3 className=\"product-title\">{product.name}</h3>\n                                        <p className=\"product-desc\">{product.description}</p>\n                                        <div className=\"product-price-tag\">₹{product.price}</div>\n                                        <div className=\"product-buttons\">\n                                            <button \n                                                className=\"cart-btn\"\n                                                onClick={() => handleAddToCart(product)}\n                                            >\n                                                🛒 Add to Cart\n                                            </button>\n                                            <button \n                                                className=\"buy-btn\"\n                                                onClick={() => handleBuyNow(product)}\n                                            >\n                                                💳 Buy Now\n                                            </button>\n                                        </div>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    )}\n                </div>\n            )}\n        </div>\n    );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5B,eAAe,SAASC,YAAYA,CAAC;EAAEC,QAAQ,GAAG,UAAU;EAAEC,WAAW,GAAG;AAAM,CAAC,EAAE;EAAAC,EAAA;EACjF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMe,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAMc,aAAa,GAAGA,CAAA,KAAM;IACxBF,UAAU,CAAC,IAAI,CAAC;IAChBb,KAAK,CAACgB,GAAG,CAAC,sCAAsC,CAAC,CAC5CC,IAAI,CAACC,QAAQ,IAAI;MACdP,WAAW,CAACO,QAAQ,CAACC,IAAI,CAAC;MAC1BN,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,CACDO,KAAK,CAACC,KAAK,IAAI;MACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDR,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACV,CAAC;EAEDf,SAAS,CAAC,MAAM;IACZiB,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,YAAY,GAAIC,EAAE,IAAK;IACzB,IAAIjB,QAAQ,KAAK,OAAO,EAAE;IAE1B,MAAMkB,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC;IACjF,IAAI,CAACF,SAAS,EAAE;IAEhBzB,KAAK,CACA4B,MAAM,CAAC,qCAAqC,EAAE;MAAEC,MAAM,EAAE;QAAEL;MAAG;IAAE,CAAC,CAAC,CACjEP,IAAI,CAAC,MAAM;MACRF,aAAa,CAAC,CAAC;MACfe,KAAK,CAAC,+BAA+B,CAAC;IAC1C,CAAC,CAAC,CACDV,KAAK,CAACW,GAAG,IAAI;MACVT,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEU,GAAG,CAAC;MACpCD,KAAK,CAAC,6CAA6C,CAAC;IACxD,CAAC,CAAC;EACV,CAAC;EAED,MAAME,YAAY,GAAIC,OAAO,IAAK;IAC9B,IAAI1B,QAAQ,KAAK,OAAO,EAAE;IAC1BO,QAAQ,CAAC,gBAAgB,EAAE;MAAEoB,KAAK,EAAE;QAAED;MAAQ;IAAE,CAAC,CAAC;EACtD,CAAC;EAED,MAAME,eAAe,GAAIF,OAAO,IAAK;IACjC,IAAI1B,QAAQ,KAAK,UAAU,EAAE;;IAE7B;IACA,MAAM6B,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACjD,IAAIC,IAAI,GAAGH,YAAY,GAAGI,IAAI,CAACC,KAAK,CAACL,YAAY,CAAC,GAAG,EAAE;;IAEvD;IACA,MAAMM,iBAAiB,GAAGH,IAAI,CAACI,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACpB,EAAE,KAAKS,OAAO,CAACT,EAAE,CAAC;IAExE,IAAIkB,iBAAiB,GAAG,CAAC,CAAC,EAAE;MACxB;MACAH,IAAI,CAACG,iBAAiB,CAAC,CAACG,QAAQ,IAAI,CAAC;MACrCf,KAAK,CAAC,aAAaG,OAAO,CAACa,IAAI,oBAAoB,CAAC;IACxD,CAAC,MAAM;MACH;MACAP,IAAI,CAACQ,IAAI,CAAC;QACNvB,EAAE,EAAES,OAAO,CAACT,EAAE;QACdsB,IAAI,EAAEb,OAAO,CAACa,IAAI;QAClBE,WAAW,EAAEf,OAAO,CAACe,WAAW;QAChCC,KAAK,EAAEhB,OAAO,CAACgB,KAAK;QACpBC,KAAK,EAAEjB,OAAO,CAACiB,KAAK;QACpBL,QAAQ,EAAE;MACd,CAAC,CAAC;MACFf,KAAK,CAAC,SAASG,OAAO,CAACa,IAAI,WAAW,CAAC;IAC3C;;IAEA;IACAT,YAAY,CAACc,OAAO,CAAC,MAAM,EAAEX,IAAI,CAACY,SAAS,CAACb,IAAI,CAAC,CAAC;EACtD,CAAC;EAED,MAAMc,YAAY,GAAIpB,OAAO,IAAK;IAC9B,IAAI1B,QAAQ,KAAK,UAAU,EAAE;IAC7B;IACAuB,KAAK,CAAC,qBAAqBG,OAAO,CAACa,IAAI,GAAG,CAAC;IAC3C;EACJ,CAAC;EAED,IAAIlC,OAAO,EAAE;IACT,oBACIT,OAAA;MAAKmD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BpD,OAAA;QAAKmD,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvCxD,OAAA;QAAAoD,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEd;EAEA,oBACIxD,OAAA;IAAKmD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,GACnChD,QAAQ,KAAK,OAAO,iBACjBJ,OAAA;MAAKmD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBpD,OAAA;QAAAoD,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBxD,OAAA;QAAAoD,QAAA,EAAG;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACR,EAEApD,QAAQ,KAAK,UAAU,iBACpBJ,OAAA;MAAKmD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBACxCpD,OAAA;QAAAoD,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrBxD,OAAA;QAAAoD,QAAA,EAAG;MAAyC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CACR,EAEAjD,QAAQ,CAACkD,MAAM,KAAK,CAAC,gBAClBzD,OAAA;MAAKmD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBpD,OAAA;QAAAoD,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACzBpD,QAAQ,KAAK,OAAO,gBACjBJ,OAAA,CAAAE,SAAA;QAAAkD,QAAA,gBACIpD,OAAA;UAAAoD,QAAA,EAAG;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvDxD,OAAA;UACImD,SAAS,EAAC,iBAAiB;UAC3BO,OAAO,EAAEA,CAAA,KAAM/C,QAAQ,CAAC,cAAc,CAAE;UAAAyC,QAAA,EAC3C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACX,CAAC,gBAEHxD,OAAA;QAAAoD,QAAA,EAAG;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAC3C;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,gBAENxD,OAAA;MAAKmD,SAAS,EAAC,oBAAoB;MAAAC,QAAA,GAC9BhD,QAAQ,KAAK,OAAO,iBACjBJ,OAAA;QAAKmD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBpD,OAAA;UAAAoD,QAAA,GAAI,YAAU,EAAC7C,QAAQ,CAACkD,MAAM,EAAC,GAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrCxD,OAAA;UACImD,SAAS,EAAC,iBAAiB;UAC3BO,OAAO,EAAEA,CAAA,KAAM/C,QAAQ,CAAC,cAAc,CAAE;UAAAyC,QAAA,EAC3C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,EAEApD,QAAQ,KAAK,UAAU,iBACpBJ,OAAA;QAAKmD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC3BpD,OAAA;UAAAoD,QAAA,GAAK7C,QAAQ,CAACkD,MAAM,EAAC,qBAAmB;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACR,EAEApD,QAAQ,KAAK,OAAO;MAAA;MACjB;MACAJ,OAAA;QAAKmD,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1BpD,OAAA;UAAOmD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BpD,OAAA;YAAAoD,QAAA,eACIpD,OAAA;cAAAoD,QAAA,gBACIpD,OAAA;gBAAAoD,QAAA,EAAI;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACXxD,OAAA;gBAAAoD,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdxD,OAAA;gBAAAoD,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbxD,OAAA;gBAAAoD,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBxD,OAAA;gBAAAoD,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdxD,OAAA;gBAAAoD,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRxD,OAAA;YAAAoD,QAAA,EACK7C,QAAQ,CAACoD,GAAG,CAAC7B,OAAO,iBACjB9B,OAAA;cAAAoD,QAAA,gBACIpD,OAAA;gBAAImD,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,GAAC,EAACtB,OAAO,CAACT,EAAE;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7CxD,OAAA;gBAAImD,SAAS,EAAC,eAAe;gBAAAC,QAAA,eACzBpD,OAAA;kBACI4D,GAAG,EAAE9B,OAAO,CAACiB,KAAM;kBACnBc,GAAG,EAAE/B,OAAO,CAACa,IAAK;kBAClBmB,OAAO,EAAGC,CAAC,IAAK;oBACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,mDAAmD;kBACtE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACLxD,OAAA;gBAAImD,SAAS,EAAC,cAAc;gBAAAC,QAAA,eACxBpD,OAAA;kBAAAoD,QAAA,EAAStB,OAAO,CAACa;gBAAI;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACLxD,OAAA;gBAAImD,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAC9BtB,OAAO,CAACe;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACLxD,OAAA;gBAAImD,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,QACzB,EAACtB,OAAO,CAACgB,KAAK;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACLxD,OAAA;gBAAImD,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC3BpD,OAAA;kBACImD,SAAS,EAAC,YAAY;kBACtBO,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAACC,OAAO,CAAE;kBACrCmC,KAAK,EAAC,gBAAgB;kBAAAb,QAAA,EACzB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTxD,OAAA;kBACImD,SAAS,EAAC,YAAY;kBACtBO,OAAO,EAAEA,CAAA,KAAMtC,YAAY,CAACU,OAAO,CAACT,EAAE,CAAE;kBACxC4C,KAAK,EAAC,gBAAgB;kBAAAb,QAAA,EACzB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA,GAnCA1B,OAAO,CAACT,EAAE;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoCf,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;MAAA;MAEN;MACAxD,OAAA;QAAKmD,SAAS,EAAC,eAAe;QAAAC,QAAA,EACzB7C,QAAQ,CAACoD,GAAG,CAAC7B,OAAO,iBACjB9B,OAAA;UAAsBmD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC1CpD,OAAA;YAAKmD,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACpCpD,OAAA;cACI4D,GAAG,EAAE9B,OAAO,CAACiB,KAAM;cACnBc,GAAG,EAAE/B,OAAO,CAACa,IAAK;cAClBmB,OAAO,EAAGC,CAAC,IAAK;gBACZA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,mDAAmD;cACtE;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNxD,OAAA;YAAKmD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBpD,OAAA;cAAImD,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEtB,OAAO,CAACa;YAAI;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjDxD,OAAA;cAAGmD,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEtB,OAAO,CAACe;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrDxD,OAAA;cAAKmD,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAAC,QAAC,EAACtB,OAAO,CAACgB,KAAK;YAAA;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzDxD,OAAA;cAAKmD,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC5BpD,OAAA;gBACImD,SAAS,EAAC,UAAU;gBACpBO,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACF,OAAO,CAAE;gBAAAsB,QAAA,EAC3C;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxD,OAAA;gBACImD,SAAS,EAAC,SAAS;gBACnBO,OAAO,EAAEA,CAAA,KAAMR,YAAY,CAACpB,OAAO,CAAE;gBAAAsB,QAAA,EACxC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA,GA5BA1B,OAAO,CAACT,EAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6Bf,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAClD,EAAA,CAnPuBH,YAAY;EAAA,QAGfL,WAAW;AAAA;AAAAoE,EAAA,GAHR/D,YAAY;AAAA,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}