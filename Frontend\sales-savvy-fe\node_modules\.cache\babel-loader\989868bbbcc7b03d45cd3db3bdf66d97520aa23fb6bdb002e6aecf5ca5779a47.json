{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\login\\\\Login.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport './Login.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Login() {\n  _s();\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const navigate = useNavigate();\n  const handleLogin = async e => {\n    e.preventDefault();\n    try {\n      const response = await axios.post('http://localhost:8080/login', {\n        username,\n        password\n      });\n      console.log('Login success:', response.data);\n      console.log(response.data);\n      if (response.data === \"Admin\") {\n        navigate('/AdminDashboard');\n        alert('Login successful');\n      } else if (response.data === \"User\") {\n        navigate('/CustomerDashboard');\n        localStorage.setItem('user', username);\n        alert('Login successful');\n      } else {\n        alert('Login failed. Check your credentials.');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      alert('Login failed. Check your credentials.');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"brand-logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-icon\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"Sales Savvy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Sign in to your account to continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleLogin,\n        className: \"login-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            className: \"form-input\",\n            placeholder: \"Enter your username\",\n            required: true,\n            onChange: e => setUsername(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"form-label\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            className: \"form-input\",\n            placeholder: \"Enter your password\",\n            required: true,\n            onChange: e => setPassword(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"btn btn-primary btn-lg login-btn\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDE80\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), \"Sign In\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"login-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Don't have an account?\", /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"link-btn\",\n              onClick: () => navigate('/signup'),\n              children: \"Create one here\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"IIPwWddbiEHHD7aoL5eQeOR7fTk=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useNavigate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "username", "setUsername", "password", "setPassword", "navigate", "handleLogin", "e", "preventDefault", "response", "post", "console", "log", "data", "alert", "localStorage", "setItem", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "placeholder", "required", "onChange", "target", "value", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/login/Login.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport axios from 'axios';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport './Login.css';\r\nexport default function Login() {\r\n  const [username, setUsername] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const navigate = useNavigate();\r\n\r\n  const handleLogin = async (e) => {\r\n    e.preventDefault();\r\n\r\n    try {\r\n      const response = await axios.post('http://localhost:8080/login', {\r\n        username,\r\n        password\r\n      });\r\n\r\n      console.log('Login success:', response.data);\r\n      console.log(response.data);\r\n      if(response.data === \"Admin\"){\r\n          navigate('/AdminDashboard');\r\n          alert('Login successful');\r\n        } else if(response.data === \"User\"){\r\n            navigate('/CustomerDashboard');\r\n            localStorage.setItem('user', username);\r\n            alert('Login successful');\r\n      } else {\r\n          alert('Login failed. Check your credentials.');\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      alert('Login failed. Check your credentials.');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"login-container\">\r\n      <div className=\"login-card\">\r\n        <div className=\"login-header\">\r\n          <div className=\"brand-logo\">\r\n            <span className=\"logo-icon\">🛒</span>\r\n            <span className=\"logo-text\">Sales Savvy</span>\r\n          </div>\r\n          <h2>Welcome Back</h2>\r\n          <p>Sign in to your account to continue</p>\r\n        </div>\r\n\r\n        <form onSubmit={handleLogin} className=\"login-form\">\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">Username</label>\r\n            <input\r\n              type=\"text\"\r\n              className=\"form-input\"\r\n              placeholder=\"Enter your username\"\r\n              required\r\n              onChange={(e) => setUsername(e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"form-group\">\r\n            <label className=\"form-label\">Password</label>\r\n            <input\r\n              type=\"password\"\r\n              className=\"form-input\"\r\n              placeholder=\"Enter your password\"\r\n              required\r\n              onChange={(e) => setPassword(e.target.value)}\r\n            />\r\n          </div>\r\n\r\n          <button type=\"submit\" className=\"btn btn-primary btn-lg login-btn\">\r\n            <span>🚀</span>\r\n            Sign In\r\n          </button>\r\n\r\n          <div className=\"login-footer\">\r\n            <p>Don't have an account?\r\n              <button\r\n                type=\"button\"\r\n                className=\"link-btn\"\r\n                onClick={() => navigate('/signup')}\r\n              >\r\n                Create one here\r\n              </button>\r\n            </p>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AACrB,eAAe,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EAC9B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAMW,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9B,MAAMU,WAAW,GAAG,MAAOC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACe,IAAI,CAAC,6BAA6B,EAAE;QAC/DT,QAAQ;QACRE;MACF,CAAC,CAAC;MAEFQ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,QAAQ,CAACI,IAAI,CAAC;MAC5CF,OAAO,CAACC,GAAG,CAACH,QAAQ,CAACI,IAAI,CAAC;MAC1B,IAAGJ,QAAQ,CAACI,IAAI,KAAK,OAAO,EAAC;QACzBR,QAAQ,CAAC,iBAAiB,CAAC;QAC3BS,KAAK,CAAC,kBAAkB,CAAC;MAC3B,CAAC,MAAM,IAAGL,QAAQ,CAACI,IAAI,KAAK,MAAM,EAAC;QAC/BR,QAAQ,CAAC,oBAAoB,CAAC;QAC9BU,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEf,QAAQ,CAAC;QACtCa,KAAK,CAAC,kBAAkB,CAAC;MAC/B,CAAC,MAAM;QACHA,KAAK,CAAC,uCAAuC,CAAC;MAClD;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCH,KAAK,CAAC,uCAAuC,CAAC;IAChD;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKoB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BrB,OAAA;MAAKoB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBrB,OAAA;QAAKoB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BrB,OAAA;UAAKoB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrB,OAAA;YAAMoB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrCzB,OAAA;YAAMoB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACNzB,OAAA;UAAAqB,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBzB,OAAA;UAAAqB,QAAA,EAAG;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAENzB,OAAA;QAAM0B,QAAQ,EAAElB,WAAY;QAACY,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACjDrB,OAAA;UAAKoB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrB,OAAA;YAAOoB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9CzB,OAAA;YACE2B,IAAI,EAAC,MAAM;YACXP,SAAS,EAAC,YAAY;YACtBQ,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;YACRC,QAAQ,EAAGrB,CAAC,IAAKL,WAAW,CAACK,CAAC,CAACsB,MAAM,CAACC,KAAK;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzB,OAAA;UAAKoB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBrB,OAAA;YAAOoB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9CzB,OAAA;YACE2B,IAAI,EAAC,UAAU;YACfP,SAAS,EAAC,YAAY;YACtBQ,WAAW,EAAC,qBAAqB;YACjCC,QAAQ;YACRC,QAAQ,EAAGrB,CAAC,IAAKH,WAAW,CAACG,CAAC,CAACsB,MAAM,CAACC,KAAK;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENzB,OAAA;UAAQ2B,IAAI,EAAC,QAAQ;UAACP,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAChErB,OAAA;YAAAqB,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,WAEjB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzB,OAAA;UAAKoB,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BrB,OAAA;YAAAqB,QAAA,GAAG,wBACD,eAAArB,OAAA;cACE2B,IAAI,EAAC,QAAQ;cACbP,SAAS,EAAC,UAAU;cACpBa,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,SAAS,CAAE;cAAAc,QAAA,EACpC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACvB,EAAA,CAvFuBD,KAAK;EAAA,QAGVH,WAAW;AAAA;AAAAoC,EAAA,GAHNjC,KAAK;AAAA,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}