/* Professional Login Page */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  padding: var(--spacing-6);
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="white" stop-opacity="0.1"/><stop offset="100%" stop-color="white" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
  pointer-events: none;
}

.login-card {
  background: var(--white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  padding: var(--spacing-10);
  width: 100%;
  max-width: 450px;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.6s ease-out;
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.logo-icon {
  font-size: 2.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-header h2 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.login-header p {
  color: var(--gray-600);
  font-size: var(--font-size-base);
}

.login-form {
  width: 100%;
}

.login-btn {
  width: 100%;
  margin-bottom: var(--spacing-6);
}

.login-footer {
  text-align: center;
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--gray-200);
}

.login-footer p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

.link-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  font-size: var(--font-size-sm);
  margin-left: var(--spacing-1);
  transition: color var(--transition-fast);
}

.link-btn:hover {
  color: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    padding: var(--spacing-4);
  }

  .login-card {
    padding: var(--spacing-8);
  }

  .login-header h2 {
    font-size: var(--font-size-2xl);
  }

  .logo-text {
    font-size: var(--font-size-xl);
  }
}