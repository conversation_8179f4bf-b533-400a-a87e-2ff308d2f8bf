package sales.savvy.entity;

import jakarta.persistence.*;
import lombok.*;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString


@Entity
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
   private Long id;
    private String username;
  private  String email;
    private String password;
   private String dob;
   private String gender;
    private String role;

@OneToOne
private Cart cart;
  }
