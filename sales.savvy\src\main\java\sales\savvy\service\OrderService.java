package sales.savvy.service;

import sales.savvy.dto.OrderRequest;
import sales.savvy.entity.Order;

import java.time.LocalDateTime;
import java.util.List;

public interface OrderService {
    
    /**
     * Create a new order after successful payment
     */
    Order createOrder(OrderRequest orderRequest) throws Exception;
    
    /**
     * Get order by order number
     */
    Order getOrderByOrderNumber(String orderNumber);
    
    /**
     * Get order by Razorpay order ID
     */
    Order getOrderByRazorpayOrderId(String razorpayOrderId);
    
    /**
     * Get order by payment ID
     */
    Order getOrderByPaymentId(String paymentId);
    
    /**
     * Get all orders for a user
     */
    List<Order> getOrdersByUsername(String username);
    
    /**
     * Get order history for a user (ordered by date)
     */
    List<Order> getOrderHistory(String username);
    
    /**
     * Get orders by status
     */
    List<Order> getOrdersByStatus(String status);
    
    /**
     * Get orders by user and status
     */
    List<Order> getOrdersByUsernameAndStatus(String username, String status);
    
    /**
     * Get orders by date range
     */
    List<Order> getOrdersByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Get user orders by date range
     */
    List<Order> getUserOrdersByDateRange(String username, LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Update order status
     */
    Order updateOrderStatus(String orderNumber, String status);
    
    /**
     * Update order tracking number
     */
    Order updateTrackingNumber(String orderNumber, String trackingNumber);
    
    /**
     * Get order by tracking number
     */
    Order getOrderByTrackingNumber(String trackingNumber);
    
    /**
     * Get user order statistics
     */
    OrderStatistics getUserOrderStatistics(String username);
    
    /**
     * Cancel order
     */
    Order cancelOrder(String orderNumber, String username) throws Exception;
    
    /**
     * Get overdue orders
     */
    List<Order> getOverdueOrders();
    
    /**
     * Generate order number
     */
    String generateOrderNumber();
    
    // Inner class for order statistics
    class OrderStatistics {
        private Long totalOrders;
        private Double totalSpent;
        private Long pendingOrders;
        private Long completedOrders;
        private Long cancelledOrders;
        
        public OrderStatistics(Long totalOrders, Double totalSpent, Long pendingOrders, 
                             Long completedOrders, Long cancelledOrders) {
            this.totalOrders = totalOrders;
            this.totalSpent = totalSpent;
            this.pendingOrders = pendingOrders;
            this.completedOrders = completedOrders;
            this.cancelledOrders = cancelledOrders;
        }
        
        // Getters and Setters
        public Long getTotalOrders() { return totalOrders; }
        public void setTotalOrders(Long totalOrders) { this.totalOrders = totalOrders; }
        
        public Double getTotalSpent() { return totalSpent; }
        public void setTotalSpent(Double totalSpent) { this.totalSpent = totalSpent; }
        
        public Long getPendingOrders() { return pendingOrders; }
        public void setPendingOrders(Long pendingOrders) { this.pendingOrders = pendingOrders; }
        
        public Long getCompletedOrders() { return completedOrders; }
        public void setCompletedOrders(Long completedOrders) { this.completedOrders = completedOrders; }
        
        public Long getCancelledOrders() { return cancelledOrders; }
        public void setCancelledOrders(Long cancelledOrders) { this.cancelledOrders = cancelledOrders; }
    }
}
