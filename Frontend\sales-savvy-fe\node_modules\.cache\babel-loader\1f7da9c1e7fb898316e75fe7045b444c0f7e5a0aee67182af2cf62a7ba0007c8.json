{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\homePage\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Navbar from '../Navbar/Navbar';\nimport './AdminDashboard.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function AdminDashboard() {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"admin-dashboard\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Welcome back! Manage your products and inventory from here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-cards\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-card\",\n          onClick: () => navigate(\"/AllProducts\"),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-icon\",\n            children: \"\\uD83D\\uDCE6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"All Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"View and manage all your products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-arrow\",\n            children: \"\\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-card\",\n          onClick: () => navigate(\"/AddProducts\"),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-icon\",\n            children: \"\\u2795\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Add Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Add new products to your inventory\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-arrow\",\n            children: \"\\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-card\",\n          onClick: () => navigate(\"/AllProducts\"),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-icon\",\n            children: \"\\uD83D\\uDCCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Analytics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"View product statistics and reports\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-arrow\",\n            children: \"\\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"dashboard-card\",\n          onClick: () => navigate(\"/AllProducts\"),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-icon\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Configure your store settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-arrow\",\n            children: \"\\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"quick-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"action-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn primary\",\n            onClick: () => navigate(\"/AddProducts\"),\n            children: \"+ Add New Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"action-btn secondary\",\n            onClick: () => navigate(\"/AllProducts\"),\n            children: \"\\uD83D\\uDCE6 View All Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n}\n_s(AdminDashboard, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useNavigate", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminDashboard", "_s", "navigate", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/homePage/AdminDashboard.jsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Navbar from '../Navbar/Navbar';\nimport './AdminDashboard.css';\n\nexport default function AdminDashboard() {\n    const navigate = useNavigate();\n    \n    return (\n        <>\n            <Navbar />\n            <div className=\"admin-dashboard\">\n                <div className=\"dashboard-header\">\n                    <h1>Admin Dashboard</h1>\n                    <p>Welcome back! Manage your products and inventory from here.</p>\n                </div>\n                \n                <div className=\"dashboard-cards\">\n                    <div className=\"dashboard-card\" onClick={() => navigate(\"/AllProducts\")}>\n                        <div className=\"card-icon\">📦</div>\n                        <h3>All Products</h3>\n                        <p>View and manage all your products</p>\n                        <div className=\"card-arrow\">→</div>\n                    </div>\n                    \n                    <div className=\"dashboard-card\" onClick={() => navigate(\"/AddProducts\")}>\n                        <div className=\"card-icon\">➕</div>\n                        <h3>Add Product</h3>\n                        <p>Add new products to your inventory</p>\n                        <div className=\"card-arrow\">→</div>\n                    </div>\n                    \n                    <div className=\"dashboard-card\" onClick={() => navigate(\"/AllProducts\")}>\n                        <div className=\"card-icon\">📊</div>\n                        <h3>Analytics</h3>\n                        <p>View product statistics and reports</p>\n                        <div className=\"card-arrow\">→</div>\n                    </div>\n                    \n                    <div className=\"dashboard-card\" onClick={() => navigate(\"/AllProducts\")}>\n                        <div className=\"card-icon\">⚙️</div>\n                        <h3>Settings</h3>\n                        <p>Configure your store settings</p>\n                        <div className=\"card-arrow\">→</div>\n                    </div>\n                </div>\n                \n                <div className=\"quick-actions\">\n                    <h3>Quick Actions</h3>\n                    <div className=\"action-buttons\">\n                        <button \n                            className=\"action-btn primary\"\n                            onClick={() => navigate(\"/AddProducts\")}\n                        >\n                            + Add New Product\n                        </button>\n                        <button \n                            className=\"action-btn secondary\"\n                            onClick={() => navigate(\"/AllProducts\")}\n                        >\n                            📦 View All Products\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </>\n    );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACrC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,oBACIG,OAAA,CAAAE,SAAA;IAAAI,QAAA,gBACIN,OAAA,CAACF,MAAM;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVV,OAAA;MAAKW,SAAS,EAAC,iBAAiB;MAAAL,QAAA,gBAC5BN,OAAA;QAAKW,SAAS,EAAC,kBAAkB;QAAAL,QAAA,gBAC7BN,OAAA;UAAAM,QAAA,EAAI;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxBV,OAAA;UAAAM,QAAA,EAAG;QAA2D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eAENV,OAAA;QAAKW,SAAS,EAAC,iBAAiB;QAAAL,QAAA,gBAC5BN,OAAA;UAAKW,SAAS,EAAC,gBAAgB;UAACC,OAAO,EAAEA,CAAA,KAAMP,QAAQ,CAAC,cAAc,CAAE;UAAAC,QAAA,gBACpEN,OAAA;YAAKW,SAAS,EAAC,WAAW;YAAAL,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnCV,OAAA;YAAAM,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBV,OAAA;YAAAM,QAAA,EAAG;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxCV,OAAA;YAAKW,SAAS,EAAC,YAAY;YAAAL,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAENV,OAAA;UAAKW,SAAS,EAAC,gBAAgB;UAACC,OAAO,EAAEA,CAAA,KAAMP,QAAQ,CAAC,cAAc,CAAE;UAAAC,QAAA,gBACpEN,OAAA;YAAKW,SAAS,EAAC,WAAW;YAAAL,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClCV,OAAA;YAAAM,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBV,OAAA;YAAAM,QAAA,EAAG;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACzCV,OAAA;YAAKW,SAAS,EAAC,YAAY;YAAAL,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAENV,OAAA;UAAKW,SAAS,EAAC,gBAAgB;UAACC,OAAO,EAAEA,CAAA,KAAMP,QAAQ,CAAC,cAAc,CAAE;UAAAC,QAAA,gBACpEN,OAAA;YAAKW,SAAS,EAAC,WAAW;YAAAL,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnCV,OAAA;YAAAM,QAAA,EAAI;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClBV,OAAA;YAAAM,QAAA,EAAG;UAAmC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC1CV,OAAA;YAAKW,SAAS,EAAC,YAAY;YAAAL,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAENV,OAAA;UAAKW,SAAS,EAAC,gBAAgB;UAACC,OAAO,EAAEA,CAAA,KAAMP,QAAQ,CAAC,cAAc,CAAE;UAAAC,QAAA,gBACpEN,OAAA;YAAKW,SAAS,EAAC,WAAW;YAAAL,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnCV,OAAA;YAAAM,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBV,OAAA;YAAAM,QAAA,EAAG;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpCV,OAAA;YAAKW,SAAS,EAAC,YAAY;YAAAL,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENV,OAAA;QAAKW,SAAS,EAAC,eAAe;QAAAL,QAAA,gBAC1BN,OAAA;UAAAM,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtBV,OAAA;UAAKW,SAAS,EAAC,gBAAgB;UAAAL,QAAA,gBAC3BN,OAAA;YACIW,SAAS,EAAC,oBAAoB;YAC9BC,OAAO,EAAEA,CAAA,KAAMP,QAAQ,CAAC,cAAc,CAAE;YAAAC,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTV,OAAA;YACIW,SAAS,EAAC,sBAAsB;YAChCC,OAAO,EAAEA,CAAA,KAAMP,QAAQ,CAAC,cAAc,CAAE;YAAAC,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACR,CAAC;AAEX;AAACN,EAAA,CA9DuBD,cAAc;EAAA,QACjBN,WAAW;AAAA;AAAAgB,EAAA,GADRV,cAAc;AAAA,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}