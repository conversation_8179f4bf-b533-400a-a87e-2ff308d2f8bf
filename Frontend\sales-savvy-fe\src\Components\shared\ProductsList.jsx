import { useEffect, useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { triggerCartUpdate, getUsername, isLoggedIn, handleApiError, showSuccessMessage } from './CartUtils';
import './ProductsList.css';

export default function ProductsList({ userRole = 'customer', showActions = false }) {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const navigate = useNavigate();

    const fetchProducts = async () => {
        setLoading(true);
        try {
            const response = await axios.get('http://localhost:8080/getAllProducts');
            setProducts(response.data || []);
            setLoading(false);
        } catch (error) {
            console.error('Error fetching products:', error);
            handleApiError(error, 'Failed to load products. Please try again.');
            setProducts([]);
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchProducts();
    }, []);

    const handleDelete = async (id) => {
        if (userRole !== 'admin') return;

        const confirmed = window.confirm("Are you sure you want to delete this product?");
        if (!confirmed) return;

        try {
            await axios.delete('http://localhost:8080/deleteProduct', { params: { id } });
            fetchProducts();
            showSuccessMessage('Product deleted successfully!');
        } catch (error) {
            console.error('Delete failed:', error);
            handleApiError(error, 'Failed to delete product. Please try again.');
        }
    };

    const handleUpdate = (product) => {
        if (userRole !== 'admin') {
            showSuccessMessage('Access denied. Admin privileges required.');
            return;
        }
        navigate('/updateproduct', { state: { product } });
    };

    const handleAddToCart = async (product) => {
        if (userRole !== 'customer') return;

        if (!isLoggedIn()) {
            showSuccessMessage('Please login to add items to cart');
            return;
        }

        const username = getUsername();

        try {
            const cartItem = {
                username: username,
                productId: product.id,
                quantity: 1
            };

            const response = await axios.post('http://localhost:8080/addToCart', cartItem);

            if (response.data === 'cart added') {
                showSuccessMessage(`Added ${product.name} to cart!`);
                triggerCartUpdate();
            } else {
                showSuccessMessage('Failed to add item to cart: ' + response.data);
            }
        } catch (error) {
            handleApiError(error, 'Failed to add item to cart. Please try again.');
        }
    };

    const handleBuyNow = (product) => {
        if (userRole !== 'customer') {
            showSuccessMessage('This feature is only available for customers.');
            return;
        }

        if (!isLoggedIn()) {
            showSuccessMessage('Please login to purchase products.');
            return;
        }

        // For now, add to cart and navigate to cart
        handleAddToCart(product);
        setTimeout(() => {
            navigate('/cart');
        }, 1000);
    };

    if (loading) {
        return (
            <div className="products-list-container">
                {userRole === 'admin' && (
                    <div className="page-header">
                        <div className="page-header-content">
                            <h1>All Products</h1>
                            <p>Manage your product inventory</p>
                        </div>
                    </div>
                )}

                {userRole === 'customer' && (
                    <div className="page-header customer-header">
                        <div className="page-header-content">
                            <h1>Our Products</h1>
                            <p>Discover amazing products at great prices</p>
                        </div>
                    </div>
                )}

                <div className="main-content-area">
                    <div className="loading-container">
                        <div className="loading-spinner"></div>
                        <p>Loading products...</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="products-list-container">
            {userRole === 'admin' && (
                <div className="page-header">
                    <div className="page-header-content">
                        <h1>All Products</h1>
                        <p>Manage your product inventory</p>
                    </div>
                </div>
            )}

            {userRole === 'customer' && (
                <div className="page-header customer-header">
                    <div className="page-header-content">
                        <h1>Our Products</h1>
                        <p>Discover amazing products at great prices</p>
                    </div>
                </div>
            )}

            <div className="main-content-area">
                {products.length === 0 ? (
                    <div className="no-products">
                        <h3>No products found</h3>
                        {userRole === 'admin' ? (
                            <>
                                <p>Start by adding some products to your inventory.</p>
                                <button
                                    className="add-product-btn"
                                    onClick={() => navigate('/AddProducts')}
                                >
                                    Add Your First Product
                                </button>
                            </>
                        ) : (
                            <p>Check back later for new products!</p>
                        )}
                    </div>
                ) : (
                    <div className="products-container">
                    {userRole === 'admin' && (
                        <div className="table-header">
                            <h3>Products ({products.length})</h3>
                            <button 
                                className="add-product-btn"
                                onClick={() => navigate('/AddProducts')}
                            >
                                + Add New Product
                            </button>
                        </div>
                    )}
                    
                    {userRole === 'customer' && (
                        <div className="products-count">
                            <h3>{products.length} Products Available</h3>
                        </div>
                    )}
                    
                    {userRole === 'admin' ? (
                        // Admin Table View
                        <div className="table-wrapper">
                            <table className="products-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Image</th>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Price</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {products.map(product => (
                                        <tr key={product.id}>
                                            <td className="product-id">#{product.id}</td>
                                            <td className="product-image">
                                                <img 
                                                    src={product.image} 
                                                    alt={product.name}
                                                    onError={(e) => {
                                                        e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';
                                                    }}
                                                />
                                            </td>
                                            <td className="product-name">
                                                <strong>{product.name}</strong>
                                            </td>
                                            <td className="product-description">
                                                {product.description}
                                            </td>
                                            <td className="product-price">
                                                ₹{product.price}
                                            </td>
                                            <td className="product-actions">
                                                <button 
                                                    className="update-btn"
                                                    onClick={() => handleUpdate(product)}
                                                    title="Update Product"
                                                >
                                                    ✏️ Update
                                                </button>
                                                <button 
                                                    className="delete-btn"
                                                    onClick={() => handleDelete(product.id)}
                                                    title="Delete Product"
                                                >
                                                    🗑️ Delete
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    ) : (
                        // Customer Card View
                        <div className="products-grid">
                            {products.map(product => (
                                <div key={product.id} className="product-card">
                                    <div className="product-image-container">
                                        <img 
                                            src={product.image} 
                                            alt={product.name}
                                            onError={(e) => {
                                                e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';
                                            }}
                                        />
                                    </div>
                                    <div className="product-info">
                                        <h3 className="product-title">{product.name}</h3>
                                        <p className="product-desc">{product.description}</p>
                                        <div className="product-price-tag">₹{product.price}</div>
                                        <div className="product-buttons">
                                            <button 
                                                className="cart-btn"
                                                onClick={() => handleAddToCart(product)}
                                            >
                                                🛒 Add to Cart
                                            </button>
                                            <button 
                                                className="buy-btn"
                                                onClick={() => handleBuyNow(product)}
                                            >
                                                💳 Buy Now
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                    </div>
                )}
            </div>
        </div>
    );
}
