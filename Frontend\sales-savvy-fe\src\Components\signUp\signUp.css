/* Gradient Background */
body {
  margin: 0;
  padding: 0;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  background: linear-gradient(135deg, #667eea, #764ba2);
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.awesome-signup-wrapper {
  background: #fff;
  padding: 40px 35px;
  border-radius: 15px;
  box-shadow: 0px 10px 40px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 450px;
  animation: fadeIn 0.7s ease-in-out;
}

.awesome-signup-form h2 {
  text-align: center;
  margin-bottom: 25px;
  color: #333;
  font-size: 28px;
  font-weight: 600;
  
}

.awesome-signup-form input,
.awesome-signup-form select {
  width: 90%;
  padding: 12px 15px;
  margin-bottom: 18px;
  border: 1px solid #ccc;
  border-radius: 8px;
  transition: 0.3s ease;
  font-size: 15px;
}

.awesome-signup-form input:focus,
.awesome-signup-form select:focus {
  border-color: #667eea;
  box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
  outline: none;
}

.awesome-signup-form label {
  font-weight: 500;
  color: #444;
  margin-bottom: 5px;
  display: block;
}

.radio-group {
  margin-bottom: 20px;
}

.radio-options {
  display: flex;
  /* justify-content: space-between; */
  flex-direction: row;
  gap: 10px;
}

.radio-options label {
  font-weight: normal;
  font-size: 14px;
  display: flex;
  gap: 2px;
}

.awesome-signup-form button {
  width: 100%;
  background: #667eea;
  color: white;
  padding: 12px;
  border: none;
  font-size: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
  margin-bottom: 10px;
}

.awesome-signup-form button:hover {
  background: #0c0e16;
  color: #fff;
  font-weight: 600;
}

/* Fade-in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
