/* Professional SignUp Page */
.signup-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  padding: var(--spacing-6);
  position: relative;
  overflow: hidden;
}

.signup-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="white" stop-opacity="0.1"/><stop offset="100%" stop-color="white" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
  pointer-events: none;
}

.signup-card {
  background: var(--white);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  padding: var(--spacing-10);
  width: 100%;
  max-width: 500px;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.6s ease-out;
  max-height: 90vh;
  overflow-y: auto;
}

.signup-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.logo-icon {
  font-size: 2.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.signup-header h2 {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-2);
}

.signup-header p {
  color: var(--gray-600);
  font-size: var(--font-size-base);
}

.signup-form {
  width: 100%;
}

.radio-group {
  display: flex;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  padding: var(--spacing-3);
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  flex: 1;
  min-width: 120px;
}

.radio-option:hover {
  border-color: var(--primary-color);
  background: var(--gray-50);
}

.radio-option input[type="radio"] {
  margin: 0;
  width: auto;
}

.radio-option input[type="radio"]:checked + .radio-label {
  color: var(--primary-color);
  font-weight: 600;
}

.radio-option:has(input[type="radio"]:checked) {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.radio-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--gray-700);
}

.role-icon {
  font-size: var(--font-size-base);
}

.signup-btn {
  width: 100%;
  margin-bottom: var(--spacing-6);
}

.signup-footer {
  text-align: center;
  padding-top: var(--spacing-6);
  border-top: 1px solid var(--gray-200);
}

.signup-footer p {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
}

.link-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  font-size: var(--font-size-sm);
  margin-left: var(--spacing-1);
  transition: color var(--transition-fast);
}

.link-btn:hover {
  color: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
  .signup-container {
    padding: var(--spacing-4);
  }

  .signup-card {
    padding: var(--spacing-8);
    max-height: 95vh;
  }

  .radio-group {
    flex-direction: column;
  }

  .radio-option {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .signup-card {
    padding: var(--spacing-6);
  }

  .signup-header h2 {
    font-size: var(--font-size-2xl);
  }

  .logo-text {
    font-size: var(--font-size-xl);
  }
}
