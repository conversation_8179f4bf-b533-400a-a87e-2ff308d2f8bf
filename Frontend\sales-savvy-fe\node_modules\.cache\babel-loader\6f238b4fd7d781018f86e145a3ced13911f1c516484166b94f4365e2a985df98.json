{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\shared\\\\ProductsList.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { triggerCartUpdate, getUsername, isLoggedIn, handleApiError, showSuccessMessage } from './CartUtils';\nimport './ProductsList.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function ProductsList({\n  userRole = 'customer',\n  showActions = false\n}) {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const fetchProducts = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('http://localhost:8080/getAllProducts');\n      setProducts(response.data || []);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      handleApiError(error, 'Failed to load products. Please try again.');\n      setProducts([]);\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const handleDelete = id => {\n    if (userRole !== 'admin') return;\n    const confirmed = window.confirm(\"Are you sure you want to delete this product?\");\n    if (!confirmed) return;\n    axios.delete('http://localhost:8080/deleteProduct', {\n      params: {\n        id\n      }\n    }).then(() => {\n      fetchProducts();\n      alert('Product deleted successfully!');\n    }).catch(err => {\n      console.error('Delete failed:', err);\n      alert('Failed to delete product. Please try again.');\n    });\n  };\n  const handleUpdate = product => {\n    if (userRole !== 'admin') return;\n    navigate('/updateproduct', {\n      state: {\n        product\n      }\n    });\n  };\n  const handleAddToCart = async product => {\n    if (userRole !== 'customer') return;\n    if (!isLoggedIn()) {\n      showSuccessMessage('Please login to add items to cart');\n      return;\n    }\n    const username = getUsername();\n    try {\n      const cartItem = {\n        username: username,\n        productId: product.id,\n        quantity: 1\n      };\n      const response = await axios.post('http://localhost:8080/addToCart', cartItem);\n      if (response.data === 'cart added') {\n        showSuccessMessage(`Added ${product.name} to cart!`);\n        triggerCartUpdate();\n      } else {\n        showSuccessMessage('Failed to add item to cart: ' + response.data);\n      }\n    } catch (error) {\n      handleApiError(error, 'Failed to add item to cart. Please try again.');\n    }\n  };\n  const handleBuyNow = product => {\n    if (userRole !== 'customer') return;\n    // Buy now functionality for customers\n    alert(`Proceeding to buy ${product.name}!`);\n    // You can implement actual purchase functionality here\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"products-list-container\",\n    children: [userRole === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"All Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Manage your product inventory\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 17\n    }, this), userRole === 'customer' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header customer-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Our Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Discover amazing products at great prices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content-area\",\n      children: products.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-products\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No products found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 25\n        }, this), userRole === 'admin' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Start by adding some products to your inventory.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"add-product-btn\",\n            onClick: () => navigate('/AddProducts'),\n            children: \"Add Your First Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Check back later for new products!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"products-container\",\n        children: [userRole === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Products (\", products.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"add-product-btn\",\n            onClick: () => navigate('/AddProducts'),\n            children: \"+ Add New Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 25\n        }, this), userRole === 'customer' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"products-count\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [products.length, \" Products Available\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 25\n        }, this), userRole === 'admin' ?\n        /*#__PURE__*/\n        // Admin Table View\n        _jsxDEV(\"div\", {\n          className: \"table-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"products-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Price\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-id\",\n                  children: [\"#\", product.id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-image\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: product.image,\n                    alt: product.name,\n                    onError: e => {\n                      e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-name\",\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 185,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-description\",\n                  children: product.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-price\",\n                  children: [\"\\u20B9\", product.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"update-btn\",\n                    onClick: () => handleUpdate(product),\n                    title: \"Update Product\",\n                    children: \"\\u270F\\uFE0F Update\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"delete-btn\",\n                    onClick: () => handleDelete(product.id),\n                    title: \"Delete Product\",\n                    children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 45\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 25\n        }, this) :\n        /*#__PURE__*/\n        // Customer Card View\n        _jsxDEV(\"div\", {\n          className: \"products-grid\",\n          children: products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-image-container\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name,\n                onError: e => {\n                  e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"product-title\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-desc\",\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-price-tag\",\n                children: [\"\\u20B9\", product.price]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"cart-btn\",\n                  onClick: () => handleAddToCart(product),\n                  children: \"\\uD83D\\uDED2 Add to Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"buy-btn\",\n                  onClick: () => handleBuyNow(product),\n                  children: \"\\uD83D\\uDCB3 Buy Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 37\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 9\n  }, this);\n}\n_s(ProductsList, \"Ipduiw1a7ftVVJlTipoeXPWOzWQ=\", false, function () {\n  return [useNavigate];\n});\n_c = ProductsList;\nvar _c;\n$RefreshReg$(_c, \"ProductsList\");", "map": {"version": 3, "names": ["useEffect", "useState", "axios", "useNavigate", "triggerCartUpdate", "getUsername", "isLoggedIn", "handleApiError", "showSuccessMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductsList", "userRole", "showActions", "_s", "products", "setProducts", "loading", "setLoading", "navigate", "fetchProducts", "response", "get", "data", "error", "console", "handleDelete", "id", "confirmed", "window", "confirm", "delete", "params", "then", "alert", "catch", "err", "handleUpdate", "product", "state", "handleAddToCart", "username", "cartItem", "productId", "quantity", "post", "name", "handleBuyNow", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "map", "src", "image", "alt", "onError", "e", "target", "description", "price", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/shared/ProductsList.jsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { triggerCartUpdate, getUsername, isLoggedIn, handleApiError, showSuccessMessage } from './CartUtils';\nimport './ProductsList.css';\n\nexport default function ProductsList({ userRole = 'customer', showActions = false }) {\n    const [products, setProducts] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const navigate = useNavigate();\n\n    const fetchProducts = async () => {\n        setLoading(true);\n        try {\n            const response = await axios.get('http://localhost:8080/getAllProducts');\n            setProducts(response.data || []);\n            setLoading(false);\n        } catch (error) {\n            console.error('Error fetching products:', error);\n            handleApiError(error, 'Failed to load products. Please try again.');\n            setProducts([]);\n            setLoading(false);\n        }\n    };\n\n    useEffect(() => {\n        fetchProducts();\n    }, []);\n\n    const handleDelete = (id) => {\n        if (userRole !== 'admin') return;\n        \n        const confirmed = window.confirm(\"Are you sure you want to delete this product?\");\n        if (!confirmed) return;\n        \n        axios\n            .delete('http://localhost:8080/deleteProduct', { params: { id } })\n            .then(() => {\n                fetchProducts();\n                alert('Product deleted successfully!');\n            })\n            .catch(err => {\n                console.error('Delete failed:', err);\n                alert('Failed to delete product. Please try again.');\n            });\n    };\n\n    const handleUpdate = (product) => {\n        if (userRole !== 'admin') return;\n        navigate('/updateproduct', { state: { product } });\n    };\n\n    const handleAddToCart = async (product) => {\n        if (userRole !== 'customer') return;\n\n        if (!isLoggedIn()) {\n            showSuccessMessage('Please login to add items to cart');\n            return;\n        }\n\n        const username = getUsername();\n\n        try {\n            const cartItem = {\n                username: username,\n                productId: product.id,\n                quantity: 1\n            };\n\n            const response = await axios.post('http://localhost:8080/addToCart', cartItem);\n\n            if (response.data === 'cart added') {\n                showSuccessMessage(`Added ${product.name} to cart!`);\n                triggerCartUpdate();\n            } else {\n                showSuccessMessage('Failed to add item to cart: ' + response.data);\n            }\n        } catch (error) {\n            handleApiError(error, 'Failed to add item to cart. Please try again.');\n        }\n    };\n\n    const handleBuyNow = (product) => {\n        if (userRole !== 'customer') return;\n        // Buy now functionality for customers\n        alert(`Proceeding to buy ${product.name}!`);\n        // You can implement actual purchase functionality here\n    };\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"loading-spinner\"></div>\n                <p>Loading products...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"products-list-container\">\n            {userRole === 'admin' && (\n                <div className=\"page-header\">\n                    <div className=\"page-header-content\">\n                        <h1>All Products</h1>\n                        <p>Manage your product inventory</p>\n                    </div>\n                </div>\n            )}\n\n            {userRole === 'customer' && (\n                <div className=\"page-header customer-header\">\n                    <div className=\"page-header-content\">\n                        <h1>Our Products</h1>\n                        <p>Discover amazing products at great prices</p>\n                    </div>\n                </div>\n            )}\n\n            <div className=\"main-content-area\">\n                {products.length === 0 ? (\n                    <div className=\"no-products\">\n                        <h3>No products found</h3>\n                        {userRole === 'admin' ? (\n                            <>\n                                <p>Start by adding some products to your inventory.</p>\n                                <button\n                                    className=\"add-product-btn\"\n                                    onClick={() => navigate('/AddProducts')}\n                                >\n                                    Add Your First Product\n                                </button>\n                            </>\n                        ) : (\n                            <p>Check back later for new products!</p>\n                        )}\n                    </div>\n                ) : (\n                    <div className=\"products-container\">\n                    {userRole === 'admin' && (\n                        <div className=\"table-header\">\n                            <h3>Products ({products.length})</h3>\n                            <button \n                                className=\"add-product-btn\"\n                                onClick={() => navigate('/AddProducts')}\n                            >\n                                + Add New Product\n                            </button>\n                        </div>\n                    )}\n                    \n                    {userRole === 'customer' && (\n                        <div className=\"products-count\">\n                            <h3>{products.length} Products Available</h3>\n                        </div>\n                    )}\n                    \n                    {userRole === 'admin' ? (\n                        // Admin Table View\n                        <div className=\"table-wrapper\">\n                            <table className=\"products-table\">\n                                <thead>\n                                    <tr>\n                                        <th>ID</th>\n                                        <th>Image</th>\n                                        <th>Name</th>\n                                        <th>Description</th>\n                                        <th>Price</th>\n                                        <th>Actions</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {products.map(product => (\n                                        <tr key={product.id}>\n                                            <td className=\"product-id\">#{product.id}</td>\n                                            <td className=\"product-image\">\n                                                <img \n                                                    src={product.image} \n                                                    alt={product.name}\n                                                    onError={(e) => {\n                                                        e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';\n                                                    }}\n                                                />\n                                            </td>\n                                            <td className=\"product-name\">\n                                                <strong>{product.name}</strong>\n                                            </td>\n                                            <td className=\"product-description\">\n                                                {product.description}\n                                            </td>\n                                            <td className=\"product-price\">\n                                                ₹{product.price}\n                                            </td>\n                                            <td className=\"product-actions\">\n                                                <button \n                                                    className=\"update-btn\"\n                                                    onClick={() => handleUpdate(product)}\n                                                    title=\"Update Product\"\n                                                >\n                                                    ✏️ Update\n                                                </button>\n                                                <button \n                                                    className=\"delete-btn\"\n                                                    onClick={() => handleDelete(product.id)}\n                                                    title=\"Delete Product\"\n                                                >\n                                                    🗑️ Delete\n                                                </button>\n                                            </td>\n                                        </tr>\n                                    ))}\n                                </tbody>\n                            </table>\n                        </div>\n                    ) : (\n                        // Customer Card View\n                        <div className=\"products-grid\">\n                            {products.map(product => (\n                                <div key={product.id} className=\"product-card\">\n                                    <div className=\"product-image-container\">\n                                        <img \n                                            src={product.image} \n                                            alt={product.name}\n                                            onError={(e) => {\n                                                e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';\n                                            }}\n                                        />\n                                    </div>\n                                    <div className=\"product-info\">\n                                        <h3 className=\"product-title\">{product.name}</h3>\n                                        <p className=\"product-desc\">{product.description}</p>\n                                        <div className=\"product-price-tag\">₹{product.price}</div>\n                                        <div className=\"product-buttons\">\n                                            <button \n                                                className=\"cart-btn\"\n                                                onClick={() => handleAddToCart(product)}\n                                            >\n                                                🛒 Add to Cart\n                                            </button>\n                                            <button \n                                                className=\"buy-btn\"\n                                                onClick={() => handleBuyNow(product)}\n                                            >\n                                                💳 Buy Now\n                                            </button>\n                                        </div>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    )}\n                    </div>\n                )}\n            </div>\n        </div>\n    );\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,cAAc,EAAEC,kBAAkB,QAAQ,aAAa;AAC5G,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5B,eAAe,SAASC,YAAYA,CAAC;EAAEC,QAAQ,GAAG,UAAU;EAAEC,WAAW,GAAG;AAAM,CAAC,EAAE;EAAAC,EAAA;EACjF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMoB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9B,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9BF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAMG,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAAC,sCAAsC,CAAC;MACxEN,WAAW,CAACK,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;MAChCL,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDnB,cAAc,CAACmB,KAAK,EAAE,4CAA4C,CAAC;MACnER,WAAW,CAAC,EAAE,CAAC;MACfE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAEDpB,SAAS,CAAC,MAAM;IACZsB,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,YAAY,GAAIC,EAAE,IAAK;IACzB,IAAIf,QAAQ,KAAK,OAAO,EAAE;IAE1B,MAAMgB,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC;IACjF,IAAI,CAACF,SAAS,EAAE;IAEhB5B,KAAK,CACA+B,MAAM,CAAC,qCAAqC,EAAE;MAAEC,MAAM,EAAE;QAAEL;MAAG;IAAE,CAAC,CAAC,CACjEM,IAAI,CAAC,MAAM;MACRb,aAAa,CAAC,CAAC;MACfc,KAAK,CAAC,+BAA+B,CAAC;IAC1C,CAAC,CAAC,CACDC,KAAK,CAACC,GAAG,IAAI;MACVX,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEY,GAAG,CAAC;MACpCF,KAAK,CAAC,6CAA6C,CAAC;IACxD,CAAC,CAAC;EACV,CAAC;EAED,MAAMG,YAAY,GAAIC,OAAO,IAAK;IAC9B,IAAI1B,QAAQ,KAAK,OAAO,EAAE;IAC1BO,QAAQ,CAAC,gBAAgB,EAAE;MAAEoB,KAAK,EAAE;QAAED;MAAQ;IAAE,CAAC,CAAC;EACtD,CAAC;EAED,MAAME,eAAe,GAAG,MAAOF,OAAO,IAAK;IACvC,IAAI1B,QAAQ,KAAK,UAAU,EAAE;IAE7B,IAAI,CAACR,UAAU,CAAC,CAAC,EAAE;MACfE,kBAAkB,CAAC,mCAAmC,CAAC;MACvD;IACJ;IAEA,MAAMmC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;IAE9B,IAAI;MACA,MAAMuC,QAAQ,GAAG;QACbD,QAAQ,EAAEA,QAAQ;QAClBE,SAAS,EAAEL,OAAO,CAACX,EAAE;QACrBiB,QAAQ,EAAE;MACd,CAAC;MAED,MAAMvB,QAAQ,GAAG,MAAMrB,KAAK,CAAC6C,IAAI,CAAC,iCAAiC,EAAEH,QAAQ,CAAC;MAE9E,IAAIrB,QAAQ,CAACE,IAAI,KAAK,YAAY,EAAE;QAChCjB,kBAAkB,CAAC,SAASgC,OAAO,CAACQ,IAAI,WAAW,CAAC;QACpD5C,iBAAiB,CAAC,CAAC;MACvB,CAAC,MAAM;QACHI,kBAAkB,CAAC,8BAA8B,GAAGe,QAAQ,CAACE,IAAI,CAAC;MACtE;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZnB,cAAc,CAACmB,KAAK,EAAE,+CAA+C,CAAC;IAC1E;EACJ,CAAC;EAED,MAAMuB,YAAY,GAAIT,OAAO,IAAK;IAC9B,IAAI1B,QAAQ,KAAK,UAAU,EAAE;IAC7B;IACAsB,KAAK,CAAC,qBAAqBI,OAAO,CAACQ,IAAI,GAAG,CAAC;IAC3C;EACJ,CAAC;EAED,IAAI7B,OAAO,EAAE;IACT,oBACIT,OAAA;MAAKwC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BzC,OAAA;QAAKwC,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC7C,OAAA;QAAAyC,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEd;EAEA,oBACI7C,OAAA;IAAKwC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,GACnCrC,QAAQ,KAAK,OAAO,iBACjBJ,OAAA;MAAKwC,SAAS,EAAC,aAAa;MAAAC,QAAA,eACxBzC,OAAA;QAAKwC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCzC,OAAA;UAAAyC,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrB7C,OAAA;UAAAyC,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEAzC,QAAQ,KAAK,UAAU,iBACpBJ,OAAA;MAAKwC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eACxCzC,OAAA;QAAKwC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCzC,OAAA;UAAAyC,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrB7C,OAAA;UAAAyC,QAAA,EAAG;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,eAED7C,OAAA;MAAKwC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC7BlC,QAAQ,CAACuC,MAAM,KAAK,CAAC,gBAClB9C,OAAA;QAAKwC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBzC,OAAA;UAAAyC,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACzBzC,QAAQ,KAAK,OAAO,gBACjBJ,OAAA,CAAAE,SAAA;UAAAuC,QAAA,gBACIzC,OAAA;YAAAyC,QAAA,EAAG;UAAgD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvD7C,OAAA;YACIwC,SAAS,EAAC,iBAAiB;YAC3BO,OAAO,EAAEA,CAAA,KAAMpC,QAAQ,CAAC,cAAc,CAAE;YAAA8B,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACX,CAAC,gBAEH7C,OAAA;UAAAyC,QAAA,EAAG;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC3C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,gBAEN7C,OAAA;QAAKwC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,GAClCrC,QAAQ,KAAK,OAAO,iBACjBJ,OAAA;UAAKwC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBzC,OAAA;YAAAyC,QAAA,GAAI,YAAU,EAAClC,QAAQ,CAACuC,MAAM,EAAC,GAAC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrC7C,OAAA;YACIwC,SAAS,EAAC,iBAAiB;YAC3BO,OAAO,EAAEA,CAAA,KAAMpC,QAAQ,CAAC,cAAc,CAAE;YAAA8B,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR,EAEAzC,QAAQ,KAAK,UAAU,iBACpBJ,OAAA;UAAKwC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC3BzC,OAAA;YAAAyC,QAAA,GAAKlC,QAAQ,CAACuC,MAAM,EAAC,qBAAmB;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACR,EAEAzC,QAAQ,KAAK,OAAO;QAAA;QACjB;QACAJ,OAAA;UAAKwC,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC1BzC,OAAA;YAAOwC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzC,OAAA;cAAAyC,QAAA,eACIzC,OAAA;gBAAAyC,QAAA,gBACIzC,OAAA;kBAAAyC,QAAA,EAAI;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACX7C,OAAA;kBAAAyC,QAAA,EAAI;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd7C,OAAA;kBAAAyC,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb7C,OAAA;kBAAAyC,QAAA,EAAI;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpB7C,OAAA;kBAAAyC,QAAA,EAAI;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd7C,OAAA;kBAAAyC,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACR7C,OAAA;cAAAyC,QAAA,EACKlC,QAAQ,CAACyC,GAAG,CAAClB,OAAO,iBACjB9B,OAAA;gBAAAyC,QAAA,gBACIzC,OAAA;kBAAIwC,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,GAAC,EAACX,OAAO,CAACX,EAAE;gBAAA;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7C7C,OAAA;kBAAIwC,SAAS,EAAC,eAAe;kBAAAC,QAAA,eACzBzC,OAAA;oBACIiD,GAAG,EAAEnB,OAAO,CAACoB,KAAM;oBACnBC,GAAG,EAAErB,OAAO,CAACQ,IAAK;oBAClBc,OAAO,EAAGC,CAAC,IAAK;sBACZA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,mDAAmD;oBACtE;kBAAE;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACL7C,OAAA;kBAAIwC,SAAS,EAAC,cAAc;kBAAAC,QAAA,eACxBzC,OAAA;oBAAAyC,QAAA,EAASX,OAAO,CAACQ;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACL7C,OAAA;kBAAIwC,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAC9BX,OAAO,CAACyB;gBAAW;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACL7C,OAAA;kBAAIwC,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,QACzB,EAACX,OAAO,CAAC0B,KAAK;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACL7C,OAAA;kBAAIwC,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC3BzC,OAAA;oBACIwC,SAAS,EAAC,YAAY;oBACtBO,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAACC,OAAO,CAAE;oBACrC2B,KAAK,EAAC,gBAAgB;oBAAAhB,QAAA,EACzB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT7C,OAAA;oBACIwC,SAAS,EAAC,YAAY;oBACtBO,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAACY,OAAO,CAACX,EAAE,CAAE;oBACxCsC,KAAK,EAAC,gBAAgB;oBAAAhB,QAAA,EACzB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA,GAnCAf,OAAO,CAACX,EAAE;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoCf,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;QAAA;QAEN;QACA7C,OAAA;UAAKwC,SAAS,EAAC,eAAe;UAAAC,QAAA,EACzBlC,QAAQ,CAACyC,GAAG,CAAClB,OAAO,iBACjB9B,OAAA;YAAsBwC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1CzC,OAAA;cAAKwC,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACpCzC,OAAA;gBACIiD,GAAG,EAAEnB,OAAO,CAACoB,KAAM;gBACnBC,GAAG,EAAErB,OAAO,CAACQ,IAAK;gBAClBc,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,mDAAmD;gBACtE;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN7C,OAAA;cAAKwC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBzC,OAAA;gBAAIwC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEX,OAAO,CAACQ;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjD7C,OAAA;gBAAGwC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEX,OAAO,CAACyB;cAAW;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrD7C,OAAA;gBAAKwC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,GAAC,QAAC,EAACX,OAAO,CAAC0B,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzD7C,OAAA;gBAAKwC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC5BzC,OAAA;kBACIwC,SAAS,EAAC,UAAU;kBACpBO,OAAO,EAAEA,CAAA,KAAMf,eAAe,CAACF,OAAO,CAAE;kBAAAW,QAAA,EAC3C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT7C,OAAA;kBACIwC,SAAS,EAAC,SAAS;kBACnBO,OAAO,EAAEA,CAAA,KAAMR,YAAY,CAACT,OAAO,CAAE;kBAAAW,QAAA,EACxC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA,GA5BAf,OAAO,CAACX,EAAE;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6Bf,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACvC,EAAA,CAzPuBH,YAAY;EAAA,QAGfV,WAAW;AAAA;AAAAiE,EAAA,GAHRvD,YAAY;AAAA,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}