/* Full Screen Products List Styles */
.products-list-container {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: calc(100vh - 80px);
    padding: 0;
    margin: 0;
    width: 100%;
}

.page-header {
    background: white;
    padding: 40px 0;
    text-align: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0;
}

.page-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

.page-header h1 {
    color: #2c3e50;
    font-size: 3rem;
    margin-bottom: 15px;
    font-weight: 700;
}

.page-header p {
    color: #6c757d;
    font-size: 1.2rem;
    margin: 0;
    font-weight: 400;
}

.customer-header h1 {
    background: linear-gradient(135deg, #28a745, #20c997);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Loading Styles */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 50vh;
    color: #666;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No Products Styles */
.no-products {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    margin: 0 auto;
}

.no-products h3 {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 15px;
}

.no-products p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 30px;
}

/* Main Content Area */
.main-content-area {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px;
}

/* Admin Table Styles */
.products-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #f1f3f4;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 40px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.table-header h3 {
    color: #2c3e50;
    font-size: 1.6rem;
    margin: 0;
    font-weight: 700;
}

.products-count {
    padding: 30px 40px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    text-align: center;
}

.products-count h3 {
    color: #2c3e50;
    font-size: 1.6rem;
    margin: 0;
    font-weight: 700;
}

.add-product-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-product-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.table-wrapper {
    overflow-x: auto;
    max-height: 600px;
    overflow-y: auto;
}

.products-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.products-table thead {
    background: #667eea;
    color: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

.products-table th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.products-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.products-table tbody tr:hover {
    background-color: #f8f9fa;
}

.product-id {
    font-weight: 600;
    color: #667eea;
    font-size: 13px;
}

.product-image img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

.product-name {
    font-weight: 600;
    color: #333;
    max-width: 200px;
}

.product-description {
    color: #666;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.product-price {
    font-weight: 600;
    color: #28a745;
    font-size: 15px;
}

.product-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.update-btn, .delete-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.update-btn {
    background: #17a2b8;
    color: white;
}

.update-btn:hover {
    background: #138496;
    transform: translateY(-1px);
}

.delete-btn {
    background: #dc3545;
    color: white;
}

.delete-btn:hover {
    background: #c82333;
    transform: translateY(-1px);
}

/* Customer Card Grid Styles */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 30px;
    padding: 0;
    justify-content: center;
}

.product-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.product-image-container {
    position: relative;
    height: 200px;
    overflow: hidden;
    width: 100%;
}

.product-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image-container img {
    transform: scale(1.05);
}

.product-info {
    padding: 10px;
}

.product-title {
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    line-height: 1.3;
}

.product-desc {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price-tag {
    font-size: 1.5rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 20px;
}

.product-buttons {
    display: flex;
    gap: 10px;
}

.cart-btn, .buy-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.cart-btn {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e9ecef;
}

.cart-btn:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.buy-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.buy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content-area {
        padding: 30px;
    }

    .page-header-content {
        padding: 0 30px;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 30px 0;
    }

    .page-header-content {
        padding: 0 20px;
    }

    .page-header h1 {
        font-size: 2.2rem;
    }

    .page-header p {
        font-size: 1rem;
    }

    .main-content-area {
        padding: 20px;
    }

    .table-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
        padding: 25px 30px;
    }

    .products-count {
        padding: 25px 30px;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .product-buttons {
        flex-direction: column;
    }

    .products-table {
        font-size: 12px;
    }

    .products-table th,
    .products-table td {
        padding: 12px 10px;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 20px 0;
    }

    .page-header-content {
        padding: 0 15px;
    }

    .page-header h1 {
        font-size: 1.8rem;
    }

    .page-header p {
        font-size: 0.9rem;
    }

    .main-content-area {
        padding: 15px;
    }

    .table-header,
    .products-count {
        padding: 20px 25px;
    }

    .table-header h3,
    .products-count h3 {
        font-size: 1.3rem;
    }

    .products-grid {
        gap: 20px;
    }

    .product-card {
        margin: 0;
    }

    .product-info {
        padding: 15px;
    }

    .product-title {
        font-size: 1.1rem;
    }

    .product-price-tag {
        font-size: 1.3rem;
    }

    .product-description {
        max-width: 150px;
    }

    .product-name {
        max-width: 120px;
    }

    .no-products {
        padding: 40px 20px;
        margin: 0 10px;
    }

    .no-products h3 {
        font-size: 1.5rem;
    }

    .no-products p {
        font-size: 1rem;
    }
}
