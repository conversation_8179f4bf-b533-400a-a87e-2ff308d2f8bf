{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\Navbar\\\\CustomerNavbar.jsx\",\n  _s = $RefreshSig$();\n// import React, { useState, useEffect } from 'react';\n// import { useNavigate, useLocation } from 'react-router-dom';\n// import axios from 'axios';\n// import './CustomerNavbar.css';\n\n// export default function CustomerNavbar() {\n//     const [isMenuOpen, setIsMenuOpen] = useState(false);\n//     const [cartCount, setCartCount] = useState(0);\n//     const navigate = useNavigate();\n//     const location = useLocation();\n\n//     useEffect(() => {\n//         // Update cart count when component mounts and when localStorage changes\n//         const updateCartCount = async () => {\n//             const username = localStorage.getItem('user');\n//             if (!username) {\n//                 setCartCount(0);\n//                 return;\n//             }\n\n//             try {\n//                 const response = await axios.get(`http://localhost:8080/getCartCount/${username}`);\n//                 setCartCount(response.data || 0);\n//             } catch (error) {\n//                 console.error('Error fetching cart count:', error);\n//                 setCartCount(0);\n//             }\n//         };\n\n//         updateCartCount();\n\n//         // Custom event for same-page cart updates\n//         window.addEventListener('cartUpdated', updateCartCount);\n\n//         return () => {\n//             window.removeEventListener('cartUpdated', updateCartCount);\n//         };\n//     }, []);\n\n//     const handleLogout = () => {\n//         const confirmed = window.confirm(\"Are you sure you want to logout?\");\n//         if (confirmed) {\n//             localStorage.removeItem('user');\n//             localStorage.removeItem('cart');\n//             sessionStorage.clear();\n//             navigate('/');\n//         }\n//     };\n\n//     const toggleMenu = () => {\n//         setIsMenuOpen(!isMenuOpen);\n//     };\n\n//     const isActive = (path) => {\n//         return location.pathname === path;\n//     };\n\n//     return (\n//         <nav className=\"customer-navbar\">\n//             <div className=\"navbar-container\">\n//                 <div className=\"navbar-logo\">\n//                     <span>🛒 <span className=\"logo-text\">Sales Savvy</span></span>\n//                     <span className=\"user-badge\">{localStorage.getItem('user')}</span>\n//                 </div>\n\n//                 <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>\n//                     <ul className=\"navbar-links\">\n//                         <li>\n//                             <span\n//                                 className={`nav-item ${isActive('/CustomerDashboard') ? 'active' : ''}`}\n//                                 onClick={() => {\n//                                     navigate('/CustomerDashboard');\n//                                     setIsMenuOpen(false);\n//                                 }}\n//                             >\n//                                 🏠 Home\n//                             </span>\n//                         </li>\n//                         <li>\n//                             <span\n//                                 className={`nav-item cart-item ${isActive('/cart') ? 'active' : ''}`}\n//                                 onClick={() => {\n//                                     navigate('/cart');\n//                                     setIsMenuOpen(false);\n//                                 }}\n//                             >\n//                                 🛒 Cart ({cartCount})\n//                             </span>\n//                         </li>\n//                         <li>\n//                             <span className=\"nav-item\">\n//                                 📦 My Orders\n//                             </span>\n//                         </li>\n//                         <li>\n//                             <button\n//                                 className=\"logout-btn\"\n//                                 onClick={handleLogout}\n//                             >\n//                                 🚪 Logout\n//                             </button>\n//                         </li>\n//                     </ul>\n//                 </div>\n\n//                 <div className=\"navbar-toggle\" onClick={toggleMenu}>\n//                     <span></span>\n//                     <span></span>\n//                     <span></span>\n//                 </div>\n//             </div>\n//         </nav>\n//     );\n// }\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaShoppingCart, FaHome, FaBox, FaSignOutAlt, FaBars } from 'react-icons/fa';\nimport './CustomerNavbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function CustomerNavbar() {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [cartCount, setCartCount] = useState(0);\n  const [username, setUsername] = useState('');\n  const navigate = useNavigate();\n  const location = useLocation();\n  useEffect(() => {\n    const storedUsername = localStorage.getItem('user');\n    setUsername(storedUsername || '');\n    const updateCartCount = async () => {\n      if (!storedUsername) {\n        setCartCount(0);\n        return;\n      }\n      try {\n        const response = await axios.get(`http://localhost:8080/getCartCount/${storedUsername}`);\n        setCartCount(response.data || 0);\n      } catch (error) {\n        console.error('Error fetching cart count:', error);\n        setCartCount(0);\n      }\n    };\n    updateCartCount();\n    const handleCartUpdate = () => updateCartCount();\n    window.addEventListener('cartUpdated', handleCartUpdate);\n    return () => {\n      window.removeEventListener('cartUpdated', handleCartUpdate);\n    };\n  }, []);\n  const handleLogout = () => {\n    const confirmed = window.confirm(\"Are you sure you want to logout?\");\n    if (confirmed) {\n      localStorage.removeItem('user');\n      localStorage.removeItem('cart');\n      sessionStorage.clear();\n      navigate('/');\n    }\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n    document.body.style.overflow = isMenuOpen ? 'auto' : 'hidden';\n  };\n  const navigateTo = path => {\n    navigate(path);\n    setIsMenuOpen(false);\n    document.body.style.overflow = 'auto';\n  };\n  const isActive = path => {\n    return location.pathname === path;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"customer-navbar\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"navbar-logo\",\n          onClick: () => navigateTo('/CustomerDashboard'),\n          role: \"button\",\n          tabIndex: 0,\n          onKeyDown: e => e.key === 'Enter' && navigateTo('/CustomerDashboard'),\n          children: [/*#__PURE__*/_jsxDEV(FaShoppingCart, {\n            className: \"logo-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"Sales Savvy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this), username && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-badge\",\n            children: username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 38\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `navbar-menu ${isMenuOpen ? 'active' : ''}`,\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"navbar-links\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `nav-item ${isActive('/CustomerDashboard') ? 'active' : ''}`,\n                onClick: () => navigateTo('/CustomerDashboard'),\n                children: [/*#__PURE__*/_jsxDEV(FaHome, {\n                  className: \"nav-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"nav-text\",\n                  children: \"Home\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `nav-item cart-item ${isActive('/cart') ? 'active' : ''}`,\n                onClick: () => navigateTo('/cart'),\n                children: [/*#__PURE__*/_jsxDEV(FaShoppingCart, {\n                  className: \"nav-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"nav-text\",\n                  children: [\"Cart \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"cart-count\",\n                    children: [\"(\", cartCount, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 69\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `nav-item ${isActive('/orders') ? 'active' : ''}`,\n                onClick: () => navigateTo('/orders'),\n                children: [/*#__PURE__*/_jsxDEV(FaBox, {\n                  className: \"nav-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"nav-text\",\n                  children: \"My Orders\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"logout-container\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"logout-btn\",\n                onClick: handleLogout,\n                children: [/*#__PURE__*/_jsxDEV(FaSignOutAlt, {\n                  className: \"nav-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"nav-text\",\n                  children: \"Logout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `navbar-toggle ${isMenuOpen ? 'active' : ''}`,\n          onClick: toggleMenu,\n          \"aria-label\": isMenuOpen ? \"Close menu\" : \"Open menu\",\n          children: /*#__PURE__*/_jsxDEV(FaBars, {\n            className: \"toggle-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 13\n    }, this), isMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-overlay\",\n      onClick: toggleMenu\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 28\n    }, this)]\n  }, void 0, true);\n}\n_s(CustomerNavbar, \"Lu4RaTJIbj1q7te7mAsw78JbHTk=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = CustomerNavbar;\nvar _c;\n$RefreshReg$(_c, \"CustomerNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "axios", "FaShoppingCart", "FaHome", "FaBox", "FaSignOutAlt", "FaBars", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CustomerNavbar", "_s", "isMenuOpen", "setIsMenuOpen", "cartCount", "setCartCount", "username", "setUsername", "navigate", "location", "storedUsername", "localStorage", "getItem", "updateCartCount", "response", "get", "data", "error", "console", "handleCartUpdate", "window", "addEventListener", "removeEventListener", "handleLogout", "confirmed", "confirm", "removeItem", "sessionStorage", "clear", "toggleMenu", "document", "body", "style", "overflow", "navigateTo", "path", "isActive", "pathname", "children", "className", "onClick", "role", "tabIndex", "onKeyDown", "e", "key", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/Navbar/CustomerNavbar.jsx"], "sourcesContent": ["// import React, { useState, useEffect } from 'react';\n// import { useNavigate, useLocation } from 'react-router-dom';\n// import axios from 'axios';\n// import './CustomerNavbar.css';\n\n// export default function CustomerNavbar() {\n//     const [isMenuOpen, setIsMenuOpen] = useState(false);\n//     const [cartCount, setCartCount] = useState(0);\n//     const navigate = useNavigate();\n//     const location = useLocation();\n\n//     useEffect(() => {\n//         // Update cart count when component mounts and when localStorage changes\n//         const updateCartCount = async () => {\n//             const username = localStorage.getItem('user');\n//             if (!username) {\n//                 setCartCount(0);\n//                 return;\n//             }\n\n//             try {\n//                 const response = await axios.get(`http://localhost:8080/getCartCount/${username}`);\n//                 setCartCount(response.data || 0);\n//             } catch (error) {\n//                 console.error('Error fetching cart count:', error);\n//                 setCartCount(0);\n//             }\n//         };\n\n//         updateCartCount();\n\n//         // Custom event for same-page cart updates\n//         window.addEventListener('cartUpdated', updateCartCount);\n\n//         return () => {\n//             window.removeEventListener('cartUpdated', updateCartCount);\n//         };\n//     }, []);\n\n//     const handleLogout = () => {\n//         const confirmed = window.confirm(\"Are you sure you want to logout?\");\n//         if (confirmed) {\n//             localStorage.removeItem('user');\n//             localStorage.removeItem('cart');\n//             sessionStorage.clear();\n//             navigate('/');\n//         }\n//     };\n\n//     const toggleMenu = () => {\n//         setIsMenuOpen(!isMenuOpen);\n//     };\n\n//     const isActive = (path) => {\n//         return location.pathname === path;\n//     };\n\n//     return (\n//         <nav className=\"customer-navbar\">\n//             <div className=\"navbar-container\">\n//                 <div className=\"navbar-logo\">\n//                     <span>🛒 <span className=\"logo-text\">Sales Savvy</span></span>\n//                     <span className=\"user-badge\">{localStorage.getItem('user')}</span>\n//                 </div>\n\n//                 <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>\n//                     <ul className=\"navbar-links\">\n//                         <li>\n//                             <span\n//                                 className={`nav-item ${isActive('/CustomerDashboard') ? 'active' : ''}`}\n//                                 onClick={() => {\n//                                     navigate('/CustomerDashboard');\n//                                     setIsMenuOpen(false);\n//                                 }}\n//                             >\n//                                 🏠 Home\n//                             </span>\n//                         </li>\n//                         <li>\n//                             <span\n//                                 className={`nav-item cart-item ${isActive('/cart') ? 'active' : ''}`}\n//                                 onClick={() => {\n//                                     navigate('/cart');\n//                                     setIsMenuOpen(false);\n//                                 }}\n//                             >\n//                                 🛒 Cart ({cartCount})\n//                             </span>\n//                         </li>\n//                         <li>\n//                             <span className=\"nav-item\">\n//                                 📦 My Orders\n//                             </span>\n//                         </li>\n//                         <li>\n//                             <button\n//                                 className=\"logout-btn\"\n//                                 onClick={handleLogout}\n//                             >\n//                                 🚪 Logout\n//                             </button>\n//                         </li>\n//                     </ul>\n//                 </div>\n\n//                 <div className=\"navbar-toggle\" onClick={toggleMenu}>\n//                     <span></span>\n//                     <span></span>\n//                     <span></span>\n//                 </div>\n//             </div>\n//         </nav>\n//     );\n// }\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from 'axios';\nimport { FaShoppingCart, FaHome, FaBox, FaSignOutAlt, FaBars } from 'react-icons/fa';\nimport './CustomerNavbar.css';\n\nexport default function CustomerNavbar() {\n    const [isMenuOpen, setIsMenuOpen] = useState(false);\n    const [cartCount, setCartCount] = useState(0);\n    const [username, setUsername] = useState('');\n    const navigate = useNavigate();\n    const location = useLocation();\n\n    useEffect(() => {\n        const storedUsername = localStorage.getItem('user');\n        setUsername(storedUsername || '');\n\n        const updateCartCount = async () => {\n            if (!storedUsername) {\n                setCartCount(0);\n                return;\n            }\n\n            try {\n                const response = await axios.get(`http://localhost:8080/getCartCount/${storedUsername}`);\n                setCartCount(response.data || 0);\n            } catch (error) {\n                console.error('Error fetching cart count:', error);\n                setCartCount(0);\n            }\n        };\n\n        updateCartCount();\n\n        const handleCartUpdate = () => updateCartCount();\n        window.addEventListener('cartUpdated', handleCartUpdate);\n\n        return () => {\n            window.removeEventListener('cartUpdated', handleCartUpdate);\n        };\n    }, []);\n\n    const handleLogout = () => {\n        const confirmed = window.confirm(\"Are you sure you want to logout?\");\n        if (confirmed) {\n            localStorage.removeItem('user');\n            localStorage.removeItem('cart');\n            sessionStorage.clear();\n            navigate('/');\n        }\n    };\n\n    const toggleMenu = () => {\n        setIsMenuOpen(!isMenuOpen);\n        document.body.style.overflow = isMenuOpen ? 'auto' : 'hidden';\n    };\n\n    const navigateTo = (path) => {\n        navigate(path);\n        setIsMenuOpen(false);\n        document.body.style.overflow = 'auto';\n    };\n\n    const isActive = (path) => {\n        return location.pathname === path;\n    };\n\n    return (\n        <>\n            <nav className=\"customer-navbar\">\n                <div className=\"navbar-container\">\n                    <div \n                        className=\"navbar-logo\" \n                        onClick={() => navigateTo('/CustomerDashboard')}\n                        role=\"button\"\n                        tabIndex={0}\n                        onKeyDown={(e) => e.key === 'Enter' && navigateTo('/CustomerDashboard')}\n                    >\n                        <FaShoppingCart className=\"logo-icon\" />\n                        <span className=\"logo-text\">Sales Savvy</span>\n                        {username && <span className=\"user-badge\">{username}</span>}\n                    </div>\n\n                    <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>\n                        <ul className=\"navbar-links\">\n                            <li>\n                                <button\n                                    className={`nav-item ${isActive('/CustomerDashboard') ? 'active' : ''}`}\n                                    onClick={() => navigateTo('/CustomerDashboard')}\n                                >\n                                    <FaHome className=\"nav-icon\" />\n                                    <span className=\"nav-text\">Home</span>\n                                </button>\n                            </li>\n                            <li>\n                                <button\n                                    className={`nav-item cart-item ${isActive('/cart') ? 'active' : ''}`}\n                                    onClick={() => navigateTo('/cart')}\n                                >\n                                    <FaShoppingCart className=\"nav-icon\" />\n                                    <span className=\"nav-text\">Cart <span className=\"cart-count\">({cartCount})</span></span>\n                                </button>\n                            </li>\n                            <li>\n                                <button\n                                    className={`nav-item ${isActive('/orders') ? 'active' : ''}`}\n                                    onClick={() => navigateTo('/orders')}\n                                >\n                                    <FaBox className=\"nav-icon\" />\n                                    <span className=\"nav-text\">My Orders</span>\n                                </button>\n                            </li>\n                            <li className=\"logout-container\">\n                                <button\n                                    className=\"logout-btn\"\n                                    onClick={handleLogout}\n                                >\n                                    <FaSignOutAlt className=\"nav-icon\" />\n                                    <span className=\"nav-text\">Logout</span>\n                                </button>\n                            </li>\n                        </ul>\n                    </div>\n\n                    <button \n                        className={`navbar-toggle ${isMenuOpen ? 'active' : ''}`}\n                        onClick={toggleMenu}\n                        aria-label={isMenuOpen ? \"Close menu\" : \"Open menu\"}\n                    >\n                        <FaBars className=\"toggle-icon\" />\n                    </button>\n                </div>\n            </nav>\n            {isMenuOpen && <div className=\"navbar-overlay\" onClick={toggleMenu} />}\n        </>\n    );\n}"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,EAAEC,MAAM,EAAEC,KAAK,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AACpF,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACrC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAMsB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACZ,MAAMuB,cAAc,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IACnDL,WAAW,CAACG,cAAc,IAAI,EAAE,CAAC;IAEjC,MAAMG,eAAe,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI,CAACH,cAAc,EAAE;QACjBL,YAAY,CAAC,CAAC,CAAC;QACf;MACJ;MAEA,IAAI;QACA,MAAMS,QAAQ,GAAG,MAAMxB,KAAK,CAACyB,GAAG,CAAC,sCAAsCL,cAAc,EAAE,CAAC;QACxFL,YAAY,CAACS,QAAQ,CAACE,IAAI,IAAI,CAAC,CAAC;MACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDZ,YAAY,CAAC,CAAC,CAAC;MACnB;IACJ,CAAC;IAEDQ,eAAe,CAAC,CAAC;IAEjB,MAAMM,gBAAgB,GAAGA,CAAA,KAAMN,eAAe,CAAC,CAAC;IAChDO,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEF,gBAAgB,CAAC;IAExD,OAAO,MAAM;MACTC,MAAM,CAACE,mBAAmB,CAAC,aAAa,EAAEH,gBAAgB,CAAC;IAC/D,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAGJ,MAAM,CAACK,OAAO,CAAC,kCAAkC,CAAC;IACpE,IAAID,SAAS,EAAE;MACXb,YAAY,CAACe,UAAU,CAAC,MAAM,CAAC;MAC/Bf,YAAY,CAACe,UAAU,CAAC,MAAM,CAAC;MAC/BC,cAAc,CAACC,KAAK,CAAC,CAAC;MACtBpB,QAAQ,CAAC,GAAG,CAAC;IACjB;EACJ,CAAC;EAED,MAAMqB,UAAU,GAAGA,CAAA,KAAM;IACrB1B,aAAa,CAAC,CAACD,UAAU,CAAC;IAC1B4B,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG/B,UAAU,GAAG,MAAM,GAAG,QAAQ;EACjE,CAAC;EAED,MAAMgC,UAAU,GAAIC,IAAI,IAAK;IACzB3B,QAAQ,CAAC2B,IAAI,CAAC;IACdhC,aAAa,CAAC,KAAK,CAAC;IACpB2B,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,MAAM;EACzC,CAAC;EAED,MAAMG,QAAQ,GAAID,IAAI,IAAK;IACvB,OAAO1B,QAAQ,CAAC4B,QAAQ,KAAKF,IAAI;EACrC,CAAC;EAED,oBACItC,OAAA,CAAAE,SAAA;IAAAuC,QAAA,gBACIzC,OAAA;MAAK0C,SAAS,EAAC,iBAAiB;MAAAD,QAAA,eAC5BzC,OAAA;QAAK0C,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC7BzC,OAAA;UACI0C,SAAS,EAAC,aAAa;UACvBC,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oBAAoB,CAAE;UAChDO,IAAI,EAAC,QAAQ;UACbC,QAAQ,EAAE,CAAE;UACZC,SAAS,EAAGC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAIX,UAAU,CAAC,oBAAoB,CAAE;UAAAI,QAAA,gBAExEzC,OAAA,CAACN,cAAc;YAACgD,SAAS,EAAC;UAAW;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxCpD,OAAA;YAAM0C,SAAS,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAW;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC7C3C,QAAQ,iBAAIT,OAAA;YAAM0C,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAEhC;UAAQ;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAENpD,OAAA;UAAK0C,SAAS,EAAE,eAAerC,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UAAAoC,QAAA,eACxDzC,OAAA;YAAI0C,SAAS,EAAC,cAAc;YAAAD,QAAA,gBACxBzC,OAAA;cAAAyC,QAAA,eACIzC,OAAA;gBACI0C,SAAS,EAAE,YAAYH,QAAQ,CAAC,oBAAoB,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACxEI,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,oBAAoB,CAAE;gBAAAI,QAAA,gBAEhDzC,OAAA,CAACL,MAAM;kBAAC+C,SAAS,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/BpD,OAAA;kBAAM0C,SAAS,EAAC,UAAU;kBAAAD,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACLpD,OAAA;cAAAyC,QAAA,eACIzC,OAAA;gBACI0C,SAAS,EAAE,sBAAsBH,QAAQ,CAAC,OAAO,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACrEI,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,OAAO,CAAE;gBAAAI,QAAA,gBAEnCzC,OAAA,CAACN,cAAc;kBAACgD,SAAS,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCpD,OAAA;kBAAM0C,SAAS,EAAC,UAAU;kBAAAD,QAAA,GAAC,OAAK,eAAAzC,OAAA;oBAAM0C,SAAS,EAAC,YAAY;oBAAAD,QAAA,GAAC,GAAC,EAAClC,SAAS,EAAC,GAAC;kBAAA;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACLpD,OAAA;cAAAyC,QAAA,eACIzC,OAAA;gBACI0C,SAAS,EAAE,YAAYH,QAAQ,CAAC,SAAS,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC7DI,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,SAAS,CAAE;gBAAAI,QAAA,gBAErCzC,OAAA,CAACJ,KAAK;kBAAC8C,SAAS,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9BpD,OAAA;kBAAM0C,SAAS,EAAC,UAAU;kBAAAD,QAAA,EAAC;gBAAS;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACLpD,OAAA;cAAI0C,SAAS,EAAC,kBAAkB;cAAAD,QAAA,eAC5BzC,OAAA;gBACI0C,SAAS,EAAC,YAAY;gBACtBC,OAAO,EAAEjB,YAAa;gBAAAe,QAAA,gBAEtBzC,OAAA,CAACH,YAAY;kBAAC6C,SAAS,EAAC;gBAAU;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCpD,OAAA;kBAAM0C,SAAS,EAAC,UAAU;kBAAAD,QAAA,EAAC;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENpD,OAAA;UACI0C,SAAS,EAAE,iBAAiBrC,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;UACzDsC,OAAO,EAAEX,UAAW;UACpB,cAAY3B,UAAU,GAAG,YAAY,GAAG,WAAY;UAAAoC,QAAA,eAEpDzC,OAAA,CAACF,MAAM;YAAC4C,SAAS,EAAC;UAAa;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EACL/C,UAAU,iBAAIL,OAAA;MAAK0C,SAAS,EAAC,gBAAgB;MAACC,OAAO,EAAEX;IAAW;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACxE,CAAC;AAEX;AAAChD,EAAA,CAlIuBD,cAAc;EAAA,QAIjBZ,WAAW,EACXC,WAAW;AAAA;AAAA6D,EAAA,GALRlD,cAAc;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}