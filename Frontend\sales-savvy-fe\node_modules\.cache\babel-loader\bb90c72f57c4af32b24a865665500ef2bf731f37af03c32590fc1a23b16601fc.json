{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\shared\\\\ProductsList.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { triggerCartUpdate, getUsername, isLoggedIn, handleApiError, showSuccessMessage } from './CartUtils';\nimport './ProductsList.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function ProductsList({\n  userRole = 'customer',\n  showActions = false\n}) {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const fetchProducts = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get('http://localhost:8080/getAllProducts');\n      setProducts(response.data || []);\n      setLoading(false);\n    } catch (error) {\n      console.error('Error fetching products:', error);\n      handleApiError(error, 'Failed to load products. Please try again.');\n      setProducts([]);\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const handleDelete = async id => {\n    if (userRole !== 'admin') return;\n    const confirmed = window.confirm(\"Are you sure you want to delete this product?\");\n    if (!confirmed) return;\n    try {\n      await axios.delete('http://localhost:8080/deleteProduct', {\n        params: {\n          id\n        }\n      });\n      fetchProducts();\n      showSuccessMessage('Product deleted successfully!');\n    } catch (error) {\n      console.error('Delete failed:', error);\n      handleApiError(error, 'Failed to delete product. Please try again.');\n    }\n  };\n  const handleUpdate = product => {\n    if (userRole !== 'admin') {\n      showSuccessMessage('Access denied. Admin privileges required.');\n      return;\n    }\n    navigate('/updateproduct', {\n      state: {\n        product\n      }\n    });\n  };\n  const handleAddToCart = async product => {\n    if (userRole !== 'customer') return;\n    if (!isLoggedIn()) {\n      showSuccessMessage('Please login to add items to cart');\n      return;\n    }\n    const username = getUsername();\n    try {\n      const cartItem = {\n        username: username,\n        productId: product.id,\n        quantity: 1\n      };\n      const response = await axios.post('http://localhost:8080/addToCart', cartItem);\n      if (response.data === 'cart added') {\n        showSuccessMessage(`Added ${product.name} to cart!`);\n        triggerCartUpdate();\n      } else {\n        showSuccessMessage('Failed to add item to cart: ' + response.data);\n      }\n    } catch (error) {\n      handleApiError(error, 'Failed to add item to cart. Please try again.');\n    }\n  };\n  const handleBuyNow = product => {\n    if (userRole !== 'customer') {\n      showSuccessMessage('This feature is only available for customers.');\n      return;\n    }\n    if (!isLoggedIn()) {\n      showSuccessMessage('Please login to purchase products.');\n      return;\n    }\n\n    // For now, add to cart and navigate to cart\n    handleAddToCart(product);\n    setTimeout(() => {\n      navigate('/cart');\n    }, 1000);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"products-list-container\",\n    children: [userRole === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"All Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Manage your product inventory\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 17\n    }, this), userRole === 'customer' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header customer-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"page-header-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Our Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Discover amazing products at great prices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content-area\",\n      children: products.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-products\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"No products found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 25\n        }, this), userRole === 'admin' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Start by adding some products to your inventory.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"add-product-btn\",\n            onClick: () => navigate('/AddProducts'),\n            children: \"Add Your First Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Check back later for new products!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"products-container\",\n        children: [userRole === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Products (\", products.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"add-product-btn\",\n            onClick: () => navigate('/AddProducts'),\n            children: \"+ Add New Product\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 25\n        }, this), userRole === 'customer' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"products-count\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [products.length, \" Products Available\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 25\n        }, this), userRole === 'admin' ?\n        /*#__PURE__*/\n        // Admin Table View\n        _jsxDEV(\"div\", {\n          className: \"table-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"products-table\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Price\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-id\",\n                  children: [\"#\", product.id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-image\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: product.image,\n                    alt: product.name,\n                    onError: e => {\n                      e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-name\",\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-description\",\n                  children: product.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-price\",\n                  children: [\"\\u20B9\", product.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"product-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"update-btn\",\n                    onClick: () => handleUpdate(product),\n                    title: \"Update Product\",\n                    children: \"\\u270F\\uFE0F Update\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"delete-btn\",\n                    onClick: () => handleDelete(product.id),\n                    title: \"Delete Product\",\n                    children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 45\n                }, this)]\n              }, product.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 25\n        }, this) :\n        /*#__PURE__*/\n        // Customer Card View\n        _jsxDEV(\"div\", {\n          className: \"products-grid\",\n          children: products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-image-container\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: product.image,\n                alt: product.name,\n                onError: e => {\n                  e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"product-title\",\n                children: product.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"product-desc\",\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-price-tag\",\n                children: [\"\\u20B9\", product.price]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"product-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"cart-btn\",\n                  onClick: () => handleAddToCart(product),\n                  children: \"\\uD83D\\uDED2 Add to Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"buy-btn\",\n                  onClick: () => handleBuyNow(product),\n                  children: \"\\uD83D\\uDCB3 Buy Now\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 37\n            }, this)]\n          }, product.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 112,\n    columnNumber: 9\n  }, this);\n}\n_s(ProductsList, \"Ipduiw1a7ftVVJlTipoeXPWOzWQ=\", false, function () {\n  return [useNavigate];\n});\n_c = ProductsList;\nvar _c;\n$RefreshReg$(_c, \"ProductsList\");", "map": {"version": 3, "names": ["useEffect", "useState", "axios", "useNavigate", "triggerCartUpdate", "getUsername", "isLoggedIn", "handleApiError", "showSuccessMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductsList", "userRole", "showActions", "_s", "products", "setProducts", "loading", "setLoading", "navigate", "fetchProducts", "response", "get", "data", "error", "console", "handleDelete", "id", "confirmed", "window", "confirm", "delete", "params", "handleUpdate", "product", "state", "handleAddToCart", "username", "cartItem", "productId", "quantity", "post", "name", "handleBuyNow", "setTimeout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "map", "src", "image", "alt", "onError", "e", "target", "description", "price", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/shared/ProductsList.jsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { triggerCartUpdate, getUsername, isLoggedIn, handleApiError, showSuccessMessage } from './CartUtils';\nimport './ProductsList.css';\n\nexport default function ProductsList({ userRole = 'customer', showActions = false }) {\n    const [products, setProducts] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const navigate = useNavigate();\n\n    const fetchProducts = async () => {\n        setLoading(true);\n        try {\n            const response = await axios.get('http://localhost:8080/getAllProducts');\n            setProducts(response.data || []);\n            setLoading(false);\n        } catch (error) {\n            console.error('Error fetching products:', error);\n            handleApiError(error, 'Failed to load products. Please try again.');\n            setProducts([]);\n            setLoading(false);\n        }\n    };\n\n    useEffect(() => {\n        fetchProducts();\n    }, []);\n\n    const handleDelete = async (id) => {\n        if (userRole !== 'admin') return;\n\n        const confirmed = window.confirm(\"Are you sure you want to delete this product?\");\n        if (!confirmed) return;\n\n        try {\n            await axios.delete('http://localhost:8080/deleteProduct', { params: { id } });\n            fetchProducts();\n            showSuccessMessage('Product deleted successfully!');\n        } catch (error) {\n            console.error('Delete failed:', error);\n            handleApiError(error, 'Failed to delete product. Please try again.');\n        }\n    };\n\n    const handleUpdate = (product) => {\n        if (userRole !== 'admin') {\n            showSuccessMessage('Access denied. Admin privileges required.');\n            return;\n        }\n        navigate('/updateproduct', { state: { product } });\n    };\n\n    const handleAddToCart = async (product) => {\n        if (userRole !== 'customer') return;\n\n        if (!isLoggedIn()) {\n            showSuccessMessage('Please login to add items to cart');\n            return;\n        }\n\n        const username = getUsername();\n\n        try {\n            const cartItem = {\n                username: username,\n                productId: product.id,\n                quantity: 1\n            };\n\n            const response = await axios.post('http://localhost:8080/addToCart', cartItem);\n\n            if (response.data === 'cart added') {\n                showSuccessMessage(`Added ${product.name} to cart!`);\n                triggerCartUpdate();\n            } else {\n                showSuccessMessage('Failed to add item to cart: ' + response.data);\n            }\n        } catch (error) {\n            handleApiError(error, 'Failed to add item to cart. Please try again.');\n        }\n    };\n\n    const handleBuyNow = (product) => {\n        if (userRole !== 'customer') {\n            showSuccessMessage('This feature is only available for customers.');\n            return;\n        }\n\n        if (!isLoggedIn()) {\n            showSuccessMessage('Please login to purchase products.');\n            return;\n        }\n\n        // For now, add to cart and navigate to cart\n        handleAddToCart(product);\n        setTimeout(() => {\n            navigate('/cart');\n        }, 1000);\n    };\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"loading-spinner\"></div>\n                <p>Loading products...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"products-list-container\">\n            {userRole === 'admin' && (\n                <div className=\"page-header\">\n                    <div className=\"page-header-content\">\n                        <h1>All Products</h1>\n                        <p>Manage your product inventory</p>\n                    </div>\n                </div>\n            )}\n\n            {userRole === 'customer' && (\n                <div className=\"page-header customer-header\">\n                    <div className=\"page-header-content\">\n                        <h1>Our Products</h1>\n                        <p>Discover amazing products at great prices</p>\n                    </div>\n                </div>\n            )}\n\n            <div className=\"main-content-area\">\n                {products.length === 0 ? (\n                    <div className=\"no-products\">\n                        <h3>No products found</h3>\n                        {userRole === 'admin' ? (\n                            <>\n                                <p>Start by adding some products to your inventory.</p>\n                                <button\n                                    className=\"add-product-btn\"\n                                    onClick={() => navigate('/AddProducts')}\n                                >\n                                    Add Your First Product\n                                </button>\n                            </>\n                        ) : (\n                            <p>Check back later for new products!</p>\n                        )}\n                    </div>\n                ) : (\n                    <div className=\"products-container\">\n                    {userRole === 'admin' && (\n                        <div className=\"table-header\">\n                            <h3>Products ({products.length})</h3>\n                            <button \n                                className=\"add-product-btn\"\n                                onClick={() => navigate('/AddProducts')}\n                            >\n                                + Add New Product\n                            </button>\n                        </div>\n                    )}\n                    \n                    {userRole === 'customer' && (\n                        <div className=\"products-count\">\n                            <h3>{products.length} Products Available</h3>\n                        </div>\n                    )}\n                    \n                    {userRole === 'admin' ? (\n                        // Admin Table View\n                        <div className=\"table-wrapper\">\n                            <table className=\"products-table\">\n                                <thead>\n                                    <tr>\n                                        <th>ID</th>\n                                        <th>Image</th>\n                                        <th>Name</th>\n                                        <th>Description</th>\n                                        <th>Price</th>\n                                        <th>Actions</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {products.map(product => (\n                                        <tr key={product.id}>\n                                            <td className=\"product-id\">#{product.id}</td>\n                                            <td className=\"product-image\">\n                                                <img \n                                                    src={product.image} \n                                                    alt={product.name}\n                                                    onError={(e) => {\n                                                        e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';\n                                                    }}\n                                                />\n                                            </td>\n                                            <td className=\"product-name\">\n                                                <strong>{product.name}</strong>\n                                            </td>\n                                            <td className=\"product-description\">\n                                                {product.description}\n                                            </td>\n                                            <td className=\"product-price\">\n                                                ₹{product.price}\n                                            </td>\n                                            <td className=\"product-actions\">\n                                                <button \n                                                    className=\"update-btn\"\n                                                    onClick={() => handleUpdate(product)}\n                                                    title=\"Update Product\"\n                                                >\n                                                    ✏️ Update\n                                                </button>\n                                                <button \n                                                    className=\"delete-btn\"\n                                                    onClick={() => handleDelete(product.id)}\n                                                    title=\"Delete Product\"\n                                                >\n                                                    🗑️ Delete\n                                                </button>\n                                            </td>\n                                        </tr>\n                                    ))}\n                                </tbody>\n                            </table>\n                        </div>\n                    ) : (\n                        // Customer Card View\n                        <div className=\"products-grid\">\n                            {products.map(product => (\n                                <div key={product.id} className=\"product-card\">\n                                    <div className=\"product-image-container\">\n                                        <img \n                                            src={product.image} \n                                            alt={product.name}\n                                            onError={(e) => {\n                                                e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';\n                                            }}\n                                        />\n                                    </div>\n                                    <div className=\"product-info\">\n                                        <h3 className=\"product-title\">{product.name}</h3>\n                                        <p className=\"product-desc\">{product.description}</p>\n                                        <div className=\"product-price-tag\">₹{product.price}</div>\n                                        <div className=\"product-buttons\">\n                                            <button \n                                                className=\"cart-btn\"\n                                                onClick={() => handleAddToCart(product)}\n                                            >\n                                                🛒 Add to Cart\n                                            </button>\n                                            <button \n                                                className=\"buy-btn\"\n                                                onClick={() => handleBuyNow(product)}\n                                            >\n                                                💳 Buy Now\n                                            </button>\n                                        </div>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    )}\n                    </div>\n                )}\n            </div>\n        </div>\n    );\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,cAAc,EAAEC,kBAAkB,QAAQ,aAAa;AAC5G,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5B,eAAe,SAASC,YAAYA,CAAC;EAAEC,QAAQ,GAAG,UAAU;EAAEC,WAAW,GAAG;AAAM,CAAC,EAAE;EAAAC,EAAA;EACjF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMoB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9B,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9BF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAMG,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,GAAG,CAAC,sCAAsC,CAAC;MACxEN,WAAW,CAACK,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;MAChCL,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDnB,cAAc,CAACmB,KAAK,EAAE,4CAA4C,CAAC;MACnER,WAAW,CAAC,EAAE,CAAC;MACfE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAEDpB,SAAS,CAAC,MAAM;IACZsB,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,YAAY,GAAG,MAAOC,EAAE,IAAK;IAC/B,IAAIf,QAAQ,KAAK,OAAO,EAAE;IAE1B,MAAMgB,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC;IACjF,IAAI,CAACF,SAAS,EAAE;IAEhB,IAAI;MACA,MAAM5B,KAAK,CAAC+B,MAAM,CAAC,qCAAqC,EAAE;QAAEC,MAAM,EAAE;UAAEL;QAAG;MAAE,CAAC,CAAC;MAC7EP,aAAa,CAAC,CAAC;MACfd,kBAAkB,CAAC,+BAA+B,CAAC;IACvD,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCnB,cAAc,CAACmB,KAAK,EAAE,6CAA6C,CAAC;IACxE;EACJ,CAAC;EAED,MAAMS,YAAY,GAAIC,OAAO,IAAK;IAC9B,IAAItB,QAAQ,KAAK,OAAO,EAAE;MACtBN,kBAAkB,CAAC,2CAA2C,CAAC;MAC/D;IACJ;IACAa,QAAQ,CAAC,gBAAgB,EAAE;MAAEgB,KAAK,EAAE;QAAED;MAAQ;IAAE,CAAC,CAAC;EACtD,CAAC;EAED,MAAME,eAAe,GAAG,MAAOF,OAAO,IAAK;IACvC,IAAItB,QAAQ,KAAK,UAAU,EAAE;IAE7B,IAAI,CAACR,UAAU,CAAC,CAAC,EAAE;MACfE,kBAAkB,CAAC,mCAAmC,CAAC;MACvD;IACJ;IAEA,MAAM+B,QAAQ,GAAGlC,WAAW,CAAC,CAAC;IAE9B,IAAI;MACA,MAAMmC,QAAQ,GAAG;QACbD,QAAQ,EAAEA,QAAQ;QAClBE,SAAS,EAAEL,OAAO,CAACP,EAAE;QACrBa,QAAQ,EAAE;MACd,CAAC;MAED,MAAMnB,QAAQ,GAAG,MAAMrB,KAAK,CAACyC,IAAI,CAAC,iCAAiC,EAAEH,QAAQ,CAAC;MAE9E,IAAIjB,QAAQ,CAACE,IAAI,KAAK,YAAY,EAAE;QAChCjB,kBAAkB,CAAC,SAAS4B,OAAO,CAACQ,IAAI,WAAW,CAAC;QACpDxC,iBAAiB,CAAC,CAAC;MACvB,CAAC,MAAM;QACHI,kBAAkB,CAAC,8BAA8B,GAAGe,QAAQ,CAACE,IAAI,CAAC;MACtE;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZnB,cAAc,CAACmB,KAAK,EAAE,+CAA+C,CAAC;IAC1E;EACJ,CAAC;EAED,MAAMmB,YAAY,GAAIT,OAAO,IAAK;IAC9B,IAAItB,QAAQ,KAAK,UAAU,EAAE;MACzBN,kBAAkB,CAAC,+CAA+C,CAAC;MACnE;IACJ;IAEA,IAAI,CAACF,UAAU,CAAC,CAAC,EAAE;MACfE,kBAAkB,CAAC,oCAAoC,CAAC;MACxD;IACJ;;IAEA;IACA8B,eAAe,CAACF,OAAO,CAAC;IACxBU,UAAU,CAAC,MAAM;MACbzB,QAAQ,CAAC,OAAO,CAAC;IACrB,CAAC,EAAE,IAAI,CAAC;EACZ,CAAC;EAED,IAAIF,OAAO,EAAE;IACT,oBACIT,OAAA;MAAKqC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BtC,OAAA;QAAKqC,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC1C,OAAA;QAAAsC,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEd;EAEA,oBACI1C,OAAA;IAAKqC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,GACnClC,QAAQ,KAAK,OAAO,iBACjBJ,OAAA;MAAKqC,SAAS,EAAC,aAAa;MAAAC,QAAA,eACxBtC,OAAA;QAAKqC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCtC,OAAA;UAAAsC,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrB1C,OAAA;UAAAsC,QAAA,EAAG;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEAtC,QAAQ,KAAK,UAAU,iBACpBJ,OAAA;MAAKqC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eACxCtC,OAAA;QAAKqC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAChCtC,OAAA;UAAAsC,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrB1C,OAAA;UAAAsC,QAAA,EAAG;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,eAED1C,OAAA;MAAKqC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC7B/B,QAAQ,CAACoC,MAAM,KAAK,CAAC,gBAClB3C,OAAA;QAAKqC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxBtC,OAAA;UAAAsC,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACzBtC,QAAQ,KAAK,OAAO,gBACjBJ,OAAA,CAAAE,SAAA;UAAAoC,QAAA,gBACItC,OAAA;YAAAsC,QAAA,EAAG;UAAgD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvD1C,OAAA;YACIqC,SAAS,EAAC,iBAAiB;YAC3BO,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,cAAc,CAAE;YAAA2B,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACX,CAAC,gBAEH1C,OAAA;UAAAsC,QAAA,EAAG;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAC3C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,gBAEN1C,OAAA;QAAKqC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,GAClClC,QAAQ,KAAK,OAAO,iBACjBJ,OAAA;UAAKqC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBtC,OAAA;YAAAsC,QAAA,GAAI,YAAU,EAAC/B,QAAQ,CAACoC,MAAM,EAAC,GAAC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrC1C,OAAA;YACIqC,SAAS,EAAC,iBAAiB;YAC3BO,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,cAAc,CAAE;YAAA2B,QAAA,EAC3C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR,EAEAtC,QAAQ,KAAK,UAAU,iBACpBJ,OAAA;UAAKqC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC3BtC,OAAA;YAAAsC,QAAA,GAAK/B,QAAQ,CAACoC,MAAM,EAAC,qBAAmB;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACR,EAEAtC,QAAQ,KAAK,OAAO;QAAA;QACjB;QACAJ,OAAA;UAAKqC,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC1BtC,OAAA;YAAOqC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BtC,OAAA;cAAAsC,QAAA,eACItC,OAAA;gBAAAsC,QAAA,gBACItC,OAAA;kBAAAsC,QAAA,EAAI;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACX1C,OAAA;kBAAAsC,QAAA,EAAI;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd1C,OAAA;kBAAAsC,QAAA,EAAI;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACb1C,OAAA;kBAAAsC,QAAA,EAAI;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpB1C,OAAA;kBAAAsC,QAAA,EAAI;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACd1C,OAAA;kBAAAsC,QAAA,EAAI;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACR1C,OAAA;cAAAsC,QAAA,EACK/B,QAAQ,CAACsC,GAAG,CAACnB,OAAO,iBACjB1B,OAAA;gBAAAsC,QAAA,gBACItC,OAAA;kBAAIqC,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,GAAC,EAACZ,OAAO,CAACP,EAAE;gBAAA;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7C1C,OAAA;kBAAIqC,SAAS,EAAC,eAAe;kBAAAC,QAAA,eACzBtC,OAAA;oBACI8C,GAAG,EAAEpB,OAAO,CAACqB,KAAM;oBACnBC,GAAG,EAAEtB,OAAO,CAACQ,IAAK;oBAClBe,OAAO,EAAGC,CAAC,IAAK;sBACZA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,mDAAmD;oBACtE;kBAAE;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACL1C,OAAA;kBAAIqC,SAAS,EAAC,cAAc;kBAAAC,QAAA,eACxBtC,OAAA;oBAAAsC,QAAA,EAASZ,OAAO,CAACQ;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACL1C,OAAA;kBAAIqC,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAC9BZ,OAAO,CAAC0B;gBAAW;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACL1C,OAAA;kBAAIqC,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAAC,QACzB,EAACZ,OAAO,CAAC2B,KAAK;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACL1C,OAAA;kBAAIqC,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC3BtC,OAAA;oBACIqC,SAAS,EAAC,YAAY;oBACtBO,OAAO,EAAEA,CAAA,KAAMnB,YAAY,CAACC,OAAO,CAAE;oBACrC4B,KAAK,EAAC,gBAAgB;oBAAAhB,QAAA,EACzB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT1C,OAAA;oBACIqC,SAAS,EAAC,YAAY;oBACtBO,OAAO,EAAEA,CAAA,KAAM1B,YAAY,CAACQ,OAAO,CAACP,EAAE,CAAE;oBACxCmC,KAAK,EAAC,gBAAgB;oBAAAhB,QAAA,EACzB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA,GAnCAhB,OAAO,CAACP,EAAE;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoCf,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;QAAA;QAEN;QACA1C,OAAA;UAAKqC,SAAS,EAAC,eAAe;UAAAC,QAAA,EACzB/B,QAAQ,CAACsC,GAAG,CAACnB,OAAO,iBACjB1B,OAAA;YAAsBqC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC1CtC,OAAA;cAAKqC,SAAS,EAAC,yBAAyB;cAAAC,QAAA,eACpCtC,OAAA;gBACI8C,GAAG,EAAEpB,OAAO,CAACqB,KAAM;gBACnBC,GAAG,EAAEtB,OAAO,CAACQ,IAAK;gBAClBe,OAAO,EAAGC,CAAC,IAAK;kBACZA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,mDAAmD;gBACtE;cAAE;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBtC,OAAA;gBAAIqC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEZ,OAAO,CAACQ;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjD1C,OAAA;gBAAGqC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEZ,OAAO,CAAC0B;cAAW;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrD1C,OAAA;gBAAKqC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,GAAC,QAAC,EAACZ,OAAO,CAAC2B,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzD1C,OAAA;gBAAKqC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC5BtC,OAAA;kBACIqC,SAAS,EAAC,UAAU;kBACpBO,OAAO,EAAEA,CAAA,KAAMhB,eAAe,CAACF,OAAO,CAAE;kBAAAY,QAAA,EAC3C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1C,OAAA;kBACIqC,SAAS,EAAC,SAAS;kBACnBO,OAAO,EAAEA,CAAA,KAAMT,YAAY,CAACT,OAAO,CAAE;kBAAAY,QAAA,EACxC;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA,GA5BAhB,OAAO,CAACP,EAAE;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6Bf,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACpC,EAAA,CArQuBH,YAAY;EAAA,QAGfV,WAAW;AAAA;AAAA8D,EAAA,GAHRpD,YAAY;AAAA,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}