{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\Navbar\\\\CustomerNavbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from 'axios';\nimport './CustomerNavbar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CustomerNavbar() {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [cartCount, setCartCount] = useState(0);\n  const navigate = useNavigate();\n  useEffect(() => {\n    // Update cart count when component mounts and when localStorage changes\n    const updateCartCount = async () => {\n      const username = localStorage.getItem('user');\n      if (!username) {\n        setCartCount(0);\n        return;\n      }\n      try {\n        const response = await axios.get(`http://localhost:8080/getCartCount/${username}`);\n        setCartCount(response.data || 0);\n      } catch (error) {\n        console.error('Error fetching cart count:', error);\n        setCartCount(0);\n      }\n    };\n    updateCartCount();\n\n    // Custom event for same-page cart updates\n    window.addEventListener('cartUpdated', updateCartCount);\n    return () => {\n      window.removeEventListener('cartUpdated', updateCartCount);\n    };\n  }, []);\n  const handleLogout = () => {\n    const confirmed = window.confirm(\"Are you sure you want to logout?\");\n    if (confirmed) {\n      localStorage.removeItem('user');\n      localStorage.removeItem('cart');\n      sessionStorage.clear();\n      navigate('/');\n    }\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"customer-navbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-logo\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\uD83D\\uDED2 \", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: \"Sales Savvy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 30\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"user-badge\",\n          children: localStorage.getItem('user')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `navbar-menu ${isMenuOpen ? 'active' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"navbar-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-item\",\n              onClick: () => navigate('/CustomerDashboard'),\n              children: \"\\uD83C\\uDFE0 Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-item cart-item\",\n              onClick: () => navigate('/cart'),\n              children: [\"\\uD83D\\uDED2 Cart (\", cartCount, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-item\",\n              children: \"\\uD83D\\uDCE6 My Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"logout-btn\",\n              onClick: handleLogout,\n              children: \"\\uD83D\\uDEAA Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"navbar-toggle\",\n        onClick: toggleMenu,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 9\n  }, this);\n}\n_s(CustomerNavbar, \"Sav86P+VcZZ+WSIiN1aGBu7F9z8=\", false, function () {\n  return [useNavigate];\n});\n_c = CustomerNavbar;\nvar _c;\n$RefreshReg$(_c, \"CustomerNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "axios", "jsxDEV", "_jsxDEV", "CustomerNavbar", "_s", "isMenuOpen", "setIsMenuOpen", "cartCount", "setCartCount", "navigate", "updateCartCount", "username", "localStorage", "getItem", "response", "get", "data", "error", "console", "window", "addEventListener", "removeEventListener", "handleLogout", "confirmed", "confirm", "removeItem", "sessionStorage", "clear", "toggleMenu", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/Navbar/CustomerNavbar.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from 'axios';\nimport './CustomerNavbar.css';\n\nexport default function CustomerNavbar() {\n    const [isMenuOpen, setIsMenuOpen] = useState(false);\n    const [cartCount, setCartCount] = useState(0);\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        // Update cart count when component mounts and when localStorage changes\n        const updateCartCount = async () => {\n            const username = localStorage.getItem('user');\n            if (!username) {\n                setCartCount(0);\n                return;\n            }\n\n            try {\n                const response = await axios.get(`http://localhost:8080/getCartCount/${username}`);\n                setCartCount(response.data || 0);\n            } catch (error) {\n                console.error('Error fetching cart count:', error);\n                setCartCount(0);\n            }\n        };\n\n        updateCartCount();\n\n        // Custom event for same-page cart updates\n        window.addEventListener('cartUpdated', updateCartCount);\n\n        return () => {\n            window.removeEventListener('cartUpdated', updateCartCount);\n        };\n    }, []);\n\n    const handleLogout = () => {\n        const confirmed = window.confirm(\"Are you sure you want to logout?\");\n        if (confirmed) {\n            localStorage.removeItem('user');\n            localStorage.removeItem('cart');\n            sessionStorage.clear();\n            navigate('/');\n        }\n    };\n\n    const toggleMenu = () => {\n        setIsMenuOpen(!isMenuOpen);\n    };\n\n    return (\n        <nav className=\"customer-navbar\">\n            <div className=\"navbar-container\">\n                <div className=\"navbar-logo\">\n                    <span>🛒 <span className=\"logo-text\">Sales Savvy</span></span>\n                    <span className=\"user-badge\">{localStorage.getItem('user')}</span>\n                </div>\n\n                <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>\n                    <ul className=\"navbar-links\">\n                        <li>\n                            <span\n                                className=\"nav-item\"\n                                onClick={() => navigate('/CustomerDashboard')}\n                            >\n                                🏠 Home\n                            </span>\n                        </li>\n                        <li>\n                            <span\n                                className=\"nav-item cart-item\"\n                                onClick={() => navigate('/cart')}\n                            >\n                                🛒 Cart ({cartCount})\n                            </span>\n                        </li>\n                        <li>\n                            <span className=\"nav-item\">\n                                📦 My Orders\n                            </span>\n                        </li>\n                        <li>\n                            <button\n                                className=\"logout-btn\"\n                                onClick={handleLogout}\n                            >\n                                🚪 Logout\n                            </button>\n                        </li>\n                    </ul>\n                </div>\n\n                <div className=\"navbar-toggle\" onClick={toggleMenu}>\n                    <span></span>\n                    <span></span>\n                    <span></span>\n                </div>\n            </div>\n        </nav>\n    );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACrC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAMa,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZ;IACA,MAAMa,eAAe,GAAG,MAAAA,CAAA,KAAY;MAChC,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC7C,IAAI,CAACF,QAAQ,EAAE;QACXH,YAAY,CAAC,CAAC,CAAC;QACf;MACJ;MAEA,IAAI;QACA,MAAMM,QAAQ,GAAG,MAAMd,KAAK,CAACe,GAAG,CAAC,sCAAsCJ,QAAQ,EAAE,CAAC;QAClFH,YAAY,CAACM,QAAQ,CAACE,IAAI,IAAI,CAAC,CAAC;MACpC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDT,YAAY,CAAC,CAAC,CAAC;MACnB;IACJ,CAAC;IAEDE,eAAe,CAAC,CAAC;;IAEjB;IACAS,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEV,eAAe,CAAC;IAEvD,OAAO,MAAM;MACTS,MAAM,CAACE,mBAAmB,CAAC,aAAa,EAAEX,eAAe,CAAC;IAC9D,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAGJ,MAAM,CAACK,OAAO,CAAC,kCAAkC,CAAC;IACpE,IAAID,SAAS,EAAE;MACXX,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;MAC/Bb,YAAY,CAACa,UAAU,CAAC,MAAM,CAAC;MAC/BC,cAAc,CAACC,KAAK,CAAC,CAAC;MACtBlB,QAAQ,CAAC,GAAG,CAAC;IACjB;EACJ,CAAC;EAED,MAAMmB,UAAU,GAAGA,CAAA,KAAM;IACrBtB,aAAa,CAAC,CAACD,UAAU,CAAC;EAC9B,CAAC;EAED,oBACIH,OAAA;IAAK2B,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC5B5B,OAAA;MAAK2B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7B5B,OAAA;QAAK2B,SAAS,EAAC,aAAa;QAAAC,QAAA,gBACxB5B,OAAA;UAAA4B,QAAA,GAAM,eAAG,eAAA5B,OAAA;YAAM2B,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9DhC,OAAA;UAAM2B,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAElB,YAAY,CAACC,OAAO,CAAC,MAAM;QAAC;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eAENhC,OAAA;QAAK2B,SAAS,EAAE,eAAexB,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAyB,QAAA,eACxD5B,OAAA;UAAI2B,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACxB5B,OAAA;YAAA4B,QAAA,eACI5B,OAAA;cACI2B,SAAS,EAAC,UAAU;cACpBM,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,oBAAoB,CAAE;cAAAqB,QAAA,EACjD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACLhC,OAAA;YAAA4B,QAAA,eACI5B,OAAA;cACI2B,SAAS,EAAC,oBAAoB;cAC9BM,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,OAAO,CAAE;cAAAqB,QAAA,GACpC,qBACY,EAACvB,SAAS,EAAC,GACxB;YAAA;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACLhC,OAAA;YAAA4B,QAAA,eACI5B,OAAA;cAAM2B,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAE3B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACLhC,OAAA;YAAA4B,QAAA,eACI5B,OAAA;cACI2B,SAAS,EAAC,YAAY;cACtBM,OAAO,EAAEb,YAAa;cAAAQ,QAAA,EACzB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENhC,OAAA;QAAK2B,SAAS,EAAC,eAAe;QAACM,OAAO,EAAEP,UAAW;QAAAE,QAAA,gBAC/C5B,OAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhC,OAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhC,OAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAAC9B,EAAA,CAjGuBD,cAAc;EAAA,QAGjBL,WAAW;AAAA;AAAAsC,EAAA,GAHRjC,cAAc;AAAA,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}