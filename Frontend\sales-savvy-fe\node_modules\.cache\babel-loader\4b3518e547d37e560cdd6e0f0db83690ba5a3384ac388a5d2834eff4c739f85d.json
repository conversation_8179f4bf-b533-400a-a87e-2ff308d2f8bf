{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\shared\\\\ProductsList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { triggerCartUpdate, getUsername, isLoggedIn, handleApiError, showSuccessMessage } from './CartUtils';\nimport './ProductsList.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function ProductsList({\n  userRole = 'customer',\n  showActions = false\n}) {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const fetchProducts = () => {\n    setLoading(true);\n    axios.get('http://localhost:8080/getAllProducts').then(response => {\n      setProducts(response.data);\n      setLoading(false);\n    }).catch(error => {\n      console.error('Error fetching products:', error);\n      setLoading(false);\n    });\n  };\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n  const handleDelete = id => {\n    if (userRole !== 'admin') return;\n    const confirmed = window.confirm(\"Are you sure you want to delete this product?\");\n    if (!confirmed) return;\n    axios.delete('http://localhost:8080/deleteProduct', {\n      params: {\n        id\n      }\n    }).then(() => {\n      fetchProducts();\n      alert('Product deleted successfully!');\n    }).catch(err => {\n      console.error('Delete failed:', err);\n      alert('Failed to delete product. Please try again.');\n    });\n  };\n  const handleUpdate = product => {\n    if (userRole !== 'admin') return;\n    navigate('/updateproduct', {\n      state: {\n        product\n      }\n    });\n  };\n  const handleAddToCart = async product => {\n    if (userRole !== 'customer') return;\n    if (!isLoggedIn()) {\n      showSuccessMessage('Please login to add items to cart');\n      return;\n    }\n    const username = getUsername();\n    try {\n      const cartItem = {\n        username: username,\n        productId: product.id,\n        quantity: 1\n      };\n      const response = await axios.post('http://localhost:8080/addToCart', cartItem);\n      if (response.data === 'cart added') {\n        showSuccessMessage(`Added ${product.name} to cart!`);\n        triggerCartUpdate();\n      } else {\n        showSuccessMessage('Failed to add item to cart: ' + response.data);\n      }\n    } catch (error) {\n      handleApiError(error, 'Failed to add item to cart. Please try again.');\n    }\n  };\n  const handleBuyNow = product => {\n    if (userRole !== 'customer') return;\n    // Buy now functionality for customers\n    alert(`Proceeding to buy ${product.name}!`);\n    // You can implement actual purchase functionality here\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading products...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"products-list-container\",\n    children: [userRole === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"All Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage your product inventory\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 17\n    }, this), userRole === 'customer' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header customer-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Our Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Discover amazing products at great prices\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 17\n    }, this), products.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-products\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"No products found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 21\n      }, this), userRole === 'admin' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Start by adding some products to your inventory.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"add-product-btn\",\n          onClick: () => navigate('/AddProducts'),\n          children: \"Add Your First Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Check back later for new products!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"products-container\",\n      children: [userRole === 'admin' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Products (\", products.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"add-product-btn\",\n          onClick: () => navigate('/AddProducts'),\n          children: \"+ Add New Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 25\n      }, this), userRole === 'customer' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"products-count\",\n        children: /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [products.length, \" Products Available\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 25\n      }, this), userRole === 'admin' ?\n      /*#__PURE__*/\n      // Admin Table View\n      _jsxDEV(\"div\", {\n        className: \"table-wrapper\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"products-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Image\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: products.map(product => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-id\",\n                children: [\"#\", product.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-image\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: product.image,\n                  alt: product.name,\n                  onError: e => {\n                    e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-name\",\n                children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-description\",\n                children: product.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-price\",\n                children: [\"\\u20B9\", product.price]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"product-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"update-btn\",\n                  onClick: () => handleUpdate(product),\n                  title: \"Update Product\",\n                  children: \"\\u270F\\uFE0F Update\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"delete-btn\",\n                  onClick: () => handleDelete(product.id),\n                  title: \"Delete Product\",\n                  children: \"\\uD83D\\uDDD1\\uFE0F Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 45\n              }, this)]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 41\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 25\n      }, this) :\n      /*#__PURE__*/\n      // Customer Card View\n      _jsxDEV(\"div\", {\n        className: \"products-grid\",\n        children: products.map(product => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-image-container\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: product.image,\n              alt: product.name,\n              onError: e => {\n                e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"product-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"product-title\",\n              children: product.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"product-desc\",\n              children: product.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-price-tag\",\n              children: [\"\\u20B9\", product.price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"product-buttons\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"cart-btn\",\n                onClick: () => handleAddToCart(product),\n                children: \"\\uD83D\\uDED2 Add to Cart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"buy-btn\",\n                onClick: () => handleBuyNow(product),\n                children: \"\\uD83D\\uDCB3 Buy Now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 37\n          }, this)]\n        }, product.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 33\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 9\n  }, this);\n}\n_s(ProductsList, \"Ipduiw1a7ftVVJlTipoeXPWOzWQ=\", false, function () {\n  return [useNavigate];\n});\n_c = ProductsList;\nvar _c;\n$RefreshReg$(_c, \"ProductsList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "useNavigate", "triggerCartUpdate", "getUsername", "isLoggedIn", "handleApiError", "showSuccessMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProductsList", "userRole", "showActions", "_s", "products", "setProducts", "loading", "setLoading", "navigate", "fetchProducts", "get", "then", "response", "data", "catch", "error", "console", "handleDelete", "id", "confirmed", "window", "confirm", "delete", "params", "alert", "err", "handleUpdate", "product", "state", "handleAddToCart", "username", "cartItem", "productId", "quantity", "post", "name", "handleBuyNow", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "onClick", "map", "src", "image", "alt", "onError", "e", "target", "description", "price", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/shared/ProductsList.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { triggerCartUpdate, getUsername, isLoggedIn, handleApiError, showSuccessMessage } from './CartUtils';\nimport './ProductsList.css';\n\nexport default function ProductsList({ userRole = 'customer', showActions = false }) {\n    const [products, setProducts] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const navigate = useNavigate();\n\n    const fetchProducts = () => {\n        setLoading(true);\n        axios.get('http://localhost:8080/getAllProducts')\n            .then(response => {\n                setProducts(response.data);\n                setLoading(false);\n            })\n            .catch(error => {\n                console.error('Error fetching products:', error);\n                setLoading(false);\n            });\n    };\n\n    useEffect(() => {\n        fetchProducts();\n    }, []);\n\n    const handleDelete = (id) => {\n        if (userRole !== 'admin') return;\n        \n        const confirmed = window.confirm(\"Are you sure you want to delete this product?\");\n        if (!confirmed) return;\n        \n        axios\n            .delete('http://localhost:8080/deleteProduct', { params: { id } })\n            .then(() => {\n                fetchProducts();\n                alert('Product deleted successfully!');\n            })\n            .catch(err => {\n                console.error('Delete failed:', err);\n                alert('Failed to delete product. Please try again.');\n            });\n    };\n\n    const handleUpdate = (product) => {\n        if (userRole !== 'admin') return;\n        navigate('/updateproduct', { state: { product } });\n    };\n\n    const handleAddToCart = async (product) => {\n        if (userRole !== 'customer') return;\n\n        if (!isLoggedIn()) {\n            showSuccessMessage('Please login to add items to cart');\n            return;\n        }\n\n        const username = getUsername();\n\n        try {\n            const cartItem = {\n                username: username,\n                productId: product.id,\n                quantity: 1\n            };\n\n            const response = await axios.post('http://localhost:8080/addToCart', cartItem);\n\n            if (response.data === 'cart added') {\n                showSuccessMessage(`Added ${product.name} to cart!`);\n                triggerCartUpdate();\n            } else {\n                showSuccessMessage('Failed to add item to cart: ' + response.data);\n            }\n        } catch (error) {\n            handleApiError(error, 'Failed to add item to cart. Please try again.');\n        }\n    };\n\n    const handleBuyNow = (product) => {\n        if (userRole !== 'customer') return;\n        // Buy now functionality for customers\n        alert(`Proceeding to buy ${product.name}!`);\n        // You can implement actual purchase functionality here\n    };\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"loading-spinner\"></div>\n                <p>Loading products...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"products-list-container\">\n            {userRole === 'admin' && (\n                <div className=\"page-header\">\n                    <h1>All Products</h1>\n                    <p>Manage your product inventory</p>\n                </div>\n            )}\n            \n            {userRole === 'customer' && (\n                <div className=\"page-header customer-header\">\n                    <h1>Our Products</h1>\n                    <p>Discover amazing products at great prices</p>\n                </div>\n            )}\n            \n            {products.length === 0 ? (\n                <div className=\"no-products\">\n                    <h3>No products found</h3>\n                    {userRole === 'admin' ? (\n                        <>\n                            <p>Start by adding some products to your inventory.</p>\n                            <button \n                                className=\"add-product-btn\"\n                                onClick={() => navigate('/AddProducts')}\n                            >\n                                Add Your First Product\n                            </button>\n                        </>\n                    ) : (\n                        <p>Check back later for new products!</p>\n                    )}\n                </div>\n            ) : (\n                <div className=\"products-container\">\n                    {userRole === 'admin' && (\n                        <div className=\"table-header\">\n                            <h3>Products ({products.length})</h3>\n                            <button \n                                className=\"add-product-btn\"\n                                onClick={() => navigate('/AddProducts')}\n                            >\n                                + Add New Product\n                            </button>\n                        </div>\n                    )}\n                    \n                    {userRole === 'customer' && (\n                        <div className=\"products-count\">\n                            <h3>{products.length} Products Available</h3>\n                        </div>\n                    )}\n                    \n                    {userRole === 'admin' ? (\n                        // Admin Table View\n                        <div className=\"table-wrapper\">\n                            <table className=\"products-table\">\n                                <thead>\n                                    <tr>\n                                        <th>ID</th>\n                                        <th>Image</th>\n                                        <th>Name</th>\n                                        <th>Description</th>\n                                        <th>Price</th>\n                                        <th>Actions</th>\n                                    </tr>\n                                </thead>\n                                <tbody>\n                                    {products.map(product => (\n                                        <tr key={product.id}>\n                                            <td className=\"product-id\">#{product.id}</td>\n                                            <td className=\"product-image\">\n                                                <img \n                                                    src={product.image} \n                                                    alt={product.name}\n                                                    onError={(e) => {\n                                                        e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';\n                                                    }}\n                                                />\n                                            </td>\n                                            <td className=\"product-name\">\n                                                <strong>{product.name}</strong>\n                                            </td>\n                                            <td className=\"product-description\">\n                                                {product.description}\n                                            </td>\n                                            <td className=\"product-price\">\n                                                ₹{product.price}\n                                            </td>\n                                            <td className=\"product-actions\">\n                                                <button \n                                                    className=\"update-btn\"\n                                                    onClick={() => handleUpdate(product)}\n                                                    title=\"Update Product\"\n                                                >\n                                                    ✏️ Update\n                                                </button>\n                                                <button \n                                                    className=\"delete-btn\"\n                                                    onClick={() => handleDelete(product.id)}\n                                                    title=\"Delete Product\"\n                                                >\n                                                    🗑️ Delete\n                                                </button>\n                                            </td>\n                                        </tr>\n                                    ))}\n                                </tbody>\n                            </table>\n                        </div>\n                    ) : (\n                        // Customer Card View\n                        <div className=\"products-grid\">\n                            {products.map(product => (\n                                <div key={product.id} className=\"product-card\">\n                                    <div className=\"product-image-container\">\n                                        <img \n                                            src={product.image} \n                                            alt={product.name}\n                                            onError={(e) => {\n                                                e.target.src = 'https://via.placeholder.com/300x200?text=No+Image';\n                                            }}\n                                        />\n                                    </div>\n                                    <div className=\"product-info\">\n                                        <h3 className=\"product-title\">{product.name}</h3>\n                                        <p className=\"product-desc\">{product.description}</p>\n                                        <div className=\"product-price-tag\">₹{product.price}</div>\n                                        <div className=\"product-buttons\">\n                                            <button \n                                                className=\"cart-btn\"\n                                                onClick={() => handleAddToCart(product)}\n                                            >\n                                                🛒 Add to Cart\n                                            </button>\n                                            <button \n                                                className=\"buy-btn\"\n                                                onClick={() => handleBuyNow(product)}\n                                            >\n                                                💳 Buy Now\n                                            </button>\n                                        </div>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                    )}\n                </div>\n            )}\n        </div>\n    );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,cAAc,EAAEC,kBAAkB,QAAQ,aAAa;AAC5G,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5B,eAAe,SAASC,YAAYA,CAAC;EAAEC,QAAQ,GAAG,UAAU;EAAEC,WAAW,GAAG;AAAM,CAAC,EAAE;EAAAC,EAAA;EACjF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMoB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9B,MAAMmB,aAAa,GAAGA,CAAA,KAAM;IACxBF,UAAU,CAAC,IAAI,CAAC;IAChBlB,KAAK,CAACqB,GAAG,CAAC,sCAAsC,CAAC,CAC5CC,IAAI,CAACC,QAAQ,IAAI;MACdP,WAAW,CAACO,QAAQ,CAACC,IAAI,CAAC;MAC1BN,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,CACDO,KAAK,CAACC,KAAK,IAAI;MACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDR,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACV,CAAC;EAEDpB,SAAS,CAAC,MAAM;IACZsB,aAAa,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,YAAY,GAAIC,EAAE,IAAK;IACzB,IAAIjB,QAAQ,KAAK,OAAO,EAAE;IAE1B,MAAMkB,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC;IACjF,IAAI,CAACF,SAAS,EAAE;IAEhB9B,KAAK,CACAiC,MAAM,CAAC,qCAAqC,EAAE;MAAEC,MAAM,EAAE;QAAEL;MAAG;IAAE,CAAC,CAAC,CACjEP,IAAI,CAAC,MAAM;MACRF,aAAa,CAAC,CAAC;MACfe,KAAK,CAAC,+BAA+B,CAAC;IAC1C,CAAC,CAAC,CACDV,KAAK,CAACW,GAAG,IAAI;MACVT,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEU,GAAG,CAAC;MACpCD,KAAK,CAAC,6CAA6C,CAAC;IACxD,CAAC,CAAC;EACV,CAAC;EAED,MAAME,YAAY,GAAIC,OAAO,IAAK;IAC9B,IAAI1B,QAAQ,KAAK,OAAO,EAAE;IAC1BO,QAAQ,CAAC,gBAAgB,EAAE;MAAEoB,KAAK,EAAE;QAAED;MAAQ;IAAE,CAAC,CAAC;EACtD,CAAC;EAED,MAAME,eAAe,GAAG,MAAOF,OAAO,IAAK;IACvC,IAAI1B,QAAQ,KAAK,UAAU,EAAE;IAE7B,IAAI,CAACR,UAAU,CAAC,CAAC,EAAE;MACfE,kBAAkB,CAAC,mCAAmC,CAAC;MACvD;IACJ;IAEA,MAAMmC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;IAE9B,IAAI;MACA,MAAMuC,QAAQ,GAAG;QACbD,QAAQ,EAAEA,QAAQ;QAClBE,SAAS,EAAEL,OAAO,CAACT,EAAE;QACrBe,QAAQ,EAAE;MACd,CAAC;MAED,MAAMrB,QAAQ,GAAG,MAAMvB,KAAK,CAAC6C,IAAI,CAAC,iCAAiC,EAAEH,QAAQ,CAAC;MAE9E,IAAInB,QAAQ,CAACC,IAAI,KAAK,YAAY,EAAE;QAChClB,kBAAkB,CAAC,SAASgC,OAAO,CAACQ,IAAI,WAAW,CAAC;QACpD5C,iBAAiB,CAAC,CAAC;MACvB,CAAC,MAAM;QACHI,kBAAkB,CAAC,8BAA8B,GAAGiB,QAAQ,CAACC,IAAI,CAAC;MACtE;IACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MACZrB,cAAc,CAACqB,KAAK,EAAE,+CAA+C,CAAC;IAC1E;EACJ,CAAC;EAED,MAAMqB,YAAY,GAAIT,OAAO,IAAK;IAC9B,IAAI1B,QAAQ,KAAK,UAAU,EAAE;IAC7B;IACAuB,KAAK,CAAC,qBAAqBG,OAAO,CAACQ,IAAI,GAAG,CAAC;IAC3C;EACJ,CAAC;EAED,IAAI7B,OAAO,EAAE;IACT,oBACIT,OAAA;MAAKwC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BzC,OAAA;QAAKwC,SAAS,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACvC7C,OAAA;QAAAyC,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAEd;EAEA,oBACI7C,OAAA;IAAKwC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,GACnCrC,QAAQ,KAAK,OAAO,iBACjBJ,OAAA;MAAKwC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBzC,OAAA;QAAAyC,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrB7C,OAAA;QAAAyC,QAAA,EAAG;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACR,EAEAzC,QAAQ,KAAK,UAAU,iBACpBJ,OAAA;MAAKwC,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBACxCzC,OAAA;QAAAyC,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrB7C,OAAA;QAAAyC,QAAA,EAAG;MAAyC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CACR,EAEAtC,QAAQ,CAACuC,MAAM,KAAK,CAAC,gBAClB9C,OAAA;MAAKwC,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBzC,OAAA;QAAAyC,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACzBzC,QAAQ,KAAK,OAAO,gBACjBJ,OAAA,CAAAE,SAAA;QAAAuC,QAAA,gBACIzC,OAAA;UAAAyC,QAAA,EAAG;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvD7C,OAAA;UACIwC,SAAS,EAAC,iBAAiB;UAC3BO,OAAO,EAAEA,CAAA,KAAMpC,QAAQ,CAAC,cAAc,CAAE;UAAA8B,QAAA,EAC3C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACX,CAAC,gBAEH7C,OAAA;QAAAyC,QAAA,EAAG;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAC3C;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,gBAEN7C,OAAA;MAAKwC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,GAC9BrC,QAAQ,KAAK,OAAO,iBACjBJ,OAAA;QAAKwC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBzC,OAAA;UAAAyC,QAAA,GAAI,YAAU,EAAClC,QAAQ,CAACuC,MAAM,EAAC,GAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrC7C,OAAA;UACIwC,SAAS,EAAC,iBAAiB;UAC3BO,OAAO,EAAEA,CAAA,KAAMpC,QAAQ,CAAC,cAAc,CAAE;UAAA8B,QAAA,EAC3C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,EAEAzC,QAAQ,KAAK,UAAU,iBACpBJ,OAAA;QAAKwC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC3BzC,OAAA;UAAAyC,QAAA,GAAKlC,QAAQ,CAACuC,MAAM,EAAC,qBAAmB;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CACR,EAEAzC,QAAQ,KAAK,OAAO;MAAA;MACjB;MACAJ,OAAA;QAAKwC,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1BzC,OAAA;UAAOwC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BzC,OAAA;YAAAyC,QAAA,eACIzC,OAAA;cAAAyC,QAAA,gBACIzC,OAAA;gBAAAyC,QAAA,EAAI;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACX7C,OAAA;gBAAAyC,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd7C,OAAA;gBAAAyC,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb7C,OAAA;gBAAAyC,QAAA,EAAI;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpB7C,OAAA;gBAAAyC,QAAA,EAAI;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd7C,OAAA;gBAAAyC,QAAA,EAAI;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACR7C,OAAA;YAAAyC,QAAA,EACKlC,QAAQ,CAACyC,GAAG,CAAClB,OAAO,iBACjB9B,OAAA;cAAAyC,QAAA,gBACIzC,OAAA;gBAAIwC,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAAC,GAAC,EAACX,OAAO,CAACT,EAAE;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7C7C,OAAA;gBAAIwC,SAAS,EAAC,eAAe;gBAAAC,QAAA,eACzBzC,OAAA;kBACIiD,GAAG,EAAEnB,OAAO,CAACoB,KAAM;kBACnBC,GAAG,EAAErB,OAAO,CAACQ,IAAK;kBAClBc,OAAO,EAAGC,CAAC,IAAK;oBACZA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,mDAAmD;kBACtE;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,cAAc;gBAAAC,QAAA,eACxBzC,OAAA;kBAAAyC,QAAA,EAASX,OAAO,CAACQ;gBAAI;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAC9BX,OAAO,CAACyB;cAAW;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,QACzB,EAACX,OAAO,CAAC0B,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACL7C,OAAA;gBAAIwC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC3BzC,OAAA;kBACIwC,SAAS,EAAC,YAAY;kBACtBO,OAAO,EAAEA,CAAA,KAAMlB,YAAY,CAACC,OAAO,CAAE;kBACrC2B,KAAK,EAAC,gBAAgB;kBAAAhB,QAAA,EACzB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT7C,OAAA;kBACIwC,SAAS,EAAC,YAAY;kBACtBO,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAACU,OAAO,CAACT,EAAE,CAAE;kBACxCoC,KAAK,EAAC,gBAAgB;kBAAAhB,QAAA,EACzB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA,GAnCAf,OAAO,CAACT,EAAE;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoCf,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;MAAA;MAEN;MACA7C,OAAA;QAAKwC,SAAS,EAAC,eAAe;QAAAC,QAAA,EACzBlC,QAAQ,CAACyC,GAAG,CAAClB,OAAO,iBACjB9B,OAAA;UAAsBwC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC1CzC,OAAA;YAAKwC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACpCzC,OAAA;cACIiD,GAAG,EAAEnB,OAAO,CAACoB,KAAM;cACnBC,GAAG,EAAErB,OAAO,CAACQ,IAAK;cAClBc,OAAO,EAAGC,CAAC,IAAK;gBACZA,CAAC,CAACC,MAAM,CAACL,GAAG,GAAG,mDAAmD;cACtE;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN7C,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzBzC,OAAA;cAAIwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAEX,OAAO,CAACQ;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjD7C,OAAA;cAAGwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAEX,OAAO,CAACyB;YAAW;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD7C,OAAA;cAAKwC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAAC,QAAC,EAACX,OAAO,CAAC0B,KAAK;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzD7C,OAAA;cAAKwC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC5BzC,OAAA;gBACIwC,SAAS,EAAC,UAAU;gBACpBO,OAAO,EAAEA,CAAA,KAAMf,eAAe,CAACF,OAAO,CAAE;gBAAAW,QAAA,EAC3C;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7C,OAAA;gBACIwC,SAAS,EAAC,SAAS;gBACnBO,OAAO,EAAEA,CAAA,KAAMR,YAAY,CAACT,OAAO,CAAE;gBAAAW,QAAA,EACxC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA,GA5BAf,OAAO,CAACT,EAAE;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6Bf,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAACvC,EAAA,CAlPuBH,YAAY;EAAA,QAGfV,WAAW;AAAA;AAAAiE,EAAA,GAHRvD,YAAY;AAAA,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}