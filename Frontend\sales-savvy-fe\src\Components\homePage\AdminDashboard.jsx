import React from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from '../Navbar/Navbar';
import './AdminDashboard.css';

export default function AdminDashboard() {
    const navigate = useNavigate();
    
    return (
        <>
            <Navbar />
            <div className="admin-dashboard">
                <div className="dashboard-header">
                    <h1>Admin Dashboard</h1>
                    <p>Welcome back! Manage your products and inventory from here.</p>
                </div>
                
                <div className="dashboard-cards">
                    <div className="dashboard-card" onClick={() => navigate("/AllProducts")}>
                        <div className="card-icon">📦</div>
                        <h3>All Products</h3>
                        <p>View and manage all your products</p>
                        <div className="card-arrow">→</div>
                    </div>
                    
                    <div className="dashboard-card" onClick={() => navigate("/AddProducts")}>
                        <div className="card-icon">➕</div>
                        <h3>Add Product</h3>
                        <p>Add new products to your inventory</p>
                        <div className="card-arrow">→</div>
                    </div>
                    
                    <div className="dashboard-card" onClick={() => navigate("/AllProducts")}>
                        <div className="card-icon">📊</div>
                        <h3>Analytics</h3>
                        <p>View product statistics and reports</p>
                        <div className="card-arrow">→</div>
                    </div>
                    
                    <div className="dashboard-card" onClick={() => navigate("/AllProducts")}>
                        <div className="card-icon">⚙️</div>
                        <h3>Settings</h3>
                        <p>Configure your store settings</p>
                        <div className="card-arrow">→</div>
                    </div>
                </div>
                
                <div className="quick-actions">
                    <h3>Quick Actions</h3>
                    <div className="action-buttons">
                        <button 
                            className="action-btn primary"
                            onClick={() => navigate("/AddProducts")}
                        >
                            + Add New Product
                        </button>
                        <button 
                            className="action-btn secondary"
                            onClick={() => navigate("/AllProducts")}
                        >
                            📦 View All Products
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
}
