{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\Customer\\\\Cart.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport CustomerNavbar from '../Navbar/CustomerNavbar';\nimport Payment from '../Payment/Payment';\nimport { triggerCartUpdate, getUsername, handleApiError, showSuccessMessage } from '../shared/CartUtils';\nimport './Cart.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function Cart() {\n  _s();\n  const [cartItems, setCartItems] = useState([]);\n  const [total, setTotal] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [showPayment, setShowPayment] = useState(false);\n  const calculateTotal = useCallback(items => {\n    const totalAmount = items.reduce((sum, item) => sum + item.product.price * item.quantity, 0);\n    setTotal(totalAmount);\n  }, []);\n  const fetchCartItems = useCallback(async () => {\n    const username = getUsername();\n    if (!username) {\n      setLoading(false);\n      return;\n    }\n    try {\n      const response = await axios.get(`http://localhost:8080/getCart/${username}`);\n      const items = response.data || [];\n      setCartItems(items);\n      calculateTotal(items);\n      setLoading(false);\n    } catch (error) {\n      handleApiError(error, 'Failed to load cart items');\n      setLoading(false);\n    }\n  }, [calculateTotal]);\n  useEffect(() => {\n    fetchCartItems();\n  }, [fetchCartItems]);\n  const updateQuantity = async (productId, newQuantity) => {\n    const username = getUsername();\n    if (!username) return;\n    if (newQuantity <= 0) {\n      removeFromCart(productId);\n      return;\n    }\n    try {\n      const cartItem = {\n        username: username,\n        productId: productId,\n        quantity: newQuantity\n      };\n      const response = await axios.put('http://localhost:8080/updateCartItem', cartItem);\n      if (response.data === 'cart updated') {\n        fetchCartItems(); // Refresh cart data\n        triggerCartUpdate();\n      } else {\n        showSuccessMessage('Failed to update cart: ' + response.data);\n      }\n    } catch (error) {\n      handleApiError(error, 'Failed to update cart. Please try again.');\n    }\n  };\n  const removeFromCart = async productId => {\n    const username = getUsername();\n    if (!username) return;\n    try {\n      const response = await axios.delete('http://localhost:8080/removeFromCart', {\n        params: {\n          username,\n          productId\n        }\n      });\n      if (response.data === 'item removed from cart') {\n        fetchCartItems(); // Refresh cart data\n        triggerCartUpdate();\n      } else {\n        showSuccessMessage('Failed to remove item: ' + response.data);\n      }\n    } catch (error) {\n      handleApiError(error, 'Failed to remove item. Please try again.');\n    }\n  };\n  const clearCart = async () => {\n    const confirmed = window.confirm(\"Are you sure you want to clear your cart?\");\n    if (!confirmed) return;\n    const username = getUsername();\n    if (!username) return;\n    try {\n      const response = await axios.delete(`http://localhost:8080/clearCart/${username}`);\n      if (response.data === 'cart cleared') {\n        setCartItems([]);\n        setTotal(0);\n        triggerCartUpdate();\n      } else {\n        showSuccessMessage('Failed to clear cart: ' + response.data);\n      }\n    } catch (error) {\n      handleApiError(error, 'Failed to clear cart. Please try again.');\n    }\n  };\n  const proceedToCheckout = () => {\n    if (cartItems.length === 0) {\n      alert(\"Your cart is empty!\");\n      return;\n    }\n    alert(`Proceeding to checkout with total: ₹${total}`);\n    // Implement actual checkout logic here\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(CustomerNavbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading your cart...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(CustomerNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cart-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"Shopping Cart\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Review your items before checkout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-main-content\",\n        children: cartItems.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"empty-cart\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-cart-icon\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Your cart is empty\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Add some products to get started!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"continue-shopping-btn\",\n            onClick: () => window.history.back(),\n            children: \"Continue Shopping\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 21\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-items\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cart-items-grid\",\n              children: cartItems.map(item => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cart-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"item-image\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: item.product.image,\n                    alt: item.product.name,\n                    onError: e => {\n                      e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"item-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: item.product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: item.product.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"item-price\",\n                    children: [\"\\u20B9\", item.product.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"item-controls\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"quantity-controls\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => updateQuantity(item.product.id, item.quantity - 1),\n                      className: \"quantity-btn minus-btn\",\n                      title: \"Remove one\",\n                      children: \"-\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 182,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"quantity\",\n                      children: item.quantity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => updateQuantity(item.product.id, item.quantity + 1),\n                      className: \"quantity-btn\",\n                      title: \"Add one more\",\n                      children: \"+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"item-total\",\n                    children: [\"\\u20B9\", item.product.price * item.quantity]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 41\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => removeFromCart(item.product.id),\n                    className: \"remove-btn\",\n                    title: \"Remove from cart\",\n                    children: \"\\uD83D\\uDDD1\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 37\n                }, this)]\n              }, item.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 33\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cart-summary\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"summary-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Order Summary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"summary-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Items (\", cartItems.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u20B9\", total]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"summary-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Shipping\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Free\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"summary-row total-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Total\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"\\u20B9\", total]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cart-actions\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"checkout-btn\",\n                  onClick: proceedToCheckout,\n                  children: \"Proceed to Checkout\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"clear-cart-btn\",\n                  onClick: clearCart,\n                  children: \"Clear Cart\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n}\n_s(Cart, \"B7DnEkk8OZVq0UExnc+Lrj29Ft0=\");\n_c = Cart;\nvar _c;\n$RefreshReg$(_c, \"Cart\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "axios", "CustomerNavbar", "Payment", "triggerCartUpdate", "getUsername", "handleApiError", "showSuccessMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "cartItems", "setCartItems", "total", "setTotal", "loading", "setLoading", "showPayment", "setShowPayment", "calculateTotal", "items", "totalAmount", "reduce", "sum", "item", "product", "price", "quantity", "fetchCartItems", "username", "response", "get", "data", "error", "updateQuantity", "productId", "newQuantity", "removeFromCart", "cartItem", "put", "delete", "params", "clearCart", "confirmed", "window", "confirm", "proceedToCheckout", "length", "alert", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "history", "back", "map", "src", "image", "alt", "name", "onError", "e", "target", "description", "id", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/Customer/Cart.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport axios from 'axios';\nimport CustomerNavbar from '../Navbar/CustomerNavbar';\nimport Payment from '../Payment/Payment';\nimport { triggerCartUpdate, getUsername, handleApiError, showSuccessMessage } from '../shared/CartUtils';\nimport './Cart.css';\n\nexport default function Cart() {\n    const [cartItems, setCartItems] = useState([]);\n    const [total, setTotal] = useState(0);\n    const [loading, setLoading] = useState(true);\n    const [showPayment, setShowPayment] = useState(false);\n\n    const calculateTotal = useCallback((items) => {\n        const totalAmount = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);\n        setTotal(totalAmount);\n    }, []);\n\n    const fetchCartItems = useCallback(async () => {\n        const username = getUsername();\n        if (!username) {\n            setLoading(false);\n            return;\n        }\n\n        try {\n            const response = await axios.get(`http://localhost:8080/getCart/${username}`);\n            const items = response.data || [];\n            setCartItems(items);\n            calculateTotal(items);\n            setLoading(false);\n        } catch (error) {\n            handleApiError(error, 'Failed to load cart items');\n            setLoading(false);\n        }\n    }, [calculateTotal]);\n\n    useEffect(() => {\n        fetchCartItems();\n    }, [fetchCartItems]);\n\n    const updateQuantity = async (productId, newQuantity) => {\n        const username = getUsername();\n        if (!username) return;\n\n        if (newQuantity <= 0) {\n            removeFromCart(productId);\n            return;\n        }\n\n        try {\n            const cartItem = {\n                username: username,\n                productId: productId,\n                quantity: newQuantity\n            };\n\n            const response = await axios.put('http://localhost:8080/updateCartItem', cartItem);\n\n            if (response.data === 'cart updated') {\n                fetchCartItems(); // Refresh cart data\n                triggerCartUpdate();\n            } else {\n                showSuccessMessage('Failed to update cart: ' + response.data);\n            }\n        } catch (error) {\n            handleApiError(error, 'Failed to update cart. Please try again.');\n        }\n    };\n\n    const removeFromCart = async (productId) => {\n        const username = getUsername();\n        if (!username) return;\n\n        try {\n            const response = await axios.delete('http://localhost:8080/removeFromCart', {\n                params: { username, productId }\n            });\n\n            if (response.data === 'item removed from cart') {\n                fetchCartItems(); // Refresh cart data\n                triggerCartUpdate();\n            } else {\n                showSuccessMessage('Failed to remove item: ' + response.data);\n            }\n        } catch (error) {\n            handleApiError(error, 'Failed to remove item. Please try again.');\n        }\n    };\n\n    const clearCart = async () => {\n        const confirmed = window.confirm(\"Are you sure you want to clear your cart?\");\n        if (!confirmed) return;\n\n        const username = getUsername();\n        if (!username) return;\n\n        try {\n            const response = await axios.delete(`http://localhost:8080/clearCart/${username}`);\n\n            if (response.data === 'cart cleared') {\n                setCartItems([]);\n                setTotal(0);\n                triggerCartUpdate();\n            } else {\n                showSuccessMessage('Failed to clear cart: ' + response.data);\n            }\n        } catch (error) {\n            handleApiError(error, 'Failed to clear cart. Please try again.');\n        }\n    };\n\n    const proceedToCheckout = () => {\n        if (cartItems.length === 0) {\n            alert(\"Your cart is empty!\");\n            return;\n        }\n        alert(`Proceeding to checkout with total: ₹${total}`);\n        // Implement actual checkout logic here\n    };\n\n    if (loading) {\n        return (\n            <>\n                <CustomerNavbar />\n                <div className=\"cart-container\">\n                    <div className=\"loading-container\">\n                        <div className=\"loading-spinner\"></div>\n                        <p>Loading your cart...</p>\n                    </div>\n                </div>\n            </>\n        );\n    }\n\n    return (\n        <>\n            <CustomerNavbar />\n            <div className=\"cart-container\">\n                <div className=\"cart-header\">\n                    <div className=\"cart-header-content\">\n                        <h1>Shopping Cart</h1>\n                        <p>Review your items before checkout</p>\n                    </div>\n                </div>\n                <div className=\"cart-main-content\">\n\n                {cartItems.length === 0 ? (\n                    <div className=\"empty-cart\">\n                        <div className=\"empty-cart-icon\">🛒</div>\n                        <h3>Your cart is empty</h3>\n                        <p>Add some products to get started!</p>\n                        <button\n                            className=\"continue-shopping-btn\"\n                            onClick={() => window.history.back()}\n                        >\n                            Continue Shopping\n                        </button>\n                    </div>\n                ) : (\n                    <div className=\"cart-content\">\n                        <div className=\"cart-items\">\n                            <div className=\"cart-items-grid\">\n                            {cartItems.map(item => (\n                                <div key={item.id} className=\"cart-item\">\n                                    <div className=\"item-image\">\n                                        <img\n                                            src={item.product.image}\n                                            alt={item.product.name}\n                                            onError={(e) => {\n                                                e.target.src = 'https://via.placeholder.com/100x100?text=No+Image';\n                                            }}\n                                        />\n                                    </div>\n                                    <div className=\"item-details\">\n                                        <h3>{item.product.name}</h3>\n                                        <p>{item.product.description}</p>\n                                        <div className=\"item-price\">₹{item.product.price}</div>\n                                    </div>\n                                    <div className=\"item-controls\">\n                                        <div className=\"quantity-controls\">\n                                            <button\n                                                onClick={() => updateQuantity(item.product.id, item.quantity - 1)}\n                                                className=\"quantity-btn minus-btn\"\n                                                title=\"Remove one\"\n                                            >\n                                                -\n                                            </button>\n                                            <span className=\"quantity\">{item.quantity}</span>\n                                            <button\n                                                onClick={() => updateQuantity(item.product.id, item.quantity + 1)}\n                                                className=\"quantity-btn\"\n                                                title=\"Add one more\"\n                                            >\n                                                +\n                                            </button>\n                                        </div>\n                                        <div className=\"item-total\">₹{item.product.price * item.quantity}</div>\n                                        <button\n                                            onClick={() => removeFromCart(item.product.id)}\n                                            className=\"remove-btn\"\n                                            title=\"Remove from cart\"\n                                        >\n                                            🗑️\n                                        </button>\n                                    </div>\n                                </div>\n                            ))}\n                            </div>\n                        </div>\n\n                        <div className=\"cart-summary\">\n                            <div className=\"summary-card\">\n                                <h3>Order Summary</h3>\n                                <div className=\"summary-row\">\n                                    <span>Items ({cartItems.length})</span>\n                                    <span>₹{total}</span>\n                                </div>\n                                <div className=\"summary-row\">\n                                    <span>Shipping</span>\n                                    <span>Free</span>\n                                </div>\n                                <div className=\"summary-row total-row\">\n                                    <span>Total</span>\n                                    <span>₹{total}</span>\n                                </div>\n                                <div className=\"cart-actions\">\n                                    <button \n                                        className=\"checkout-btn\"\n                                        onClick={proceedToCheckout}\n                                    >\n                                        Proceed to Checkout\n                                    </button>\n                                    <button \n                                        className=\"clear-cart-btn\"\n                                        onClick={clearCart}\n                                    >\n                                        Clear Cart\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                )}\n                </div>\n            </div>\n        </>\n    );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,cAAc,EAAEC,kBAAkB,QAAQ,qBAAqB;AACxG,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpB,eAAe,SAASC,IAAIA,CAAA,EAAG;EAAAC,EAAA;EAC3B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMwB,cAAc,GAAGtB,WAAW,CAAEuB,KAAK,IAAK;IAC1C,MAAMC,WAAW,GAAGD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAIC,IAAI,CAACC,OAAO,CAACC,KAAK,GAAGF,IAAI,CAACG,QAAS,EAAE,CAAC,CAAC;IAC9Fb,QAAQ,CAACO,WAAW,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,cAAc,GAAG/B,WAAW,CAAC,YAAY;IAC3C,MAAMgC,QAAQ,GAAG3B,WAAW,CAAC,CAAC;IAC9B,IAAI,CAAC2B,QAAQ,EAAE;MACXb,UAAU,CAAC,KAAK,CAAC;MACjB;IACJ;IAEA,IAAI;MACA,MAAMc,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,iCAAiCF,QAAQ,EAAE,CAAC;MAC7E,MAAMT,KAAK,GAAGU,QAAQ,CAACE,IAAI,IAAI,EAAE;MACjCpB,YAAY,CAACQ,KAAK,CAAC;MACnBD,cAAc,CAACC,KAAK,CAAC;MACrBJ,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACZ9B,cAAc,CAAC8B,KAAK,EAAE,2BAA2B,CAAC;MAClDjB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,CAACG,cAAc,CAAC,CAAC;EAEpBvB,SAAS,CAAC,MAAM;IACZgC,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpB,MAAMM,cAAc,GAAG,MAAAA,CAAOC,SAAS,EAAEC,WAAW,KAAK;IACrD,MAAMP,QAAQ,GAAG3B,WAAW,CAAC,CAAC;IAC9B,IAAI,CAAC2B,QAAQ,EAAE;IAEf,IAAIO,WAAW,IAAI,CAAC,EAAE;MAClBC,cAAc,CAACF,SAAS,CAAC;MACzB;IACJ;IAEA,IAAI;MACA,MAAMG,QAAQ,GAAG;QACbT,QAAQ,EAAEA,QAAQ;QAClBM,SAAS,EAAEA,SAAS;QACpBR,QAAQ,EAAES;MACd,CAAC;MAED,MAAMN,QAAQ,GAAG,MAAMhC,KAAK,CAACyC,GAAG,CAAC,sCAAsC,EAAED,QAAQ,CAAC;MAElF,IAAIR,QAAQ,CAACE,IAAI,KAAK,cAAc,EAAE;QAClCJ,cAAc,CAAC,CAAC,CAAC,CAAC;QAClB3B,iBAAiB,CAAC,CAAC;MACvB,CAAC,MAAM;QACHG,kBAAkB,CAAC,yBAAyB,GAAG0B,QAAQ,CAACE,IAAI,CAAC;MACjE;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZ9B,cAAc,CAAC8B,KAAK,EAAE,0CAA0C,CAAC;IACrE;EACJ,CAAC;EAED,MAAMI,cAAc,GAAG,MAAOF,SAAS,IAAK;IACxC,MAAMN,QAAQ,GAAG3B,WAAW,CAAC,CAAC;IAC9B,IAAI,CAAC2B,QAAQ,EAAE;IAEf,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAAC0C,MAAM,CAAC,sCAAsC,EAAE;QACxEC,MAAM,EAAE;UAAEZ,QAAQ;UAAEM;QAAU;MAClC,CAAC,CAAC;MAEF,IAAIL,QAAQ,CAACE,IAAI,KAAK,wBAAwB,EAAE;QAC5CJ,cAAc,CAAC,CAAC,CAAC,CAAC;QAClB3B,iBAAiB,CAAC,CAAC;MACvB,CAAC,MAAM;QACHG,kBAAkB,CAAC,yBAAyB,GAAG0B,QAAQ,CAACE,IAAI,CAAC;MACjE;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZ9B,cAAc,CAAC8B,KAAK,EAAE,0CAA0C,CAAC;IACrE;EACJ,CAAC;EAED,MAAMS,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC1B,MAAMC,SAAS,GAAGC,MAAM,CAACC,OAAO,CAAC,2CAA2C,CAAC;IAC7E,IAAI,CAACF,SAAS,EAAE;IAEhB,MAAMd,QAAQ,GAAG3B,WAAW,CAAC,CAAC;IAC9B,IAAI,CAAC2B,QAAQ,EAAE;IAEf,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAAC0C,MAAM,CAAC,mCAAmCX,QAAQ,EAAE,CAAC;MAElF,IAAIC,QAAQ,CAACE,IAAI,KAAK,cAAc,EAAE;QAClCpB,YAAY,CAAC,EAAE,CAAC;QAChBE,QAAQ,CAAC,CAAC,CAAC;QACXb,iBAAiB,CAAC,CAAC;MACvB,CAAC,MAAM;QACHG,kBAAkB,CAAC,wBAAwB,GAAG0B,QAAQ,CAACE,IAAI,CAAC;MAChE;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZ9B,cAAc,CAAC8B,KAAK,EAAE,yCAAyC,CAAC;IACpE;EACJ,CAAC;EAED,MAAMa,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,IAAInC,SAAS,CAACoC,MAAM,KAAK,CAAC,EAAE;MACxBC,KAAK,CAAC,qBAAqB,CAAC;MAC5B;IACJ;IACAA,KAAK,CAAC,uCAAuCnC,KAAK,EAAE,CAAC;IACrD;EACJ,CAAC;EAED,IAAIE,OAAO,EAAE;IACT,oBACIT,OAAA,CAAAE,SAAA;MAAAyC,QAAA,gBACI3C,OAAA,CAACP,cAAc;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClB/C,OAAA;QAAKgD,SAAS,EAAC,gBAAgB;QAAAL,QAAA,eAC3B3C,OAAA;UAAKgD,SAAS,EAAC,mBAAmB;UAAAL,QAAA,gBAC9B3C,OAAA;YAAKgD,SAAS,EAAC;UAAiB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvC/C,OAAA;YAAA2C,QAAA,EAAG;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACR,CAAC;EAEX;EAEA,oBACI/C,OAAA,CAAAE,SAAA;IAAAyC,QAAA,gBACI3C,OAAA,CAACP,cAAc;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClB/C,OAAA;MAAKgD,SAAS,EAAC,gBAAgB;MAAAL,QAAA,gBAC3B3C,OAAA;QAAKgD,SAAS,EAAC,aAAa;QAAAL,QAAA,eACxB3C,OAAA;UAAKgD,SAAS,EAAC,qBAAqB;UAAAL,QAAA,gBAChC3C,OAAA;YAAA2C,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB/C,OAAA;YAAA2C,QAAA,EAAG;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN/C,OAAA;QAAKgD,SAAS,EAAC,mBAAmB;QAAAL,QAAA,EAEjCtC,SAAS,CAACoC,MAAM,KAAK,CAAC,gBACnBzC,OAAA;UAAKgD,SAAS,EAAC,YAAY;UAAAL,QAAA,gBACvB3C,OAAA;YAAKgD,SAAS,EAAC,iBAAiB;YAAAL,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzC/C,OAAA;YAAA2C,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B/C,OAAA;YAAA2C,QAAA,EAAG;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxC/C,OAAA;YACIgD,SAAS,EAAC,uBAAuB;YACjCC,OAAO,EAAEA,CAAA,KAAMX,MAAM,CAACY,OAAO,CAACC,IAAI,CAAC,CAAE;YAAAR,QAAA,EACxC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,gBAEN/C,OAAA;UAAKgD,SAAS,EAAC,cAAc;UAAAL,QAAA,gBACzB3C,OAAA;YAAKgD,SAAS,EAAC,YAAY;YAAAL,QAAA,eACvB3C,OAAA;cAAKgD,SAAS,EAAC,iBAAiB;cAAAL,QAAA,EAC/BtC,SAAS,CAAC+C,GAAG,CAAClC,IAAI,iBACflB,OAAA;gBAAmBgD,SAAS,EAAC,WAAW;gBAAAL,QAAA,gBACpC3C,OAAA;kBAAKgD,SAAS,EAAC,YAAY;kBAAAL,QAAA,eACvB3C,OAAA;oBACIqD,GAAG,EAAEnC,IAAI,CAACC,OAAO,CAACmC,KAAM;oBACxBC,GAAG,EAAErC,IAAI,CAACC,OAAO,CAACqC,IAAK;oBACvBC,OAAO,EAAGC,CAAC,IAAK;sBACZA,CAAC,CAACC,MAAM,CAACN,GAAG,GAAG,mDAAmD;oBACtE;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN/C,OAAA;kBAAKgD,SAAS,EAAC,cAAc;kBAAAL,QAAA,gBACzB3C,OAAA;oBAAA2C,QAAA,EAAKzB,IAAI,CAACC,OAAO,CAACqC;kBAAI;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5B/C,OAAA;oBAAA2C,QAAA,EAAIzB,IAAI,CAACC,OAAO,CAACyC;kBAAW;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjC/C,OAAA;oBAAKgD,SAAS,EAAC,YAAY;oBAAAL,QAAA,GAAC,QAAC,EAACzB,IAAI,CAACC,OAAO,CAACC,KAAK;kBAAA;oBAAAwB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACN/C,OAAA;kBAAKgD,SAAS,EAAC,eAAe;kBAAAL,QAAA,gBAC1B3C,OAAA;oBAAKgD,SAAS,EAAC,mBAAmB;oBAAAL,QAAA,gBAC9B3C,OAAA;sBACIiD,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAACV,IAAI,CAACC,OAAO,CAAC0C,EAAE,EAAE3C,IAAI,CAACG,QAAQ,GAAG,CAAC,CAAE;sBAClE2B,SAAS,EAAC,wBAAwB;sBAClCc,KAAK,EAAC,YAAY;sBAAAnB,QAAA,EACrB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT/C,OAAA;sBAAMgD,SAAS,EAAC,UAAU;sBAAAL,QAAA,EAAEzB,IAAI,CAACG;oBAAQ;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACjD/C,OAAA;sBACIiD,OAAO,EAAEA,CAAA,KAAMrB,cAAc,CAACV,IAAI,CAACC,OAAO,CAAC0C,EAAE,EAAE3C,IAAI,CAACG,QAAQ,GAAG,CAAC,CAAE;sBAClE2B,SAAS,EAAC,cAAc;sBACxBc,KAAK,EAAC,cAAc;sBAAAnB,QAAA,EACvB;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CAAC,eACN/C,OAAA;oBAAKgD,SAAS,EAAC,YAAY;oBAAAL,QAAA,GAAC,QAAC,EAACzB,IAAI,CAACC,OAAO,CAACC,KAAK,GAAGF,IAAI,CAACG,QAAQ;kBAAA;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvE/C,OAAA;oBACIiD,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAACb,IAAI,CAACC,OAAO,CAAC0C,EAAE,CAAE;oBAC/Cb,SAAS,EAAC,YAAY;oBACtBc,KAAK,EAAC,kBAAkB;oBAAAnB,QAAA,EAC3B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA,GAzCA7B,IAAI,CAAC2C,EAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0CZ,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN/C,OAAA;YAAKgD,SAAS,EAAC,cAAc;YAAAL,QAAA,eACzB3C,OAAA;cAAKgD,SAAS,EAAC,cAAc;cAAAL,QAAA,gBACzB3C,OAAA;gBAAA2C,QAAA,EAAI;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtB/C,OAAA;gBAAKgD,SAAS,EAAC,aAAa;gBAAAL,QAAA,gBACxB3C,OAAA;kBAAA2C,QAAA,GAAM,SAAO,EAACtC,SAAS,CAACoC,MAAM,EAAC,GAAC;gBAAA;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvC/C,OAAA;kBAAA2C,QAAA,GAAM,QAAC,EAACpC,KAAK;gBAAA;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACN/C,OAAA;gBAAKgD,SAAS,EAAC,aAAa;gBAAAL,QAAA,gBACxB3C,OAAA;kBAAA2C,QAAA,EAAM;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACrB/C,OAAA;kBAAA2C,QAAA,EAAM;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACN/C,OAAA;gBAAKgD,SAAS,EAAC,uBAAuB;gBAAAL,QAAA,gBAClC3C,OAAA;kBAAA2C,QAAA,EAAM;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClB/C,OAAA;kBAAA2C,QAAA,GAAM,QAAC,EAACpC,KAAK;gBAAA;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACN/C,OAAA;gBAAKgD,SAAS,EAAC,cAAc;gBAAAL,QAAA,gBACzB3C,OAAA;kBACIgD,SAAS,EAAC,cAAc;kBACxBC,OAAO,EAAET,iBAAkB;kBAAAG,QAAA,EAC9B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/C,OAAA;kBACIgD,SAAS,EAAC,gBAAgB;kBAC1BC,OAAO,EAAEb,SAAU;kBAAAO,QAAA,EACtB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACR,CAAC;AAEX;AAAC3C,EAAA,CAjPuBD,IAAI;AAAA4D,EAAA,GAAJ5D,IAAI;AAAA,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}