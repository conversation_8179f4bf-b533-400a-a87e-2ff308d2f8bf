{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\Navbar\\\\CustomerNavbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './CustomerNavbar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function CustomerNavbar() {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [cartCount, setCartCount] = useState(0);\n  const navigate = useNavigate();\n  useEffect(() => {\n    // Update cart count when component mounts and when localStorage changes\n    const updateCartCount = () => {\n      const cart = localStorage.getItem('cart');\n      if (cart) {\n        const cartItems = JSON.parse(cart);\n        const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);\n        setCartCount(totalItems);\n      } else {\n        setCartCount(0);\n      }\n    };\n    updateCartCount();\n\n    // Listen for storage changes (when cart is updated from other components)\n    window.addEventListener('storage', updateCartCount);\n\n    // Custom event for same-page cart updates\n    window.addEventListener('cartUpdated', updateCartCount);\n    return () => {\n      window.removeEventListener('storage', updateCartCount);\n      window.removeEventListener('cartUpdated', updateCartCount);\n    };\n  }, []);\n  const handleLogout = () => {\n    const confirmed = window.confirm(\"Are you sure you want to logout?\");\n    if (confirmed) {\n      localStorage.removeItem('user');\n      localStorage.removeItem('cart');\n      sessionStorage.clear();\n      navigate('/');\n    }\n  };\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"customer-navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-logo\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: [\"\\uD83D\\uDED2 \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"logo-text\",\n          children: \"Sales Savvy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 26\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"user-badge\",\n        children: \"Customer\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `navbar-menu ${isMenuOpen ? 'active' : ''}`,\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"navbar-links\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-item\",\n            onClick: () => navigate('/CustomerDashboard'),\n            children: \"\\uD83C\\uDFE0 Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-item cart-item\",\n            onClick: () => navigate('/cart'),\n            children: [\"\\uD83D\\uDED2 Cart (\", cartCount, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"nav-item\",\n            children: \"\\uD83D\\uDCE6 My Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"logout-btn\",\n            onClick: handleLogout,\n            children: \"\\uD83D\\uDEAA Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-toggle\",\n      onClick: toggleMenu,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 9\n  }, this);\n}\n_s(CustomerNavbar, \"Sav86P+VcZZ+WSIiN1aGBu7F9z8=\", false, function () {\n  return [useNavigate];\n});\n_c = CustomerNavbar;\nvar _c;\n$RefreshReg$(_c, \"CustomerNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "jsxDEV", "_jsxDEV", "CustomerNavbar", "_s", "isMenuOpen", "setIsMenuOpen", "cartCount", "setCartCount", "navigate", "updateCartCount", "cart", "localStorage", "getItem", "cartItems", "JSON", "parse", "totalItems", "reduce", "sum", "item", "quantity", "window", "addEventListener", "removeEventListener", "handleLogout", "confirmed", "confirm", "removeItem", "sessionStorage", "clear", "toggleMenu", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/Navbar/CustomerNavbar.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport './CustomerNavbar.css';\n\nexport default function CustomerNavbar() {\n    const [isMenuOpen, setIsMenuOpen] = useState(false);\n    const [cartCount, setCartCount] = useState(0);\n    const navigate = useNavigate();\n\n    useEffect(() => {\n        // Update cart count when component mounts and when localStorage changes\n        const updateCartCount = () => {\n            const cart = localStorage.getItem('cart');\n            if (cart) {\n                const cartItems = JSON.parse(cart);\n                const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);\n                setCartCount(totalItems);\n            } else {\n                setCartCount(0);\n            }\n        };\n\n        updateCartCount();\n\n        // Listen for storage changes (when cart is updated from other components)\n        window.addEventListener('storage', updateCartCount);\n\n        // Custom event for same-page cart updates\n        window.addEventListener('cartUpdated', updateCartCount);\n\n        return () => {\n            window.removeEventListener('storage', updateCartCount);\n            window.removeEventListener('cartUpdated', updateCartCount);\n        };\n    }, []);\n\n    const handleLogout = () => {\n        const confirmed = window.confirm(\"Are you sure you want to logout?\");\n        if (confirmed) {\n            localStorage.removeItem('user');\n            localStorage.removeItem('cart');\n            sessionStorage.clear();\n            navigate('/');\n        }\n    };\n\n    const toggleMenu = () => {\n        setIsMenuOpen(!isMenuOpen);\n    };\n\n    return (\n        <nav className=\"customer-navbar\">\n            <div className=\"navbar-logo\">\n                <span>🛒 <span className=\"logo-text\">Sales Savvy</span></span>\n                <span className=\"user-badge\">Customer</span>\n            </div>\n            \n            <div className={`navbar-menu ${isMenuOpen ? 'active' : ''}`}>\n                <ul className=\"navbar-links\">\n                    <li>\n                        <span\n                            className=\"nav-item\"\n                            onClick={() => navigate('/CustomerDashboard')}\n                        >\n                            🏠 Home\n                        </span>\n                    </li>\n                    <li>\n                        <span\n                            className=\"nav-item cart-item\"\n                            onClick={() => navigate('/cart')}\n                        >\n                            🛒 Cart ({cartCount})\n                        </span>\n                    </li>\n                    <li>\n                        <span className=\"nav-item\">\n                            📦 My Orders\n                        </span>\n                    </li>\n                    <li>\n                        <button \n                            className=\"logout-btn\"\n                            onClick={handleLogout}\n                        >\n                            🚪 Logout\n                        </button>\n                    </li>\n                </ul>\n            </div>\n\n            <div className=\"navbar-toggle\" onClick={toggleMenu}>\n                <span></span>\n                <span></span>\n                <span></span>\n            </div>\n        </nav>\n    );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACrC,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAMW,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZ;IACA,MAAMW,eAAe,GAAGA,CAAA,KAAM;MAC1B,MAAMC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MACzC,IAAIF,IAAI,EAAE;QACN,MAAMG,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACL,IAAI,CAAC;QAClC,MAAMM,UAAU,GAAGH,SAAS,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC;QAC1Eb,YAAY,CAACS,UAAU,CAAC;MAC5B,CAAC,MAAM;QACHT,YAAY,CAAC,CAAC,CAAC;MACnB;IACJ,CAAC;IAEDE,eAAe,CAAC,CAAC;;IAEjB;IACAY,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEb,eAAe,CAAC;;IAEnD;IACAY,MAAM,CAACC,gBAAgB,CAAC,aAAa,EAAEb,eAAe,CAAC;IAEvD,OAAO,MAAM;MACTY,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEd,eAAe,CAAC;MACtDY,MAAM,CAACE,mBAAmB,CAAC,aAAa,EAAEd,eAAe,CAAC;IAC9D,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMe,YAAY,GAAGA,CAAA,KAAM;IACvB,MAAMC,SAAS,GAAGJ,MAAM,CAACK,OAAO,CAAC,kCAAkC,CAAC;IACpE,IAAID,SAAS,EAAE;MACXd,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;MAC/BhB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;MAC/BC,cAAc,CAACC,KAAK,CAAC,CAAC;MACtBrB,QAAQ,CAAC,GAAG,CAAC;IACjB;EACJ,CAAC;EAED,MAAMsB,UAAU,GAAGA,CAAA,KAAM;IACrBzB,aAAa,CAAC,CAACD,UAAU,CAAC;EAC9B,CAAC;EAED,oBACIH,OAAA;IAAK8B,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC5B/B,OAAA;MAAK8B,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxB/B,OAAA;QAAA+B,QAAA,GAAM,eAAG,eAAA/B,OAAA;UAAM8B,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9DnC,OAAA;QAAM8B,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eAENnC,OAAA;MAAK8B,SAAS,EAAE,eAAe3B,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAA4B,QAAA,eACxD/B,OAAA;QAAI8B,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACxB/B,OAAA;UAAA+B,QAAA,eACI/B,OAAA;YACI8B,SAAS,EAAC,UAAU;YACpBM,OAAO,EAAEA,CAAA,KAAM7B,QAAQ,CAAC,oBAAoB,CAAE;YAAAwB,QAAA,EACjD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLnC,OAAA;UAAA+B,QAAA,eACI/B,OAAA;YACI8B,SAAS,EAAC,oBAAoB;YAC9BM,OAAO,EAAEA,CAAA,KAAM7B,QAAQ,CAAC,OAAO,CAAE;YAAAwB,QAAA,GACpC,qBACY,EAAC1B,SAAS,EAAC,GACxB;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLnC,OAAA;UAAA+B,QAAA,eACI/B,OAAA;YAAM8B,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAE3B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACLnC,OAAA;UAAA+B,QAAA,eACI/B,OAAA;YACI8B,SAAS,EAAC,YAAY;YACtBM,OAAO,EAAEb,YAAa;YAAAQ,QAAA,EACzB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENnC,OAAA;MAAK8B,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEP,UAAW;MAAAE,QAAA,gBAC/C/B,OAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnC,OAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnC,OAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACjC,EAAA,CA9FuBD,cAAc;EAAA,QAGjBH,WAAW;AAAA;AAAAuC,EAAA,GAHRpC,cAAc;AAAA,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}