/* Full Screen Cart Styles */
.cart-container {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: calc(100vh - 80px);
    padding: 0;
    margin: 0;
    width: 100%;
}

.cart-header {
    background: white;
    padding: 40px 0;
    text-align: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 0;
}

.cart-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
}

.cart-header h1 {
    color: #2c3e50;
    font-size: 3rem;
    margin-bottom: 15px;
    font-weight: 700;
    background: linear-gradient(135deg, #28a745, #20c997);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cart-header p {
    color: #6c757d;
    font-size: 1.2rem;
    margin: 0;
    font-weight: 400;
}

.cart-main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px;
}

/* Empty Cart */
.empty-cart {
    text-align: center;
    padding: 80px 20px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    max-width: 500px;
    margin: 0 auto;
}

.empty-cart-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-cart h3 {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.empty-cart p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 30px;
}

.continue-shopping-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.continue-shopping-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Cart Content */
.cart-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 30px;
}

.cart-items {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.cart-item {
    display: flex;
    gap: 20px;
    padding: 20px 0;
    border-bottom: 1px solid #e9ecef;
    align-items: center;
}

.cart-item:last-child {
    border-bottom: none;
}

.item-image {
    flex-shrink: 0;
}

.item-image img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 12px;
    border: 2px solid #e9ecef;
}

.item-details {
    flex: 1;
}

.item-details h3 {
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
}

.item-details p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 10px;
    line-height: 1.4;
}

.item-price {
    color: #28a745;
    font-size: 1.1rem;
    font-weight: 600;
}

.item-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 5px;
}

.quantity-btn {
    width: 35px;
    height: 35px;
    border: none;
    background: #28a745;
    color: white;
    border-radius: 6px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    background: #218838;
    transform: scale(1.1);
}

.quantity {
    min-width: 40px;
    text-align: center;
    font-weight: 600;
    font-size: 1.1rem;
}

.item-total {
    color: #333;
    font-size: 1.2rem;
    font-weight: 700;
}

.remove-btn {
    background: #dc3545;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* Cart Summary */
.cart-summary {
    position: sticky;
    top: 100px;
    height: fit-content;
}

.summary-card {
    background: white;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.summary-card h3 {
    color: #333;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f1f1;
}

.summary-row:last-of-type {
    border-bottom: none;
}

.total-row {
    font-size: 1.2rem;
    font-weight: 700;
    color: #333;
    border-top: 2px solid #e9ecef;
    margin-top: 15px;
    padding-top: 15px;
}

.cart-actions {
    margin-top: 25px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.checkout-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.checkout-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.clear-cart-btn {
    background: #f8f9fa;
    color: #dc3545;
    border: 2px solid #dc3545;
    padding: 12px 25px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-cart-btn:hover {
    background: #dc3545;
    color: white;
    transform: translateY(-2px);
}
.minus-btn :hover {
    background: #e9ecef;
    color: #ca0d0d;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .cart-main-content {
        padding: 30px;
    }

    .cart-header-content {
        padding: 0 30px;
    }
}

@media (max-width: 768px) {
    .cart-header {
        padding: 30px 0;
    }

    .cart-header-content {
        padding: 0 20px;
    }

    .cart-header h1 {
        font-size: 2.2rem;
    }

    .cart-header p {
        font-size: 1rem;
    }

    .cart-main-content {
        padding: 20px;
    }

    .cart-content {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .cart-item {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 25px 0;
    }

    .item-controls {
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }

    .cart-summary {
        position: static;
    }

    .cart-items,
    .summary-card {
        padding: 25px;
    }

    .summary-card {
        padding: 20px 15px;
    }
    .minus-btn :hover {
    background: #e9ecef;
    color: #ca0d0d;
}
}

@media (max-width: 480px) {
    .cart-items,
    .summary-card {
        padding: 20px 15px;
    }
    
    .item-image img {
        width: 80px;
        height: 80px;
    }
    
    .item-details h3 {
        font-size: 1.1rem;
    }
    
    .quantity-controls {
        gap: 8px;
    }
    
    .quantity-btn {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }
}
