[{"C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\Home.jsx": "3", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signup\\Signup.jsx": "4", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\login.jsx": "5", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\Login.jsx": "6", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signUp\\SignUp.jsx": "7", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\CustomerDashboard.jsx": "8", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\AdminDashboard.jsx": "9", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\UpdateProduct.jsx": "10", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AddProducts.jsx": "11", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AllProducts.jsx": "12", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\DeleteProduct.jsx": "13", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\Navbar.jsx": "14", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\shared\\ProductsList.jsx": "15", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\CustomerNavbar.jsx": "16", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Customer\\Cart.jsx": "17"}, {"size": 254, "mtime": 1751453845567, "results": "18", "hashOfConfig": "19"}, {"size": 1376, "mtime": 1751455621839, "results": "20", "hashOfConfig": "19"}, {"size": 608, "mtime": 1751287581571, "results": "21", "hashOfConfig": "19"}, {"size": 298, "mtime": 1751289767648, "results": "22", "hashOfConfig": "19"}, {"size": 393, "mtime": 1751288485756, "results": "23", "hashOfConfig": "19"}, {"size": 1592, "mtime": 1751372308140, "results": "24", "hashOfConfig": "19"}, {"size": 2336, "mtime": 1751369405780, "results": "25", "hashOfConfig": "19"}, {"size": 315, "mtime": 1751455376637, "results": "26", "hashOfConfig": "19"}, {"size": 2958, "mtime": 1751453998651, "results": "27", "hashOfConfig": "19"}, {"size": 2798, "mtime": 1751451728076, "results": "28", "hashOfConfig": "19"}, {"size": 1823, "mtime": 1751452955057, "results": "29", "hashOfConfig": "19"}, {"size": 303, "mtime": 1751454442597, "results": "30", "hashOfConfig": "19"}, {"size": 1022, "mtime": 1751452666358, "results": "31", "hashOfConfig": "19"}, {"size": 2862, "mtime": 1751453213792, "results": "32", "hashOfConfig": "19"}, {"size": 11208, "mtime": 1751455578061, "results": "33", "hashOfConfig": "19"}, {"size": 3375, "mtime": 1751455555551, "results": "34", "hashOfConfig": "19"}, {"size": 7350, "mtime": 1751455412383, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7g2f42", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\Home.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signup\\Signup.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\login.jsx", ["87"], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\Login.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signUp\\SignUp.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\CustomerDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\AdminDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\UpdateProduct.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AddProducts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AllProducts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\DeleteProduct.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\shared\\ProductsList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\CustomerNavbar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Customer\\Cart.jsx", [], [], {"ruleId": "88", "severity": 1, "message": "89", "line": 1, "column": 17, "nodeType": "90", "messageId": "91", "endLine": 1, "endColumn": 25}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar"]