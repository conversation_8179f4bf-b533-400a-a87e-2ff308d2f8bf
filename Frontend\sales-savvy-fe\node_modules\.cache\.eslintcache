[{"C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\Home.jsx": "3", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signup\\Signup.jsx": "4", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\login.jsx": "5", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\Login.jsx": "6", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signUp\\SignUp.jsx": "7", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\CustomerDashboard.jsx": "8", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\UpdateProduct.jsx": "9", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AddProducts.jsx": "10", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AllProducts.jsx": "11", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\DeleteProduct.jsx": "12", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\Navbar.jsx": "13", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\shared\\ProductsList.jsx": "14", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\CustomerNavbar.jsx": "15", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Customer\\Cart.jsx": "16", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\shared\\CartUtils.js": "17", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\AdminDashboard_new.jsx": "18", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Payment\\Payment.jsx": "19", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Customer\\MyOrders.jsx": "20"}, {"size": 254, "mtime": 1751453845567, "results": "21", "hashOfConfig": "22"}, {"size": 1490, "mtime": 1751548797993, "results": "23", "hashOfConfig": "22"}, {"size": 6034, "mtime": 1751542016633, "results": "24", "hashOfConfig": "22"}, {"size": 298, "mtime": 1751289767648, "results": "25", "hashOfConfig": "22"}, {"size": 393, "mtime": 1751288485756, "results": "26", "hashOfConfig": "22"}, {"size": 2844, "mtime": 1751542101155, "results": "27", "hashOfConfig": "22"}, {"size": 4588, "mtime": 1751542221090, "results": "28", "hashOfConfig": "22"}, {"size": 315, "mtime": 1751455376637, "results": "29", "hashOfConfig": "22"}, {"size": 2798, "mtime": 1751451728076, "results": "30", "hashOfConfig": "22"}, {"size": 6622, "mtime": 1751542527404, "results": "31", "hashOfConfig": "22"}, {"size": 303, "mtime": 1751454442597, "results": "32", "hashOfConfig": "22"}, {"size": 1022, "mtime": 1751452666358, "results": "33", "hashOfConfig": "22"}, {"size": 3183, "mtime": 1751537045556, "results": "34", "hashOfConfig": "22"}, {"size": 12647, "mtime": 1751543338011, "results": "35", "hashOfConfig": "22"}, {"size": 9262, "mtime": 1751548869986, "results": "36", "hashOfConfig": "22"}, {"size": 11980, "mtime": 1751548746980, "results": "37", "hashOfConfig": "22"}, {"size": 1145, "mtime": 1751459293173, "results": "38", "hashOfConfig": "22"}, {"size": 11989, "mtime": 1751542416378, "results": "39", "hashOfConfig": "22"}, {"size": 7704, "mtime": 1751548727923, "results": "40", "hashOfConfig": "22"}, {"size": 16518, "mtime": 1751548601789, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7g2f42", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\Home.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signup\\Signup.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\login.jsx", ["102"], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\Login.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signUp\\SignUp.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\CustomerDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\UpdateProduct.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AddProducts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AllProducts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\DeleteProduct.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\shared\\ProductsList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\CustomerNavbar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Customer\\Cart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\shared\\CartUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\AdminDashboard_new.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Payment\\Payment.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Customer\\MyOrders.jsx", [], [], {"ruleId": "103", "severity": 1, "message": "104", "line": 1, "column": 17, "nodeType": "105", "messageId": "106", "endLine": 1, "endColumn": 25}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar"]