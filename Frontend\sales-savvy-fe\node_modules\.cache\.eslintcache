[{"C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\Home.jsx": "3", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signup\\Signup.jsx": "4", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\login.jsx": "5", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\Login.jsx": "6", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signUp\\SignUp.jsx": "7", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\CustomerDashboard.jsx": "8", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\UpdateProduct.jsx": "9", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AddProducts.jsx": "10", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AllProducts.jsx": "11", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\DeleteProduct.jsx": "12", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\Navbar.jsx": "13", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\shared\\ProductsList.jsx": "14", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\CustomerNavbar.jsx": "15", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Customer\\Cart.jsx": "16", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\shared\\CartUtils.js": "17", "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\AdminDashboard_new.jsx": "18"}, {"size": 254, "mtime": 1751453845567, "results": "19", "hashOfConfig": "20"}, {"size": 1380, "mtime": 1751460639827, "results": "21", "hashOfConfig": "20"}, {"size": 608, "mtime": 1751287581571, "results": "22", "hashOfConfig": "20"}, {"size": 298, "mtime": 1751289767648, "results": "23", "hashOfConfig": "20"}, {"size": 393, "mtime": 1751288485756, "results": "24", "hashOfConfig": "20"}, {"size": 1645, "mtime": 1751457138044, "results": "25", "hashOfConfig": "20"}, {"size": 2336, "mtime": 1751369405780, "results": "26", "hashOfConfig": "20"}, {"size": 315, "mtime": 1751455376637, "results": "27", "hashOfConfig": "20"}, {"size": 2798, "mtime": 1751451728076, "results": "28", "hashOfConfig": "20"}, {"size": 1823, "mtime": 1751452955057, "results": "29", "hashOfConfig": "20"}, {"size": 303, "mtime": 1751454442597, "results": "30", "hashOfConfig": "20"}, {"size": 1022, "mtime": 1751452666358, "results": "31", "hashOfConfig": "20"}, {"size": 2862, "mtime": 1751453213792, "results": "32", "hashOfConfig": "20"}, {"size": 11012, "mtime": 1751459328724, "results": "33", "hashOfConfig": "20"}, {"size": 3357, "mtime": 1751459256619, "results": "34", "hashOfConfig": "20"}, {"size": 9679, "mtime": 1751459404968, "results": "35", "hashOfConfig": "20"}, {"size": 1145, "mtime": 1751459293173, "results": "36", "hashOfConfig": "20"}, {"size": 11954, "mtime": 1751460620800, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7g2f42", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\Home.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signup\\Signup.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\login.jsx", ["92"], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\login\\Login.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\signUp\\SignUp.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\CustomerDashboard.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\UpdateProduct.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AddProducts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\AllProducts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\AdminPages\\DeleteProduct.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\shared\\ProductsList.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Navbar\\CustomerNavbar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\Customer\\Cart.jsx", ["93"], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\shared\\CartUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\SpringBoot\\sales.savvy\\Frontend\\sales-savvy-fe\\src\\Components\\homePage\\AdminDashboard_new.jsx", [], [], {"ruleId": "94", "severity": 1, "message": "95", "line": 1, "column": 17, "nodeType": "96", "messageId": "97", "endLine": 1, "endColumn": 25}, {"ruleId": "98", "severity": 1, "message": "99", "line": 14, "column": 8, "nodeType": "100", "endLine": 14, "endColumn": 10, "suggestions": "101"}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCartItems'. Either include it or remove the dependency array.", "ArrayExpression", ["102"], {"desc": "103", "fix": "104"}, "Update the dependencies array to be: [fetchCartItems]", {"range": "105", "text": "106"}, [496, 498], "[fetchCartItems]"]