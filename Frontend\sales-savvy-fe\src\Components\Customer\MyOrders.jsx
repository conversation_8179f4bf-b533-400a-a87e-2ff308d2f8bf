import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import CustomerNavbar from '../Navbar/CustomerNavbar';
import { getUsername, handleApiError, showSuccessMessage } from '../shared/CartUtils';
import './MyOrders.css';

export default function MyOrders() {
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [showOrderDetails, setShowOrderDetails] = useState(false);
    const [filterStatus, setFilterStatus] = useState('ALL');
    const [orderStats, setOrderStats] = useState(null);

    const fetchOrders = useCallback(async () => {
        const username = getUsername();
        if (!username) {
            setLoading(false);
            return;
        }

        try {
            const response = await axios.get(`http://localhost:8080/api/orders/user/${username}`);
            setOrders(response.data || []);
            setLoading(false);
        } catch (error) {
            handleApiError(error, 'Failed to load orders');
            setLoading(false);
        }
    }, []);

    const fetchOrderStats = useCallback(async () => {
        const username = getUsername();
        if (!username) return;

        try {
            const response = await axios.get(`http://localhost:8080/api/orders/user/${username}/statistics`);
            setOrderStats(response.data);
        } catch (error) {
            console.error('Failed to load order statistics:', error);
        }
    }, []);

    useEffect(() => {
        fetchOrders();
        fetchOrderStats();
    }, [fetchOrders, fetchOrderStats]);

    const getStatusColor = (status) => {
        switch (status) {
            case 'PENDING': return 'status-pending';
            case 'CONFIRMED': return 'status-confirmed';
            case 'PROCESSING': return 'status-processing';
            case 'SHIPPED': return 'status-shipped';
            case 'DELIVERED': return 'status-delivered';
            case 'CANCELLED': return 'status-cancelled';
            default: return 'status-default';
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'PENDING': return '⏳';
            case 'CONFIRMED': return '✅';
            case 'PROCESSING': return '⚙️';
            case 'SHIPPED': return '🚚';
            case 'DELIVERED': return '📦';
            case 'CANCELLED': return '❌';
            default: return '📋';
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const handleViewDetails = (order) => {
        setSelectedOrder(order);
        setShowOrderDetails(true);
    };

    const handleCancelOrder = async (orderNumber) => {
        const username = getUsername();
        if (!username) return;

        if (window.confirm('Are you sure you want to cancel this order?')) {
            try {
                await axios.put(`http://localhost:8080/api/orders/cancel/${orderNumber}?username=${username}`);
                showSuccessMessage('Order cancelled successfully');
                fetchOrders(); // Refresh orders
            } catch (error) {
                handleApiError(error, 'Failed to cancel order');
            }
        }
    };

    const filteredOrders = orders.filter(order => {
        if (filterStatus === 'ALL') return true;
        return order.status === filterStatus;
    });

    const parseOrderItems = (orderItemsJson) => {
        try {
            return JSON.parse(orderItemsJson);
        } catch (error) {
            return [];
        }
    };

    if (loading) {
        return (
            <>
                <CustomerNavbar />
                <div className="orders-container">
                    <div className="loading-spinner">
                        <div className="spinner"></div>
                        <p>Loading your orders...</p>
                    </div>
                </div>
            </>
        );
    }

    return (
        <>
            <CustomerNavbar />
            <div className="orders-container">
                <div className="orders-header">
                    <div className="header-content">
                        <h1>My Orders</h1>
                        <p>Track and manage your order history</p>
                    </div>
                </div>

                {/* Order Statistics */}
                {orderStats && (
                    <div className="order-stats">
                        <div className="stat-card">
                            <div className="stat-icon">📊</div>
                            <div className="stat-info">
                                <h3>{orderStats.totalOrders}</h3>
                                <p>Total Orders</p>
                            </div>
                        </div>
                        <div className="stat-card">
                            <div className="stat-icon">💰</div>
                            <div className="stat-info">
                                <h3>₹{orderStats.totalSpent?.toFixed(2) || '0.00'}</h3>
                                <p>Total Spent</p>
                            </div>
                        </div>
                        <div className="stat-card">
                            <div className="stat-icon">⏳</div>
                            <div className="stat-info">
                                <h3>{orderStats.pendingOrders}</h3>
                                <p>Pending Orders</p>
                            </div>
                        </div>
                        <div className="stat-card">
                            <div className="stat-icon">✅</div>
                            <div className="stat-info">
                                <h3>{orderStats.completedOrders}</h3>
                                <p>Completed</p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Filter Controls */}
                <div className="filter-controls">
                    <div className="filter-group">
                        <label>Filter by Status:</label>
                        <select 
                            value={filterStatus} 
                            onChange={(e) => setFilterStatus(e.target.value)}
                            className="filter-select"
                        >
                            <option value="ALL">All Orders</option>
                            <option value="PENDING">Pending</option>
                            <option value="CONFIRMED">Confirmed</option>
                            <option value="PROCESSING">Processing</option>
                            <option value="SHIPPED">Shipped</option>
                            <option value="DELIVERED">Delivered</option>
                            <option value="CANCELLED">Cancelled</option>
                        </select>
                    </div>
                </div>

                {/* Orders List */}
                <div className="orders-content">
                    {filteredOrders.length === 0 ? (
                        <div className="empty-orders">
                            <div className="empty-icon">📦</div>
                            <h3>No orders found</h3>
                            <p>
                                {filterStatus === 'ALL' 
                                    ? "You haven't placed any orders yet. Start shopping to see your orders here!"
                                    : `No orders with status "${filterStatus}" found.`
                                }
                            </p>
                        </div>
                    ) : (
                        <div className="orders-grid">
                            {filteredOrders.map((order) => (
                                <div key={order.id} className="order-card">
                                    <div className="order-header">
                                        <div className="order-number">
                                            <span className="label">Order #</span>
                                            <span className="value">{order.orderNumber}</span>
                                        </div>
                                        <div className={`order-status ${getStatusColor(order.status)}`}>
                                            <span className="status-icon">{getStatusIcon(order.status)}</span>
                                            <span className="status-text">{order.status}</span>
                                        </div>
                                    </div>

                                    <div className="order-info">
                                        <div className="info-row">
                                            <span className="label">Date:</span>
                                            <span className="value">{formatDate(order.createdAt)}</span>
                                        </div>
                                        <div className="info-row">
                                            <span className="label">Total:</span>
                                            <span className="value amount">₹{order.totalAmount.toFixed(2)}</span>
                                        </div>
                                        {order.trackingNumber && (
                                            <div className="info-row">
                                                <span className="label">Tracking:</span>
                                                <span className="value tracking">{order.trackingNumber}</span>
                                            </div>
                                        )}
                                    </div>

                                    <div className="order-actions">
                                        <button 
                                            className="btn btn-secondary btn-sm"
                                            onClick={() => handleViewDetails(order)}
                                        >
                                            <span>👁️</span>
                                            View Details
                                        </button>
                                        
                                        {(order.status === 'PENDING' || order.status === 'CONFIRMED') && (
                                            <button 
                                                className="btn btn-danger btn-sm"
                                                onClick={() => handleCancelOrder(order.orderNumber)}
                                            >
                                                <span>❌</span>
                                                Cancel
                                            </button>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                {/* Order Details Modal */}
                {showOrderDetails && selectedOrder && (
                    <div className="modal-overlay">
                        <div className="order-details-modal">
                            <div className="modal-header">
                                <h2>Order Details</h2>
                                <button 
                                    className="close-btn"
                                    onClick={() => setShowOrderDetails(false)}
                                >
                                    ✕
                                </button>
                            </div>
                            
                            <div className="modal-content">
                                <div className="order-summary">
                                    <h3>Order #{selectedOrder.orderNumber}</h3>
                                    <div className={`status-badge ${getStatusColor(selectedOrder.status)}`}>
                                        {getStatusIcon(selectedOrder.status)} {selectedOrder.status}
                                    </div>
                                </div>

                                <div className="order-details-grid">
                                    <div className="detail-section">
                                        <h4>Order Information</h4>
                                        <div className="detail-item">
                                            <span>Order Date:</span>
                                            <span>{formatDate(selectedOrder.createdAt)}</span>
                                        </div>
                                        <div className="detail-item">
                                            <span>Total Amount:</span>
                                            <span>₹{selectedOrder.totalAmount.toFixed(2)}</span>
                                        </div>
                                        {selectedOrder.trackingNumber && (
                                            <div className="detail-item">
                                                <span>Tracking Number:</span>
                                                <span>{selectedOrder.trackingNumber}</span>
                                            </div>
                                        )}
                                        {selectedOrder.estimatedDelivery && (
                                            <div className="detail-item">
                                                <span>Estimated Delivery:</span>
                                                <span>{formatDate(selectedOrder.estimatedDelivery)}</span>
                                            </div>
                                        )}
                                    </div>

                                    {selectedOrder.shippingAddress && (
                                        <div className="detail-section">
                                            <h4>Shipping Address</h4>
                                            <p>{selectedOrder.shippingAddress}</p>
                                            {selectedOrder.phoneNumber && (
                                                <p>Phone: {selectedOrder.phoneNumber}</p>
                                            )}
                                        </div>
                                    )}

                                    <div className="detail-section">
                                        <h4>Order Items</h4>
                                        <div className="order-items">
                                            {parseOrderItems(selectedOrder.orderItems).map((item, index) => (
                                                <div key={index} className="order-item">
                                                    <img 
                                                        src={item.productImage} 
                                                        alt={item.productName}
                                                        className="item-image"
                                                    />
                                                    <div className="item-details">
                                                        <h5>{item.productName}</h5>
                                                        <p>Quantity: {item.quantity}</p>
                                                        <p>Price: ₹{item.price.toFixed(2)}</p>
                                                        <p className="subtotal">Subtotal: ₹{item.subtotal.toFixed(2)}</p>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </>
    );
}
