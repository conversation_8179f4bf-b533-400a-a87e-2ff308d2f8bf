{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SpringBoot\\\\sales.savvy\\\\Frontend\\\\sales-savvy-fe\\\\src\\\\Components\\\\AdminPages\\\\DeleteProduct.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function DeleteProduct() {\n  _s();\n  const {\n    id\n  } = useParams(); // get id from URL\n  const navigate = useNavigate();\n  useEffect(() => {\n    const confirmDelete = window.confirm(\"Are you sure you want to delete this product?\");\n    if (!confirmDelete) {\n      navigate('/all_products'); // Cancel -> go back\n      return;\n    }\n    axios.delete(`http://localhost:8080/deleteProduct`, {\n      params: {\n        id\n      }\n    }).then(() => {\n      alert('Product deleted successfully!');\n      navigate('/all_products');\n    }).catch(err => {\n      console.error('Delete failed:', err);\n      alert('Failed to delete product');\n      navigate('/all_products');\n    });\n  }, [id, navigate]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Deleting product...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 12\n  }, this);\n}\n_s(DeleteProduct, \"jBhyhCnGnTynLG86SR547tPdgyU=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = DeleteProduct;\nvar _c;\n$RefreshReg$(_c, \"DeleteProduct\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useParams", "axios", "jsxDEV", "_jsxDEV", "DeleteProduct", "_s", "id", "navigate", "confirmDelete", "window", "confirm", "delete", "params", "then", "alert", "catch", "err", "console", "error", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SpringBoot/sales.savvy/Frontend/sales-savvy-fe/src/Components/AdminPages/DeleteProduct.jsx"], "sourcesContent": ["import React, { useEffect } from 'react';\r\nimport { useNavigate, useParams } from 'react-router-dom';\r\nimport axios from 'axios';\r\n\r\nexport default function DeleteProduct() {\r\n    const { id } = useParams(); // get id from URL\r\n    const navigate = useNavigate();\r\n\r\n    useEffect(() => {\r\n        const confirmDelete = window.confirm(\"Are you sure you want to delete this product?\");\r\n        if (!confirmDelete) {\r\n            navigate('/all_products'); // Cancel -> go back\r\n            return;\r\n        }\r\n\r\n        axios\r\n            .delete(`http://localhost:8080/deleteProduct`, { params: { id } })\r\n            .then(() => {\r\n                alert('Product deleted successfully!');\r\n                navigate('/all_products');\r\n            })\r\n            .catch(err => {\r\n                console.error('Delete failed:', err);\r\n                alert('Failed to delete product');\r\n                navigate('/all_products');\r\n            });\r\n    }, [id, navigate]);\r\n\r\n    return <div>Deleting product...</div>;\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,eAAe,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAG,CAAC,GAAGN,SAAS,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZ,MAAMU,aAAa,GAAGC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC;IACrF,IAAI,CAACF,aAAa,EAAE;MAChBD,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC;MAC3B;IACJ;IAEAN,KAAK,CACAU,MAAM,CAAC,qCAAqC,EAAE;MAAEC,MAAM,EAAE;QAAEN;MAAG;IAAE,CAAC,CAAC,CACjEO,IAAI,CAAC,MAAM;MACRC,KAAK,CAAC,+BAA+B,CAAC;MACtCP,QAAQ,CAAC,eAAe,CAAC;IAC7B,CAAC,CAAC,CACDQ,KAAK,CAACC,GAAG,IAAI;MACVC,OAAO,CAACC,KAAK,CAAC,gBAAgB,EAAEF,GAAG,CAAC;MACpCF,KAAK,CAAC,0BAA0B,CAAC;MACjCP,QAAQ,CAAC,eAAe,CAAC;IAC7B,CAAC,CAAC;EACV,CAAC,EAAE,CAACD,EAAE,EAAEC,QAAQ,CAAC,CAAC;EAElB,oBAAOJ,OAAA;IAAAgB,QAAA,EAAK;EAAmB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACzC;AAAClB,EAAA,CAzBuBD,aAAa;EAAA,QAClBJ,SAAS,EACPD,WAAW;AAAA;AAAAyB,EAAA,GAFRpB,aAAa;AAAA,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}