package sales.savvy.service;

import sales.savvy.dto.PaymentRequest;
import sales.savvy.dto.PaymentResponse;
import sales.savvy.dto.PaymentVerificationRequest;
import sales.savvy.entity.Payment;

import java.util.List;

public interface PaymentService {
    
    /**
     * Create a new Razorpay order
     */
    PaymentResponse createOrder(PaymentRequest paymentRequest) throws Exception;
    
    /**
     * Verify payment signature and update payment status
     */
    String verifyPayment(PaymentVerificationRequest verificationRequest) throws Exception;
    
    /**
     * Get payment by Razorpay order ID
     */
    Payment getPaymentByOrderId(String orderId);
    
    /**
     * Get payment by Razorpay payment ID
     */
    Payment getPaymentByPaymentId(String paymentId);
    
    /**
     * Get all payments for a user
     */
    List<Payment> getPaymentsByUsername(String username);
    
    /**
     * Get payment history for a user (ordered by date)
     */
    List<Payment> getPaymentHistory(String username);
    
    /**
     * Get payments by status
     */
    List<Payment> getPaymentsByStatus(String status);
    
    /**
     * Update payment status
     */
    Payment updatePaymentStatus(String orderId, String status);
}
