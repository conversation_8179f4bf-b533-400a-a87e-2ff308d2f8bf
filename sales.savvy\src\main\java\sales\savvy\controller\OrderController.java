package sales.savvy.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import sales.savvy.dto.OrderRequest;
import sales.savvy.entity.Order;
import sales.savvy.service.OrderService;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/orders")
@CrossOrigin(origins = "*")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    /**
     * Create a new order after successful payment
     */
    @PostMapping("/create")
    public ResponseEntity<?> createOrder(@RequestBody OrderRequest orderRequest) {
        try {
            // Validate request
            if (orderRequest.getUsername() == null || orderRequest.getUsername().trim().isEmpty()) {
                return ResponseEntity.badRequest().body("Username is required");
            }
            
            if (orderRequest.getTotalAmount() == null || orderRequest.getTotalAmount() <= 0) {
                return ResponseEntity.badRequest().body("Invalid total amount");
            }
            
            if (orderRequest.getPaymentId() == null || orderRequest.getPaymentId().trim().isEmpty()) {
                return ResponseEntity.badRequest().body("Payment ID is required");
            }
            
            Order order = orderService.createOrder(orderRequest);
            return ResponseEntity.ok(order);
            
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Get order by order number
     */
    @GetMapping("/order/{orderNumber}")
    public ResponseEntity<?> getOrderByOrderNumber(@PathVariable String orderNumber) {
        try {
            Order order = orderService.getOrderByOrderNumber(orderNumber);
            if (order != null) {
                return ResponseEntity.ok(order);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Get all orders for a user
     */
    @GetMapping("/user/{username}")
    public ResponseEntity<?> getUserOrders(@PathVariable String username) {
        try {
            List<Order> orders = orderService.getOrderHistory(username);
            return ResponseEntity.ok(orders);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Get orders by status for a user
     */
    @GetMapping("/user/{username}/status/{status}")
    public ResponseEntity<?> getUserOrdersByStatus(@PathVariable String username, @PathVariable String status) {
        try {
            List<Order> orders = orderService.getOrdersByUsernameAndStatus(username, status);
            return ResponseEntity.ok(orders);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Get user order statistics
     */
    @GetMapping("/user/{username}/statistics")
    public ResponseEntity<?> getUserOrderStatistics(@PathVariable String username) {
        try {
            OrderService.OrderStatistics stats = orderService.getUserOrderStatistics(username);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Cancel an order
     */
    @PutMapping("/cancel/{orderNumber}")
    public ResponseEntity<?> cancelOrder(@PathVariable String orderNumber, @RequestParam String username) {
        try {
            Order order = orderService.cancelOrder(orderNumber, username);
            return ResponseEntity.ok(order);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
        }
    }
    
    /**
     * Track order by tracking number
     */
    @GetMapping("/track/{trackingNumber}")
    public ResponseEntity<?> trackOrder(@PathVariable String trackingNumber) {
        try {
            Order order = orderService.getOrderByTrackingNumber(trackingNumber);
            if (order != null) {
                return ResponseEntity.ok(order);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Get orders by date range for a user
     */
    @GetMapping("/user/{username}/date-range")
    public ResponseEntity<?> getUserOrdersByDateRange(
            @PathVariable String username,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        try {
            List<Order> orders = orderService.getUserOrdersByDateRange(username, startDate, endDate);
            return ResponseEntity.ok(orders);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    // Admin endpoints
    
    /**
     * Get all orders by status (Admin only)
     */
    @GetMapping("/admin/status/{status}")
    public ResponseEntity<?> getOrdersByStatus(@PathVariable String status) {
        try {
            List<Order> orders = orderService.getOrdersByStatus(status);
            return ResponseEntity.ok(orders);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Update order status (Admin only)
     */
    @PutMapping("/admin/status/{orderNumber}")
    public ResponseEntity<?> updateOrderStatus(@PathVariable String orderNumber, @RequestParam String status) {
        try {
            Order order = orderService.updateOrderStatus(orderNumber, status);
            if (order != null) {
                return ResponseEntity.ok(order);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Update tracking number (Admin only)
     */
    @PutMapping("/admin/tracking/{orderNumber}")
    public ResponseEntity<?> updateTrackingNumber(@PathVariable String orderNumber, @RequestParam String trackingNumber) {
        try {
            Order order = orderService.updateTrackingNumber(orderNumber, trackingNumber);
            if (order != null) {
                return ResponseEntity.ok(order);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    /**
     * Get overdue orders (Admin only)
     */
    @GetMapping("/admin/overdue")
    public ResponseEntity<?> getOverdueOrders() {
        try {
            List<Order> orders = orderService.getOverdueOrders();
            return ResponseEntity.ok(orders);
        } catch (Exception e) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}
