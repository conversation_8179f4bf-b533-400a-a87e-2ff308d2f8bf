package sales.savvy.service;
import sales.savvy.entity.User;
import sales.savvy.repository.UserRepository;

import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@Service
public class UserServiceImplementation implements UserService {
    @Autowired
    UserRepository repo;

    public void addUser(User user) {
        repo.save(user);
    }
    public User getUser(String username) {
        return repo.findByUsername(username);
    }
    public User CkeckUser(String username, String password) {
        return repo.findByUsernameAndPassword(username, password);
    }
  public List<User> getAllUsers() {
        return repo.findAll();
    }
}
