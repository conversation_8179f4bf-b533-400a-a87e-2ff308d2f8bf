import './App.css';
import CustomerDashboard from './Components/homePage/CustomerDashboard';
import Home from './Components/homePage/Home';
import Login from './Components/login/Login';
import Signup from './Components/signUp/SignUp';
import AdminDashboard from './Components/homePage/AdminDashboard_new';
import AddProducts from './Components/AdminPages/AddProducts';
import AllProducts from './Components/AdminPages/AllProducts';
import UpdateProduct from './Components/AdminPages/UpdateProduct';
import DeleteProduct from './Components/AdminPages/DeleteProduct';
import Cart from './Components/Customer/Cart';
import MyOrders from './Components/Customer/MyOrders';
import {BrowserRouter, Route, Routes} from 'react-router-dom';
function App() {
  return (
    <BrowserRouter>
    <Routes>
      <Route path="/" element={<Home/>}/>
      <Route path="/login" element={<Login/>}/>
      <Route path="/signup" element={<Signup />}/>
      <Route path="/CustomerDashboard" element={<CustomerDashboard />}/>
      <Route path="/AdminDashboard" element={<AdminDashboard />}/>
      <Route path="/AddProducts" element={<AddProducts />}/>
      <Route path="/AllProducts" element={<AllProducts />}/>
      <Route path="/updateproduct" element={<UpdateProduct />}/>
      <Route path="/DeleteProduct" element={<DeleteProduct />}/>
      <Route path="/cart" element={<Cart />}/>
      <Route path="/myorders" element={<MyOrders />}/>
    </Routes>
    </BrowserRouter>
  );
}

export default App;
