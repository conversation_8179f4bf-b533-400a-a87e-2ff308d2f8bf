import React from "react";
import { useNavigate } from "react-router-dom";
import './Home.css';

export default function Home() {
    const navigate = useNavigate();

    return (
        <div className="home-container">
            {/* Hero Section */}
            <div className="hero-section">
                <div className="hero-content">
                    <div className="hero-text">
                        <div className="brand-logo">
                            <span className="logo-icon">🛒</span>
                            <span className="logo-text">Sales Savvy</span>
                        </div>
                        <h1 className="hero-title">
                            Your One-Stop Solution for
                            <span className="gradient-text"> Smart Sales</span>
                        </h1>
                        <p className="hero-description">
                            Streamline your business operations with our comprehensive sales management platform.
                            From inventory management to customer analytics, we've got you covered.
                        </p>

                        <div className="hero-actions">
                            <button
                                className="btn btn-primary btn-lg"
                                onClick={() => navigate("/login")}
                            >
                                <span>🚀</span>
                                Get Started
                            </button>
                            <button
                                className="btn btn-secondary btn-lg"
                                onClick={() => navigate("/signup")}
                            >
                                <span>✨</span>
                                Create Account
                            </button>
                        </div>
                    </div>

                    <div className="hero-visual">
                        <div className="feature-cards">
                            <div className="feature-card">
                                <div className="feature-icon">📊</div>
                                <h3>Analytics</h3>
                                <p>Real-time insights</p>
                            </div>
                            <div className="feature-card">
                                <div className="feature-icon">📦</div>
                                <h3>Inventory</h3>
                                <p>Smart management</p>
                            </div>
                            <div className="feature-card">
                                <div className="feature-icon">💰</div>
                                <h3>Sales</h3>
                                <p>Boost revenue</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Features Section */}
            <div className="features-section">
                <div className="container">
                    <div className="section-header">
                        <h2>Why Choose Sales Savvy?</h2>
                        <p>Powerful features designed to accelerate your business growth</p>
                    </div>

                    <div className="features-grid">
                        <div className="feature-item">
                            <div className="feature-icon-large">🎯</div>
                            <h3>Smart Dashboard</h3>
                            <p>Get comprehensive insights with our intuitive admin dashboard that helps you make data-driven decisions.</p>
                        </div>

                        <div className="feature-item">
                            <div className="feature-icon-large">🛍️</div>
                            <h3>Customer Experience</h3>
                            <p>Provide your customers with a seamless shopping experience through our user-friendly interface.</p>
                        </div>

                        <div className="feature-item">
                            <div className="feature-icon-large">📈</div>
                            <h3>Growth Analytics</h3>
                            <p>Track your business performance with detailed analytics and reporting tools.</p>
                        </div>

                        <div className="feature-item">
                            <div className="feature-icon-large">🔒</div>
                            <h3>Secure Platform</h3>
                            <p>Your data is protected with enterprise-grade security and reliable infrastructure.</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* CTA Section */}
            <div className="cta-section">
                <div className="container">
                    <div className="cta-content">
                        <h2>Ready to Transform Your Business?</h2>
                        <p>Join thousands of businesses already using Sales Savvy to boost their sales and streamline operations.</p>
                        <div className="cta-actions">
                            <button
                                className="btn btn-primary btn-lg"
                                onClick={() => navigate("/signup")}
                            >
                                Start Free Trial
                            </button>
                            <button
                                className="btn btn-secondary btn-lg"
                                onClick={() => navigate("/login")}
                            >
                                Sign In
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}